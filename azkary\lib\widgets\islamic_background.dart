import 'package:flutter/material.dart';
import 'islamic_pattern.dart';

/// ويدجت خلفية إسلامية جميلة مع تدرجات لونية ناعمة
class IslamicBackground extends StatelessWidget {
  final Widget child;
  final Color? primaryColor;
  final Color? secondaryColor;
  final double opacity;
  final bool showPattern;
  final bool showGradient;

  const IslamicBackground({
    super.key,
    required this.child,
    this.primaryColor,
    this.secondaryColor,
    this.opacity = 0.05,
    this.showPattern = true,
    this.showGradient = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // تحديد الألوان بناءً على السمة
    final Color mainColor = primaryColor ?? theme.colorScheme.primary;

    return Stack(
      children: [
        // الخلفية الأساسية
        Container(
          decoration: BoxDecoration(color: theme.scaffoldBackgroundColor),
        ),

        // التدرج اللوني المبسط - محسن للأداء
        if (showGradient)
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: _createOptimizedGradient(mainColor, isDarkMode),
              ),
            ),
          ),

        // النمط الإسلامي المبسط - محسن جذرياً للأداء
        if (showPattern)
          Positioned.fill(
            child: RepaintBoundary(
              // إضافة RepaintBoundary للأداء
              child: Opacity(
                opacity:
                    isDarkMode
                        ? opacity *
                            0.2 // تقليل أكثر
                        : opacity * 0.3, // تقليل أكثر للأداء
                child: IslamicPattern(
                  color: mainColor,
                  opacity: isDarkMode ? 0.3 : 0.4, // تقليل الكثافة أكثر
                ),
              ),
            ),
          ),

        // المحتوى
        child,
      ],
    );
  }

  /// إنشاء تدرج لوني محسن للأداء
  LinearGradient _createOptimizedGradient(Color primaryColor, bool isDarkMode) {
    if (isDarkMode) {
      return LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        stops: const [0.0, 1.0], // تبسيط التدرج
        colors: [primaryColor.withAlpha(15), Colors.transparent],
      );
    } else {
      return LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        stops: const [0.0, 1.0], // تبسيط التدرج
        colors: [primaryColor.withAlpha(20), Colors.transparent],
      );
    }
  }
}

/// ويدجت خلفية بطاقة محسنة
class EnhancedCardBackground extends StatelessWidget {
  final Widget child;
  final Color? backgroundColor;
  final double borderRadius;
  final bool showGradient;
  final bool showShadow;
  final EdgeInsets? padding;
  final EdgeInsets? margin;

  const EnhancedCardBackground({
    super.key,
    required this.child,
    this.backgroundColor,
    this.borderRadius = 20.0,
    this.showGradient = true,
    this.showShadow = true,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      margin: margin ?? const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow:
            showShadow
                ? [
                  BoxShadow(
                    color:
                        isDarkMode
                            ? Colors.black.withAlpha(60)
                            : Colors.black.withAlpha(25),
                    spreadRadius: 0,
                    blurRadius: isDarkMode ? 8 : 12,
                    offset: const Offset(0, 4),
                  ),
                  if (!isDarkMode)
                    BoxShadow(
                      color: Colors.white.withAlpha(180),
                      spreadRadius: 0,
                      blurRadius: 6,
                      offset: const Offset(0, -2),
                    ),
                ]
                : null,
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(borderRadius),
          color: backgroundColor ?? theme.cardColor,
          gradient:
              showGradient ? _createCardGradient(theme, isDarkMode) : null,
          border: Border.all(
            color:
                isDarkMode
                    ? Colors.white.withAlpha(20)
                    : Colors.grey.withAlpha(30),
            width: 0.5,
          ),
        ),
        padding: padding ?? const EdgeInsets.all(16),
        child: child,
      ),
    );
  }

  /// إنشاء تدرج لوني للبطاقة
  LinearGradient? _createCardGradient(ThemeData theme, bool isDarkMode) {
    if (isDarkMode) {
      return LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          theme.cardColor,
          theme.cardColor.withAlpha(240),
          theme.cardColor,
        ],
      );
    } else {
      return LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          const Color(0xFFFDFDFD),
          const Color(0xFFFBFBFB),
          const Color(0xFFF9F9F9),
        ],
      );
    }
  }
}

/// ويدجت خلفية متحركة للصفحات الخاصة
class AnimatedIslamicBackground extends StatefulWidget {
  final Widget child;
  final Duration animationDuration;
  final Color? primaryColor;

  const AnimatedIslamicBackground({
    super.key,
    required this.child,
    this.animationDuration = const Duration(seconds: 20),
    this.primaryColor,
  });

  @override
  State<AnimatedIslamicBackground> createState() =>
      _AnimatedIslamicBackgroundState();
}

class _AnimatedIslamicBackgroundState extends State<AnimatedIslamicBackground>
    with TickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    // إزالة الرسوم المتحركة المستمرة لتحسين الأداء
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // تحسين الأداء: تقليل تكرار الرسوم المتحركة
    return IslamicBackground(
      primaryColor: widget.primaryColor ?? theme.colorScheme.primary,
      opacity: 0.03, // ثابت لتحسين الأداء
      showGradient: false, // إيقاف التدرج المعقد
      child: widget.child,
    );
  }
}

/// ويدجت خلفية محسنة للأداء - بدون رسوم متحركة مستمرة
class OptimizedIslamicBackground extends StatelessWidget {
  final Widget child;
  final Color? primaryColor;
  final double opacity;

  const OptimizedIslamicBackground({
    super.key,
    required this.child,
    this.primaryColor,
    this.opacity = 0.03,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return IslamicBackground(
      primaryColor: primaryColor ?? theme.colorScheme.primary,
      opacity: opacity,
      showGradient: false, // إيقاف التدرج لتحسين الأداء
      showPattern: true,
      child: child,
    );
  }
}
