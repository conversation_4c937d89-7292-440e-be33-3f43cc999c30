import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:async'; // للتعامل مع التأخير في البحث

import '../models/quran_model.dart';
import '../models/search_type.dart';
import '../services/quran_provider.dart';
import '../utils/logger.dart';

import '../widgets/islamic_background.dart';
import '../widgets/shimmer_loading.dart';
import '../widgets/enhanced_animations.dart';
import '../widgets/enhanced_surah_card.dart';
import '../widgets/custom_app_bar.dart';
import 'search_helpers.dart';

/// شاشة عرض القرآن الكريم
class QuranScreen extends StatefulWidget {
  const QuranScreen({super.key});

  @override
  State<QuranScreen> createState() => _QuranScreenState();
}

class _QuranScreenState extends State<QuranScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  bool _isInitialized = false;

  // متغيرات البحث
  final TextEditingController _searchController = TextEditingController();
  Timer? _debounce; // للتأخير في البحث
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // تهيئة مزود بيانات القرآن
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeQuranProvider();
    });
  }

  /// تهيئة مزود بيانات القرآن
  Future<void> _initializeQuranProvider() async {
    final quranProvider = Provider.of<QuranProvider>(context, listen: false);
    if (!_isInitialized) {
      await quranProvider.initialize();
      _isInitialized = true;
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    _searchFocusNode.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  /// بناء شريط التطبيق المحسن
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    final quranProvider = Provider.of<QuranProvider>(context);
    final theme = Theme.of(context);

    // تحديد لون وأيقونة نوع البحث
    final bool isSurahSearch = quranProvider.searchType == SearchType.surahName;
    final Color searchTypeColor = isSurahSearch ? Colors.amber : Colors.green;

    // إذا كان في وضع البحث، عرض شريط بحث مخصص
    if (quranProvider.isSearching) {
      return PreferredSize(
        preferredSize: const Size.fromHeight(kToolbarHeight + 16),
        child: SafeArea(
          child: Container(
            decoration: BoxDecoration(
              color: theme.scaffoldBackgroundColor,
              boxShadow: [
                BoxShadow(
                  color: searchTypeColor.withAlpha(50),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
              borderRadius: const BorderRadius.vertical(
                bottom: Radius.circular(16),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: _buildSearchField(context),
            ),
          ),
        ),
      );
    }

    // الشريط العادي باستخدام CustomAppBar
    return CustomAppBar(
      title: 'القرآن الكريم',
      automaticallyImplyLeading: false,
      actions: [
        // زر البحث المحسن
        Container(
          margin: const EdgeInsets.only(left: 4),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withAlpha(20),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: theme.colorScheme.primary.withAlpha(30),
              width: 1,
            ),
          ),
          child: IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context),
            tooltip: 'بحث في القرآن',
            color: theme.colorScheme.primary,
            iconSize: 22,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    // Removed unused theme variable
    final quranProvider = Provider.of<QuranProvider>(context);

    // تحديد لون زر العودة حسب نوع البحث
    final bool isSurahSearch = quranProvider.searchType == SearchType.surahName;
    final Color searchTypeColor = isSurahSearch ? Colors.amber : Colors.green;

    return Scaffold(
      appBar: _buildAppBar(context),
      // إضافة زر عودة عائم عند البحث
      floatingActionButton:
          quranProvider.isSearching
              ? buildFloatingBackButton(context, searchTypeColor)
              : null,
      floatingActionButtonLocation: FloatingActionButtonLocation.startFloat,
      body: IslamicBackground(
        opacity: 0.03,
        showPattern: true,
        showGradient: false,
        child: Column(
          children: [
            // محتوى رئيسي
            Expanded(
              child: Consumer<QuranProvider>(
                builder: (context, quranProvider, child) {
                  if (quranProvider.isLoading) {
                    return _buildLoadingState();
                  }

                  if (quranProvider.error.isNotEmpty) {
                    return _buildErrorState(quranProvider.error);
                  }

                  if (quranProvider.surahs.isEmpty) {
                    return _buildEmptyState();
                  }

                  // عرض نتائج البحث في الآيات إذا كان البحث عن آيات
                  if (quranProvider.isAyahSearch) {
                    AppLogger.info(
                      'عرض نتائج البحث في الآيات: ${quranProvider.searchResults.length} نتيجة',
                    );
                    return buildAyahSearchResults(
                      context,
                      quranProvider.searchResults,
                    );
                  }

                  return _buildSurahsList(quranProvider.surahs);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 10,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: ShimmerLoading(
            isLoading: true,
            child: Container(
              height: 80,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState(String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                final quranProvider = Provider.of<QuranProvider>(
                  context,
                  listen: false,
                );
                quranProvider.loadSurahs();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حالة القائمة الفارغة
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.menu_book,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد سور متاحة',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                final quranProvider = Provider.of<QuranProvider>(
                  context,
                  listen: false,
                );
                quranProvider.loadSurahs();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة السور
  Widget _buildSurahsList(List<Surah> surahs) {
    final quranProvider = Provider.of<QuranProvider>(context);

    /// بناء بطاقة السورة في الصف باستخدام EnhancedSurahCard
    Widget buildSurahRowCard(Surah surah) {
      return EnhancedSurahCard(
        surah: surah,
        showDetailedInfo: false, // لا نعرض التفاصيل في العرض المضغوط
        showAudioButton: true,
      );
    }

    // عرض رسالة عندما لا توجد نتائج بحث
    if (quranProvider.isSearching &&
        surahs.isEmpty &&
        !quranProvider.isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Theme.of(context).colorScheme.primary.withAlpha(128),
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد نتائج للبحث',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'حاول البحث بكلمات أخرى',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    // عرض مؤشر التحميل أثناء البحث في الآيات
    if (quranProvider.isSearching && quranProvider.isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري البحث في الآيات...', textAlign: TextAlign.center),
          ],
        ),
      );
    }

    // عرض السور في وضع القائمة (الوضع الافتراضي الوحيد)
    // عرض السور في قائمة مع صفين في كل عنصر
    return ListView.builder(
      // تحسين الأداء: تقليل الحشو والتخزين المؤقت
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      // تحسين cacheExtent للأداء الأمثل
      cacheExtent: 300, // تقليل التخزين المؤقت
      // تحسين physics للحصول على تمرير أكثر سلاسة
      physics:
          const ClampingScrollPhysics(), // أداء أفضل من BouncingScrollPhysics
      itemCount: (surahs.length / 2).ceil(), // عدد الصفوف
      itemBuilder: (context, rowIndex) {
        // حساب مؤشرات السور في هذا الصف
        final firstIndex = rowIndex * 2;
        final secondIndex = firstIndex + 1;

        // التحقق من وجود السورة الثانية
        final hasSecondSurah = secondIndex < surahs.length;

        // استخدام RepaintBoundary لتحسين الأداء
        return RepaintBoundary(
          child: Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              children: [
                // السورة الأولى
                Expanded(
                  child: FadeInAnimation(
                    delay: Duration(milliseconds: rowIndex * 100),
                    child: buildSurahRowCard(surahs[firstIndex]),
                  ),
                ),

                const SizedBox(width: 8), // مسافة بين السورتين
                // السورة الثانية (إذا وجدت)
                Expanded(
                  child:
                      hasSecondSurah
                          ? FadeInAnimation(
                            delay: Duration(milliseconds: rowIndex * 100 + 50),
                            child: buildSurahRowCard(surahs[secondIndex]),
                          )
                          : const SizedBox(), // مساحة فارغة إذا لم توجد سورة ثانية
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء حقل البحث
  Widget _buildSearchField(BuildContext context) {
    final quranProvider = Provider.of<QuranProvider>(context);
    final theme = Theme.of(context);
    final TextEditingController searchController = _searchController;

    // تحديد لون وأيقونة نوع البحث
    final bool isSurahSearch = quranProvider.searchType == SearchType.surahName;
    final Color searchTypeColor = isSurahSearch ? Colors.amber : Colors.green;
    final IconData searchTypeIcon =
        isSurahSearch ? Icons.menu_book : Icons.format_quote;

    // إنشاء حقل البحث البسيط
    return Container(
      height: 60,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(30),
        border: Border.all(color: searchTypeColor.withAlpha(100), width: 1.5),
        boxShadow: [
          BoxShadow(
            color: searchTypeColor.withAlpha(40),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // أيقونة نوع البحث
          Container(
            margin: const EdgeInsets.only(right: 8, left: 12),
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: searchTypeColor.withAlpha(30),
              shape: BoxShape.circle,
            ),
            child: Icon(searchTypeIcon, size: 18, color: searchTypeColor),
          ),

          // حقل النص
          Expanded(
            child: TextField(
              controller: searchController,
              focusNode: _searchFocusNode,
              textDirection: TextDirection.rtl,
              textAlign: TextAlign.right,
              style: theme.textTheme.bodyMedium,
              decoration: InputDecoration(
                hintText: isSurahSearch ? 'ابحث عن سورة...' : 'ابحث عن آية...',
                hintTextDirection: TextDirection.rtl,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                isDense: true,
                suffixIcon:
                    quranProvider.searchQuery.isNotEmpty
                        ? IconButton(
                          icon: Icon(
                            Icons.clear,
                            color: searchTypeColor,
                            size: 18,
                          ),
                          onPressed: () {
                            searchController.clear();
                            quranProvider.updateSearchQuery('');
                          },
                        )
                        : null,
              ),
              onChanged: (value) {
                // تنفيذ البحث فورًا أثناء الكتابة بدون تأخير
                quranProvider.updateSearchQuery(value);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// عرض حوار البحث المحسن
  void _showSearchDialog(BuildContext context) {
    final quranProvider = Provider.of<QuranProvider>(context, listen: false);
    final theme = Theme.of(context);

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          elevation: 16,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  theme.colorScheme.primary.withAlpha(10),
                  theme.cardColor,
                ],
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // عنوان الحوار المحسن
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withAlpha(20),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.search,
                        color: theme.colorScheme.primary,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'البحث في القرآن الكريم',
                            style: theme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.primary,
                            ),
                          ),
                          Text(
                            'اختر نوع البحث المناسب',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onSurface.withAlpha(180),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // خيارات البحث المحسنة
                _buildSearchOptionCard(
                  context: context,
                  title: 'البحث في أسماء السور',
                  subtitle: 'ابحث عن سورة معينة بالاسم',
                  icon: Icons.menu_book,
                  color: Colors.amber,
                  onTap: () {
                    Navigator.pop(context);
                    quranProvider.startSearch(SearchType.surahName);
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        _searchFocusNode.requestFocus();
                      }
                    });
                  },
                ),

                const SizedBox(height: 12),

                _buildSearchOptionCard(
                  context: context,
                  title: 'البحث في الآيات',
                  subtitle: 'ابحث في نصوص جميع آيات القرآن الكريم',
                  icon: Icons.format_quote,
                  color: Colors.green,
                  onTap: () {
                    Navigator.pop(context);
                    quranProvider.startSearch(SearchType.ayahText);
                    quranProvider.setSearchInSpecificSurah(false);
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        _searchFocusNode.requestFocus();
                      }
                    });
                  },
                ),

                const SizedBox(height: 20),

                // زر الإلغاء المحسن
                SizedBox(
                  width: double.infinity,
                  child: TextButton(
                    onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text('إلغاء'),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء بطاقة خيار البحث
  Widget _buildSearchOptionCard({
    required BuildContext context,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    bool isSelected = false,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected ? color : color.withAlpha(50),
              width: isSelected ? 2 : 1,
            ),
            color: isSelected ? color.withAlpha(20) : Colors.transparent,
          ),
          child: Row(
            children: [
              // أيقونة الخيار
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withAlpha(30),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Stack(
                  children: [
                    Icon(icon, color: color, size: 24),
                    if (isSelected)
                      Positioned(
                        right: -2,
                        top: -2,
                        child: Container(
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            color: color,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 10,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              const SizedBox(width: 16),

              // نص الخيار
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isSelected ? color : null,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color:
                            isSelected
                                ? color.withAlpha(200)
                                : Theme.of(
                                  context,
                                ).colorScheme.onSurface.withAlpha(180),
                      ),
                    ),
                  ],
                ),
              ),

              // سهم أو علامة اختيار
              isSelected
                  ? Icon(Icons.check_circle, size: 20, color: color)
                  : Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withAlpha(128),
                  ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء زر العودة العائم
  Widget buildFloatingBackButton(BuildContext context, Color color) {
    return FloatingActionButton(
      onPressed: () {
        final quranProvider = Provider.of<QuranProvider>(
          context,
          listen: false,
        );
        quranProvider.cancelSearch();
      },
      backgroundColor: color,
      child: const Icon(Icons.arrow_back, color: Colors.white),
    );
  }
}
