import 'package:flutter/material.dart';
import '../models/quran_model.dart';
import 'new_quran_api_service.dart';
import 'integrated_audio_player.dart';
import '../utils/logger.dart';

/// مزود بيانات القرآن الكريم - الترحيل الكامل للـ API الجديد
class QuranProvider extends ChangeNotifier {
  final NewQuranApiService _apiService = NewQuranApiService();
  final IntegratedAudioPlayer _audioPlayer = IntegratedAudioPlayer();
  List<Surah> _surahs = [];
  List<Ayah> _currentAyahs = [];
  bool _isLoading = false;
  String _error = '';
  bool _isInitialized = false;

  /// قائمة السور
  List<Surah> get surahs => _surahs;

  /// قائمة الآيات الحالية
  List<Ayah> get currentAyahs => _currentAyahs;

  /// حالة التحميل
  bool get isLoading => _isLoading;

  /// رسالة الخطأ
  String get error => _error;

  /// تهيئة مزود البيانات
  Future<void> initialize() async {
    if (_isInitialized) return;
    await loadSurahs();
    _isInitialized = true;
  }

  /// تحميل قائمة السور - محسن للأداء
  Future<void> loadSurahs() async {
    // تحقق من وجود السور مسبقاً
    if (_surahs.isNotEmpty) {
      return; // لا حاجة لإعادة التحميل
    }

    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      AppLogger.info('🔄 تحميل السور من API الجديد...');
      _surahs = await _apiService.getAllSurahs();
      AppLogger.info('✅ تم تحميل ${_surahs.length} سورة من API الجديد');
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      AppLogger.error('خطأ في تحميل قائمة السور: $e');
      _error = 'حدث خطأ أثناء تحميل قائمة السور';
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تحميل آيات سورة محددة
  Future<void> loadAyahs(int surahNumber) async {
    _isLoading = true;
    _error = '';
    _currentAyahs = [];
    notifyListeners();

    try {
      AppLogger.info('🔄 تحميل آيات السورة $surahNumber من API الجديد...');
      _currentAyahs = await _apiService.getSurahAyahs(surahNumber);
      AppLogger.info('✅ تم تحميل ${_currentAyahs.length} آية من API الجديد');
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      AppLogger.error('خطأ في تحميل آيات السورة: $e');
      _error = 'حدث خطأ أثناء تحميل آيات السورة';
      _isLoading = false;
      notifyListeners();
    }
  }

  /// البحث في الآيات
  Future<List<AyahSearchResult>> searchAyahs(String query,
      {int? surahNumber}) async {
    try {
      AppLogger.info('🔍 البحث عن: "$query"');
      return await _apiService.searchAyahs(query, surahNumber: surahNumber);
    } catch (e) {
      AppLogger.error('❌ خطأ في البحث: $e');
      return [];
    }
  }

  /// الحصول على آيات السجدة
  Future<List<Ayah>> getSajdaAyahs() async {
    try {
      AppLogger.info('🕌 تحميل آيات السجدة...');
      return await _apiService.getSajdaAyahs();
    } catch (e) {
      AppLogger.error('❌ خطأ في تحميل آيات السجدة: $e');
      return [];
    }
  }

  /// الحصول على التسجيلات الصوتية لسورة
  Future<List<QuranAudioTrack>> getSurahAudio(int surahNumber) async {
    try {
      AppLogger.info('🎵 تحميل تسجيلات السورة $surahNumber...');
      return await _apiService.getSurahAudio(surahNumber);
    } catch (e) {
      AppLogger.error('❌ خطأ في تحميل التسجيلات: $e');
      return [];
    }
  }

  /// تشغيل سورة
  Future<void> playSurah(int surahNumber, {int? reciterId}) async {
    try {
      await _audioPlayer.playSurah(surahNumber, reciterId: reciterId);
    } catch (e) {
      AppLogger.error('❌ خطأ في تشغيل السورة: $e');
      rethrow;
    }
  }

  /// الحصول على القراء المتاحين
  Future<List<QuranReciter>> getAvailableReciters(int surahNumber) async {
    try {
      return await _audioPlayer.getAvailableReciters(surahNumber);
    } catch (e) {
      AppLogger.error('❌ خطأ في الحصول على القراء: $e');
      return [];
    }
  }

  /// مشغل الصوتيات
  IntegratedAudioPlayer get audioPlayer => _audioPlayer;

  /// تهيئة مشغل الصوتيات
  Future<void> initializeAudioPlayer() async {
    try {
      await _audioPlayer.initialize();
    } catch (e) {
      AppLogger.error('❌ خطأ في تهيئة مشغل الصوتيات: $e');
    }
  }

  /// إعادة تعيين الحالة
  void reset() {
    _surahs = [];
    _currentAyahs = [];
    _isLoading = false;
    _error = '';
    _isInitialized = false;
    notifyListeners();
  }

  /// تنظيف الموارد
  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  /// معلومات الخدمة
  Map<String, dynamic> getServiceInfo() {
    return {
      'name': 'Enhanced Quran Provider',
      'version': '2.0',
      'api_service': _apiService.getServiceInfo(),
      'audio_player': _audioPlayer.getPlayerInfo(),
      'surahs_loaded': _surahs.length,
      'current_ayahs': _currentAyahs.length,
      'is_loading': _isLoading,
      'features': 'Complete API Migration, Integrated Audio, Search, Sajda',
    };
  }
}
