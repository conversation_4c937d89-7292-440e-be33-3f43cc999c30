import 'package:flutter/material.dart';
import 'package:azkary/utils/app_performance_config.dart';
import 'package:azkary/utils/logger.dart';

/// مُحسن القوائم الذكي للأجهزة ذات 6GB RAM
class SmartListOptimizer {
  static final SmartListOptimizer _instance = SmartListOptimizer._internal();
  factory SmartListOptimizer() => _instance;
  SmartListOptimizer._internal();

  // إعدادات التحسين التكيفية
  bool _isLowMemoryMode = false;
  int _maxVisibleItems = 20;
  double _cacheExtent = 200.0;

  /// تحديث حالة الذاكرة
  void updateMemoryState(bool isLowMemory) {
    _isLowMemoryMode = isLowMemory;

    if (isLowMemory) {
      _maxVisibleItems = 10;
      _cacheExtent = 100.0;
      AppLogger.info('تم تفعيل وضع الذاكرة المنخفضة للقوائم');
    } else {
      _maxVisibleItems = 20;
      _cacheExtent = 200.0;
      AppLogger.info('تم إلغاء وضع الذاكرة المنخفضة للقوائم');
    }
  }

  /// إنشاء قائمة محسنة تكيفية
  Widget buildOptimizedListView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    ScrollController? controller,
    ScrollPhysics? physics,
    EdgeInsets? padding,
    bool shrinkWrap = false,
    String? debugLabel,
  }) {
    final effectiveItemCount = _isLowMemoryMode
        ? (itemCount > _maxVisibleItems ? _maxVisibleItems : itemCount)
        : itemCount;

    if (debugLabel != null) {
      AppLogger.info(
          'بناء قائمة محسنة: $debugLabel - العناصر: $effectiveItemCount');
    }

    return ListView.builder(
      controller: controller,
      itemCount: effectiveItemCount,
      itemBuilder: (context, index) {
        // إضافة RepaintBoundary لتحسين الأداء
        return RepaintBoundary(
          child: itemBuilder(context, index),
        );
      },
      physics: physics ?? AppPerformanceConfig.optimizedScrollPhysics,
      padding: padding ?? AppPerformanceConfig.optimizedListPadding,
      shrinkWrap: shrinkWrap,
      cacheExtent: _cacheExtent,
      addAutomaticKeepAlives: false, // تحسين الذاكرة
      addRepaintBoundaries: false, // نحن نضيفها يدوياً
      addSemanticIndexes: false, // تقليل العمليات الإضافية
    );
  }

  /// إنشاء شبكة محسنة تكيفية
  Widget buildOptimizedGridView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    required int crossAxisCount,
    ScrollController? controller,
    ScrollPhysics? physics,
    EdgeInsets? padding,
    double childAspectRatio = 1.0,
    double crossAxisSpacing = 8.0,
    double mainAxisSpacing = 8.0,
    bool shrinkWrap = false,
    String? debugLabel,
  }) {
    final effectiveItemCount = _isLowMemoryMode
        ? (itemCount > _maxVisibleItems ? _maxVisibleItems : itemCount)
        : itemCount;

    if (debugLabel != null) {
      AppLogger.info(
          'بناء شبكة محسنة: $debugLabel - العناصر: $effectiveItemCount');
    }

    return GridView.builder(
      controller: controller,
      itemCount: effectiveItemCount,
      itemBuilder: (context, index) {
        return RepaintBoundary(
          child: itemBuilder(context, index),
        );
      },
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: childAspectRatio,
        crossAxisSpacing: crossAxisSpacing,
        mainAxisSpacing: mainAxisSpacing,
      ),
      physics: physics ?? AppPerformanceConfig.optimizedScrollPhysics,
      padding: padding ?? AppPerformanceConfig.optimizedListPadding,
      shrinkWrap: shrinkWrap,
      cacheExtent: _cacheExtent,
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: false,
      addSemanticIndexes: false,
    );
  }

  /// إنشاء قائمة مع تحميل تدريجي
  Widget buildLazyLoadingListView({
    required int totalItemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    required VoidCallback onLoadMore,
    ScrollController? controller,
    ScrollPhysics? physics,
    EdgeInsets? padding,
    int loadMoreThreshold = 5,
    Widget? loadingWidget,
    String? debugLabel,
  }) {
    final scrollController = controller ?? ScrollController();

    // إضافة مستمع للتحميل التدريجي
    scrollController.addListener(() {
      if (scrollController.position.pixels >=
          scrollController.position.maxScrollExtent -
              (loadMoreThreshold * 100)) {
        onLoadMore();
      }
    });

    final visibleItemCount = _isLowMemoryMode
        ? (totalItemCount > _maxVisibleItems
            ? _maxVisibleItems
            : totalItemCount)
        : totalItemCount;

    if (debugLabel != null) {
      AppLogger.info(
          'بناء قائمة تحميل تدريجي: $debugLabel - العناصر المرئية: $visibleItemCount');
    }

    return ListView.builder(
      controller: scrollController,
      itemCount: visibleItemCount + (totalItemCount > visibleItemCount ? 1 : 0),
      itemBuilder: (context, index) {
        if (index < visibleItemCount) {
          return RepaintBoundary(
            child: itemBuilder(context, index),
          );
        } else {
          // عنصر التحميل
          return loadingWidget ??
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: CircularProgressIndicator(),
                ),
              );
        }
      },
      physics: physics ?? AppPerformanceConfig.optimizedScrollPhysics,
      padding: padding ?? AppPerformanceConfig.optimizedListPadding,
      cacheExtent: _cacheExtent,
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: false,
      addSemanticIndexes: false,
    );
  }

  /// إنشاء قائمة محسنة للبحث
  Widget buildSearchOptimizedListView({
    required List<dynamic> items,
    required Widget Function(BuildContext, dynamic, int) itemBuilder,
    required String searchQuery,
    required bool Function(dynamic, String) searchFilter,
    ScrollController? controller,
    ScrollPhysics? physics,
    EdgeInsets? padding,
    Widget? emptyWidget,
    String? debugLabel,
  }) {
    // تصفية العناصر
    final filteredItems = searchQuery.isEmpty
        ? items
        : items.where((item) => searchFilter(item, searchQuery)).toList();

    final effectiveItemCount = _isLowMemoryMode
        ? (filteredItems.length > _maxVisibleItems
            ? _maxVisibleItems
            : filteredItems.length)
        : filteredItems.length;

    if (debugLabel != null) {
      AppLogger.info(
          'بناء قائمة بحث محسنة: $debugLabel - النتائج: $effectiveItemCount');
    }

    if (effectiveItemCount == 0) {
      return emptyWidget ??
          const Center(
            child: Text('لا توجد نتائج'),
          );
    }

    return ListView.builder(
      controller: controller,
      itemCount: effectiveItemCount,
      itemBuilder: (context, index) {
        return RepaintBoundary(
          child: itemBuilder(context, filteredItems[index], index),
        );
      },
      physics: physics ?? AppPerformanceConfig.optimizedScrollPhysics,
      padding: padding ?? AppPerformanceConfig.optimizedListPadding,
      cacheExtent: _cacheExtent,
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: false,
      addSemanticIndexes: false,
    );
  }

  /// الحصول على الإعدادات الحالية
  Map<String, dynamic> getCurrentSettings() {
    return {
      'isLowMemoryMode': _isLowMemoryMode,
      'maxVisibleItems': _maxVisibleItems,
      'cacheExtent': _cacheExtent,
    };
  }

  /// إعادة تعيين الإعدادات للوضع العادي
  void resetToNormalMode() {
    _isLowMemoryMode = false;
    _maxVisibleItems = 20;
    _cacheExtent = 200.0;
    AppLogger.info('تم إعادة تعيين مُحسن القوائم للوضع العادي');
  }

  /// تفعيل الوضع الحرج للذاكرة
  void enableCriticalMemoryMode() {
    _isLowMemoryMode = true;
    _maxVisibleItems = 5;
    _cacheExtent = 50.0;
    AppLogger.warning('تم تفعيل الوضع الحرج للذاكرة في القوائم');
  }
}
