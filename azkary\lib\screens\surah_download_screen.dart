import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:permission_handler/permission_handler.dart';
import '../services/quran_provider.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/islamic_background.dart';
import '../utils/logger.dart';

/// شاشة تحميل السور للاستخدام بدون إنترنت
class SurahDownloadScreen extends StatefulWidget {
  const SurahDownloadScreen({super.key});

  @override
  State<SurahDownloadScreen> createState() => _SurahDownloadScreenState();
}

class _SurahDownloadScreenState extends State<SurahDownloadScreen> {
  bool _isDownloading = false;
  bool _hasPermissions = false;
  String _downloadStatus = '';
  double _downloadProgress = 0.0;
  int _downloadedCount = 0;
  final int _totalCount = 114;

  @override
  void initState() {
    super.initState();
    _checkPermissions();
    _loadDownloadStatus();
  }

  /// فحص الصلاحيات المطلوبة
  Future<void> _checkPermissions() async {
    try {
      // طلب صلاحية التخزين
      final storageStatus = await Permission.storage.status;
      if (storageStatus.isDenied) {
        final result = await Permission.storage.request();
        _hasPermissions = result.isGranted;
      } else {
        _hasPermissions = storageStatus.isGranted;
      }

      // طلب صلاحية الموقع للميزات المستقبلية
      final locationStatus = await Permission.location.status;
      if (locationStatus.isDenied) {
        await Permission.location.request();
      }

      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      AppLogger.error('خطأ في فحص الصلاحيات: $e');
      _hasPermissions = true; // افتراض الموافقة في حالة الخطأ
    }
  }

  /// تحميل حالة التحميل الحالية
  Future<void> _loadDownloadStatus() async {
    try {
      final quranProvider = Provider.of<QuranProvider>(context, listen: false);
      final downloadedSurahs = await quranProvider.getDownloadedSurahs();

      if (mounted) {
        setState(() {
          _downloadedCount = downloadedSurahs.length;
          _downloadProgress = _downloadedCount / _totalCount;

          if (_downloadedCount == _totalCount) {
            _downloadStatus = 'تم تحميل جميع السور بنجاح';
          } else if (_downloadedCount > 0) {
            _downloadStatus = 'تم تحميل $_downloadedCount من $_totalCount سورة';
          } else {
            _downloadStatus = 'لم يتم تحميل أي سورة بعد';
          }
        });
      }
    } catch (e) {
      AppLogger.error('خطأ في تحميل حالة التحميل: $e');
    }
  }

  /// بدء تحميل جميع السور
  Future<void> _startDownload() async {
    if (!_hasPermissions) {
      _showPermissionDialog();
      return;
    }

    if (_isDownloading) return;

    setState(() {
      _isDownloading = true;
      _downloadStatus = 'جاري التحميل...';
    });

    try {
      final quranProvider = Provider.of<QuranProvider>(context, listen: false);

      // إضافة مستمع لتحديث التقدم
      void updateProgress() {
        if (mounted) {
          setState(() {
            _downloadProgress = quranProvider.downloadProgress;
            _downloadedCount = quranProvider.downloadedSurahsCount;
            _downloadStatus = 'تم تحميل $_downloadedCount من $_totalCount سورة';
          });
        }
      }

      // إضافة المستمع
      quranProvider.addListener(updateProgress);

      // بدء التحميل
      final success = await quranProvider.downloadAllSurahs();

      // إزالة المستمع
      quranProvider.removeListener(updateProgress);

      if (mounted) {
        setState(() {
          _isDownloading = false;
          if (success) {
            _downloadStatus = 'تم تحميل جميع السور بنجاح!';
            _downloadProgress = 1.0;
            _downloadedCount = _totalCount;
          } else {
            _downloadStatus = 'فشل في تحميل بعض السور. يرجى المحاولة مرة أخرى.';
          }
        });

        // عرض رسالة النجاح أو الفشل
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success ? 'تم التحميل بنجاح!' : 'فشل في التحميل'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      AppLogger.error('خطأ في تحميل السور: $e');
      if (mounted) {
        setState(() {
          _isDownloading = false;
          _downloadStatus = 'حدث خطأ أثناء التحميل: $e';
        });
      }
    }
  }

  /// إلغاء التحميل
  void _cancelDownload() {
    // TODO: تطبيق إلغاء التحميل في QuranProvider
    setState(() {
      _isDownloading = false;
      _downloadStatus = 'تم إلغاء التحميل';
    });
  }

  /// عرض حوار الصلاحيات
  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Row(
              children: [
                Icon(Icons.warning, color: Colors.orange),
                SizedBox(width: 8),
                Text('صلاحيات مطلوبة'),
              ],
            ),
            content: const Text(
              'يحتاج التطبيق إلى صلاحية الوصول للتخزين لحفظ السور على جهازك.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _checkPermissions();
                },
                child: const Text('منح الصلاحية'),
              ),
            ],
          ),
    );
  }

  /// حذف جميع السور المحملة
  Future<void> _clearDownloads() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: const Text('هل تريد حذف جميع السور المحملة؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context, true),
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('حذف'),
              ),
            ],
          ),
    );

    if (confirmed == true && mounted) {
      try {
        final quranProvider = Provider.of<QuranProvider>(
          context,
          listen: false,
        );
        await quranProvider.clearAllDownloads();

        if (!mounted) return;

        setState(() {
          _downloadedCount = 0;
          _downloadProgress = 0.0;
          _downloadStatus = 'تم حذف جميع السور المحملة';
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف جميع السور المحملة'),
            backgroundColor: Colors.green,
          ),
        );
      } catch (e) {
        AppLogger.error('خطأ في حذف السور: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isCompleted = _downloadedCount == _totalCount;

    return Scaffold(
      appBar: const CustomAppBar(title: 'تحميل السور'),
      body: IslamicBackground(
        opacity: theme.brightness == Brightness.dark ? 0.02 : 0.03,
        showPattern: true,
        showGradient: false,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // بطاقة المعلومات
              _buildInfoCard(theme, isCompleted),

              const SizedBox(height: 24),

              // شريط التقدم
              _buildProgressSection(theme),

              const SizedBox(height: 24),

              // الأزرار
              _buildActionButtons(theme, isCompleted),

              const Spacer(),

              // معلومات إضافية
              _buildAdditionalInfo(theme),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء بطاقة المعلومات
  Widget _buildInfoCard(ThemeData theme, bool isCompleted) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Icon(
              isCompleted ? Icons.check_circle : Icons.download,
              size: 64,
              color: isCompleted ? Colors.green : theme.colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              isCompleted ? 'تم التحميل بنجاح!' : 'تحميل القرآن الكريم',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: isCompleted ? Colors.green : theme.colorScheme.primary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              isCompleted
                  ? 'يمكنك الآن استخدام القرآن الكريم بدون إنترنت'
                  : 'حمل جميع سور القرآن الكريم للاستخدام بدون إنترنت',
              style: theme.textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم التقدم
  Widget _buildProgressSection(ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'التقدم',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '$_downloadedCount / $_totalCount',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: _downloadProgress,
              backgroundColor: theme.colorScheme.primary.withAlpha(50),
              valueColor: AlwaysStoppedAnimation<Color>(
                _downloadProgress == 1.0
                    ? Colors.green
                    : theme.colorScheme.primary,
              ),
              minHeight: 8,
            ),
            const SizedBox(height: 8),
            Text(_downloadStatus, style: theme.textTheme.bodySmall),
          ],
        ),
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons(ThemeData theme, bool isCompleted) {
    return Column(
      children: [
        if (!isCompleted && !_isDownloading)
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _hasPermissions ? _startDownload : _checkPermissions,
              icon: Icon(_hasPermissions ? Icons.download : Icons.security),
              label: Text(_hasPermissions ? 'بدء التحميل' : 'منح الصلاحيات'),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),

        if (_isDownloading)
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _cancelDownload,
              icon: const Icon(Icons.stop),
              label: const Text('إيقاف التحميل'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),

        if (isCompleted || _downloadedCount > 0) ...[
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: _clearDownloads,
              icon: const Icon(Icons.delete_outline),
              label: const Text('حذف السور المحملة'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.red,
                side: const BorderSide(color: Colors.red),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// بناء المعلومات الإضافية
  Widget _buildAdditionalInfo(ThemeData theme) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'معلومات مهمة',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildInfoRow('• حجم التحميل: حوالي 15-20 ميجابايت'),
            _buildInfoRow('• يتطلب اتصال إنترنت للتحميل الأولي فقط'),
            _buildInfoRow('• بعد التحميل، يمكن استخدام القرآن بدون إنترنت'),
            _buildInfoRow('• يمكن حذف السور المحملة في أي وقت'),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Text(text, style: Theme.of(context).textTheme.bodySmall),
    );
  }
}
