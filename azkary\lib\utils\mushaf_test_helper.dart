import '../models/quran_model.dart';
import '../services/surah_mushaf_service.dart';
import '../utils/logger.dart';

/// مساعد اختبار وضع المصحف الشريف
class MushafTestHelper {
  static final SurahMushafService _service = SurahMushafService();

  /// اختبار تحميل صفحات سورة معينة
  static Future<bool> testSurahPages(Surah surah) async {
    try {
      AppLogger.info('اختبار تحميل صفحات السورة: ${surah.name}');
      
      await _service.initialize();
      final pages = await _service.getSurahPages(surah);
      
      if (pages.isEmpty) {
        AppLogger.error('فشل: لا توجد صفحات للسورة ${surah.name}');
        return false;
      }
      
      AppLogger.info('نجح: تم العثور على ${pages.length} صفحة للسورة ${surah.name}');
      
      // اختبار صحة البيانات
      for (final page in pages) {
        if (page.pageNumber < 1 || page.pageNumber > 604) {
          AppLogger.error('فشل: رقم صفحة غير صحيح ${page.pageNumber}');
          return false;
        }
        
        if (page.imageUrl.isEmpty) {
          AppLogger.error('فشل: رابط صورة فارغ للصفحة ${page.pageNumber}');
          return false;
        }
        
        if (!page.surahNumbers.contains(surah.number)) {
          AppLogger.error('فشل: الصفحة ${page.pageNumber} لا تحتوي على السورة ${surah.number}');
          return false;
        }
      }
      
      AppLogger.info('نجح: جميع بيانات الصفحات صحيحة');
      return true;
    } catch (e) {
      AppLogger.error('فشل في اختبار السورة ${surah.name}: $e');
      return false;
    }
  }

  /// اختبار عدة سور
  static Future<void> testMultipleSurahs() async {
    final testSurahs = [
      Surah(number: 1, name: 'الفاتحة', numberOfAyahs: 7, revelationType: 'مكية'),
      Surah(number: 2, name: 'البقرة', numberOfAyahs: 286, revelationType: 'مدنية'),
      Surah(number: 18, name: 'الكهف', numberOfAyahs: 110, revelationType: 'مكية'),
      Surah(number: 36, name: 'يس', numberOfAyahs: 83, revelationType: 'مكية'),
      Surah(number: 114, name: 'الناس', numberOfAyahs: 6, revelationType: 'مكية'),
    ];

    int successCount = 0;
    for (final surah in testSurahs) {
      final success = await testSurahPages(surah);
      if (success) successCount++;
    }

    AppLogger.info('نتائج الاختبار: $successCount/${testSurahs.length} سور نجحت');
  }

  /// اختبار روابط الصور
  static Future<bool> testImageUrls() async {
    try {
      AppLogger.info('اختبار روابط الصور...');
      
      // اختبار بعض الصفحات العشوائية
      final testPages = [1, 50, 100, 200, 300, 400, 500, 604];
      
      for (final pageNum in testPages) {
        final url = 'https://everyayah.com/data/QuranPages/${pageNum.toString().padLeft(3, '0')}.png';
        AppLogger.info('اختبار رابط الصفحة $pageNum: $url');
        
        // هنا يمكن إضافة اختبار HTTP للتحقق من وجود الصورة
        // لكن للآن نكتفي بطباعة الرابط
      }
      
      return true;
    } catch (e) {
      AppLogger.error('فشل في اختبار روابط الصور: $e');
      return false;
    }
  }

  /// طباعة معلومات تشخيصية
  static void printDiagnosticInfo() {
    AppLogger.info('=== معلومات تشخيصية لوضع المصحف الشريف ===');
    AppLogger.info('إجمالي صفحات المصحف: 604');
    AppLogger.info('رابط الصور الأساسي: https://everyayah.com/data/QuranPages/');
    AppLogger.info('تنسيق اسم الملف: XXX.png (مثل 001.png, 050.png, 604.png)');
    AppLogger.info('عدد السور: 114');
    AppLogger.info('================================================');
  }
}
