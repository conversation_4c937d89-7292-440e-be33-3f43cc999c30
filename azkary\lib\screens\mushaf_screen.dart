import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/mushaf_page.dart';
import '../services/mushaf_service.dart';
import '../utils/mushaf_image_cache.dart';
import '../widgets/mushaf_page_viewer.dart';
import '../widgets/mushaf_navigation_bar.dart';
import '../utils/logger.dart';

/// شاشة المصحف الشريف (طبعة المدينة)
class MushafScreen extends StatefulWidget {
  final int? initialPage;

  const MushafScreen({super.key, this.initialPage});

  @override
  State<MushafScreen> createState() => _MushafScreenState();
}

class _MushafScreenState extends State<MushafScreen>
    with WidgetsBindingObserver {
  final PageController _pageController = PageController();
  final MushafService _mushafService = MushafService();
  final MushafImageCache _imageCache = MushafImageCache();

  List<MushafPage> _pages = [];
  int _currentPageIndex = 0;
  bool _isLoading = true;
  String? _error;
  MushafViewSettings _settings = const MushafViewSettings();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeMushaf();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _pageController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused) {
      // حفظ الموضع الحالي عند إيقاف التطبيق مؤقتاً
      _saveCurrentPosition();
    }
  }

  /// تهيئة المصحف وتحميل الصفحات
  Future<void> _initializeMushaf() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // تهيئة الخدمات
      await _mushafService.initialize();
      await _imageCache.initialize();

      // تحميل جميع الصفحات
      final pages = await _mushafService.getAllPages();
      _settings = _mushafService.settings;

      // تحديد الصفحة الأولى
      final initialPage = widget.initialPage ?? _mushafService.lastViewedPage;
      final initialIndex = (initialPage - 1).clamp(0, pages.length - 1);

      setState(() {
        _pages = pages;
        _currentPageIndex = initialIndex;
        _isLoading = false;
      });

      // الانتقال إلى الصفحة الأولى
      if (_pageController.hasClients) {
        _pageController.jumpToPage(initialIndex);
      } else {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && _pageController.hasClients) {
            _pageController.jumpToPage(initialIndex);
          }
        });
      }

      // تحميل مسبق للصفحات المجاورة
      _preloadAdjacentPages();

      AppLogger.info('تم تحميل ${pages.length} صفحة للمصحف الشريف');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة المصحف: $e');
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  /// تحميل مسبق للصفحات المجاورة
  Future<void> _preloadAdjacentPages() async {
    if (_pages.isEmpty) return;
    await _imageCache.preloadAdjacentPages(_currentPageIndex + 1, _pages);
  }

  /// تغيير الصفحة
  void _onPageChanged(int index) {
    if (index < 0 || index >= _pages.length) return;

    setState(() => _currentPageIndex = index);

    final page = _pages[index];

    // حفظ الموضع الحالي
    _mushafService.saveLastViewedPage(page.pageNumber);

    // تحميل مسبق للصفحات المجاورة الجديدة
    Future.delayed(const Duration(milliseconds: 300), _preloadAdjacentPages);

    // تأثير اهتزاز خفيف
    HapticFeedback.selectionClick();

    AppLogger.info('تم الانتقال إلى صفحة ${page.pageNumber}');
  }

  /// الانتقال إلى الصفحة السابقة
  void _goToPreviousPage() {
    if (_currentPageIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// الانتقال إلى الصفحة التالية
  void _goToNextPage() {
    if (_currentPageIndex < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// الانتقال إلى صفحة معينة
  void _jumpToPage(int pageNumber) {
    final index = pageNumber - 1;
    if (index >= 0 && index < _pages.length) {
      _pageController.animateToPage(
        index,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  /// حفظ الموضع الحالي
  void _saveCurrentPosition() {
    if (_pages.isNotEmpty && _currentPageIndex < _pages.length) {
      final currentPage = _pages[_currentPageIndex];
      _mushafService.saveLastViewedPage(currentPage.pageNumber);
    }
  }

  /// عرض قائمة الإعدادات
  void _showSettingsMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor:
          _settings.nightMode ? const Color(0xFF2C2C2C) : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => _buildSettingsSheet(),
    );
  }

  /// بناء ورقة الإعدادات
  Widget _buildSettingsSheet() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'إعدادات المصحف',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _settings.nightMode ? Colors.white : Colors.black87,
              fontFamily: 'Amiri',
            ),
          ),
          const SizedBox(height: 16),

          // الوضع الليلي
          SwitchListTile(
            title: Text(
              'الوضع الليلي',
              style: TextStyle(
                color: _settings.nightMode ? Colors.white : Colors.black87,
                fontFamily: 'Amiri',
              ),
            ),
            value: _settings.nightMode,
            onChanged: (value) {
              setState(() {
                _settings = _settings.copyWith(nightMode: value);
              });
              _mushafService.saveSettings(_settings);
            },
          ),

          // عرض معلومات الصفحة
          SwitchListTile(
            title: Text(
              'عرض معلومات الصفحة',
              style: TextStyle(
                color: _settings.nightMode ? Colors.white : Colors.black87,
                fontFamily: 'Amiri',
              ),
            ),
            value: _settings.showPageInfo,
            onChanged: (value) {
              setState(() {
                _settings = _settings.copyWith(showPageInfo: value);
              });
              _mushafService.saveSettings(_settings);
            },
          ),

          // إمكانية التكبير
          SwitchListTile(
            title: Text(
              'إمكانية التكبير',
              style: TextStyle(
                color: _settings.nightMode ? Colors.white : Colors.black87,
                fontFamily: 'Amiri',
              ),
            ),
            value: _settings.enableZoom,
            onChanged: (value) {
              setState(() {
                _settings = _settings.copyWith(enableZoom: value);
              });
              _mushafService.saveSettings(_settings);
            },
          ),

          const SizedBox(height: 16),

          // زر مسح التخزين المؤقت
          ElevatedButton.icon(
            onPressed: () async {
              await _imageCache.clearCache();
              if (mounted) {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text(
                      'تم مسح التخزين المؤقت',
                      style: TextStyle(fontFamily: 'Amiri'),
                    ),
                  ),
                );
              }
            },
            icon: const Icon(Icons.clear_all),
            label: const Text(
              'مسح التخزين المؤقت',
              style: TextStyle(fontFamily: 'Amiri'),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode =
        _settings.nightMode || theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor:
          isDarkMode ? const Color(0xFF1A1A1A) : const Color(0xFFFFFDF5),
      appBar: AppBar(
        title: const Text(
          'المصحف الشريف',
          style: TextStyle(fontFamily: 'Amiri', fontWeight: FontWeight.bold),
        ),
        backgroundColor: isDarkMode ? const Color(0xFF2C2C2C) : Colors.white,
        foregroundColor: isDarkMode ? Colors.white : Colors.black87,
        elevation: 1,
        actions: [
          IconButton(
            onPressed: _showSettingsMenu,
            icon: const Icon(Icons.settings),
            tooltip: 'الإعدادات',
          ),
        ],
      ),
      body: _buildBody(isDarkMode),
      bottomNavigationBar:
          _pages.isNotEmpty
              ? MushafNavigationBar(
                currentPage:
                    _pages.isNotEmpty
                        ? _pages[_currentPageIndex].pageNumber
                        : 1,
                totalPages: MushafService.totalPages,
                onPreviousPage: _goToPreviousPage,
                onNextPage: _goToNextPage,
                onPageChanged: _jumpToPage,
                isDarkMode: isDarkMode,
              )
              : null,
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody(bool isDarkMode) {
    if (_isLoading) {
      return _buildLoadingState(isDarkMode);
    }

    if (_error != null) {
      return _buildErrorState(isDarkMode);
    }

    if (_pages.isEmpty) {
      return _buildEmptyState(isDarkMode);
    }

    return RepaintBoundary(
      child: PageView.builder(
        controller: _pageController,
        onPageChanged: _onPageChanged,
        itemCount: _pages.length,
        itemBuilder: (context, index) {
          final page = _pages[index];
          return RepaintBoundary(
            child: MushafPageViewer(
              page: page,
              settings: _settings,
              enableZoom: _settings.enableZoom,
            ),
          );
        },
      ),
    );
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState(bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              isDarkMode ? Colors.amber[400]! : Colors.brown[600]!,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل المصحف الشريف...',
            style: TextStyle(
              fontSize: 16,
              color: isDarkMode ? Colors.white70 : Colors.black87,
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState(bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: isDarkMode ? Colors.red[300] : Colors.red[600],
          ),
          const SizedBox(height: 16),
          Text(
            'خطأ في تحميل المصحف',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.black87,
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              _error!,
              style: TextStyle(
                fontSize: 14,
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                fontFamily: 'Amiri',
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _initializeMushaf,
            icon: const Icon(Icons.refresh),
            label: const Text(
              'إعادة المحاولة',
              style: TextStyle(fontFamily: 'Amiri'),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor:
                  isDarkMode ? Colors.amber[400] : Colors.brown[600],
              foregroundColor: isDarkMode ? Colors.black : Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState(bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.menu_book_outlined,
            size: 64,
            color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد صفحات متاحة',
            style: TextStyle(
              fontSize: 18,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
