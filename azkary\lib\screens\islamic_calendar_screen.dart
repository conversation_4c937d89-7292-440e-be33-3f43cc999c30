import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../models/islamic_calendar_model.dart';
import '../services/islamic_calendar_service.dart';
import '../services/theme_provider.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/islamic_background.dart';
import '../utils/logger.dart';

/// شاشة التقويم الإسلامي
class IslamicCalendarScreen extends StatefulWidget {
  const IslamicCalendarScreen({super.key});

  @override
  State<IslamicCalendarScreen> createState() => _IslamicCalendarScreenState();
}

class _IslamicCalendarScreenState extends State<IslamicCalendarScreen> {
  DateTime _selectedDate = DateTime.now();
  DateTime _currentMonth = DateTime.now();
  List<IslamicCalendarModel> _monthData = [];
  IslamicCalendarModel? _todayData;
  bool _isLoading = true;
  String _error = '';

  @override
  void initState() {
    super.initState();
    _loadCalendarData();
  }

  /// تحميل بيانات التقويم
  Future<void> _loadCalendarData() async {
    setState(() {
      _isLoading = true;
      _error = '';
    });

    try {
      AppLogger.info(
        'تحميل بيانات التقويم للشهر: ${_currentMonth.month}/${_currentMonth.year}',
      );

      // تحميل بيانات الشهر
      final monthData = await IslamicCalendarService.getMonthData(
        _currentMonth.year,
        _currentMonth.month,
      );

      // تحميل بيانات اليوم
      final todayData = await IslamicCalendarService.getCalendarData(
        DateTime.now(),
      );

      if (mounted) {
        setState(() {
          _monthData = monthData;
          _todayData = todayData;
          _isLoading = false;
        });
      }
    } catch (e) {
      AppLogger.error('خطأ في تحميل بيانات التقويم: $e');
      if (mounted) {
        setState(() {
          _error = 'فشل في تحميل بيانات التقويم';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          body: IslamicBackground(
            child: Column(
              children: [
                // شريط التطبيق المخصص
                CustomAppBar(
                  title: 'التقويم الإسلامي',
                  actions: [
                    IconButton(
                      icon: const Icon(Icons.today),
                      onPressed: _goToToday,
                      tooltip: 'اليوم',
                    ),
                  ],
                ),

                // محتوى التقويم
                Expanded(
                  child:
                      _isLoading
                          ? _buildLoadingView()
                          : _error.isNotEmpty
                          ? _buildErrorView()
                          : _buildCalendarView(),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء عرض التحميل
  Widget _buildLoadingView() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('جاري تحميل التقويم...', style: TextStyle(fontSize: 16)),
        ],
      ),
    );
  }

  /// بناء عرض الخطأ
  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            _error,
            style: Theme.of(context).textTheme.titleMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadCalendarData,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// بناء عرض التقويم
  Widget _buildCalendarView() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // معلومات اليوم الحالي
          if (_todayData != null) _buildTodayCard(),

          const SizedBox(height: 20),

          // رأس الشهر مع أزرار التنقل
          _buildMonthHeader(),

          const SizedBox(height: 16),

          // شبكة التقويم
          _buildCalendarGrid(),

          const SizedBox(height: 20),

          // الأحداث القادمة
          _buildUpcomingEvents(),
        ],
      ),
    );
  }

  /// بناء بطاقة اليوم الحالي
  Widget _buildTodayCard() {
    final today = _todayData!;
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            isDarkMode ? Colors.amber[700]! : Colors.brown[600]!,
            isDarkMode ? Colors.amber[800]! : Colors.brown[700]!,
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // التاريخ الهجري
          Text(
            IslamicCalendarService.formatHijriDate(today.hijriDate),
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 8),

          // التاريخ الميلادي
          Text(
            IslamicCalendarService.formatGregorianDate(today.gregorianDate),
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white70,
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 12),

          // مرحلة القمر
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                MoonPhaseCalculator.getMoonPhaseIcon(today.moonPhase),
                style: const TextStyle(fontSize: 24),
              ),
              const SizedBox(width: 8),
              Text(
                MoonPhaseCalculator.getMoonPhaseName(today.moonPhase),
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                  fontFamily: 'Amiri',
                ),
              ),
            ],
          ),

          // الأحداث اليوم
          if (today.hasEvents) ...[
            const SizedBox(height: 12),
            ...today.events.map(
              (event) => Container(
                margin: const EdgeInsets.only(top: 4),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  event.name,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.white,
                    fontFamily: 'Amiri',
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],

          // أوقات الصلاة اليوم
          if (today.prayerTimes != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildPrayerTimeItem('الفجر', today.prayerTimes!.fajr),
                  _buildPrayerTimeItem('الظهر', today.prayerTimes!.dhuhr),
                  _buildPrayerTimeItem('العصر', today.prayerTimes!.asr),
                  _buildPrayerTimeItem('المغرب', today.prayerTimes!.maghrib),
                  _buildPrayerTimeItem('العشاء', today.prayerTimes!.isha),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء رأس الشهر
  Widget _buildMonthHeader() {
    final theme = Theme.of(context);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // زر الشهر السابق
        IconButton(
          onPressed: _previousMonth,
          icon: const Icon(Icons.arrow_back_ios),
          tooltip: 'الشهر السابق',
        ),

        // اسم الشهر والسنة
        Column(
          children: [
            Text(
              IslamicCalendarService.getGregorianMonthName(_currentMonth.month),
              style: theme.textTheme.titleLarge?.copyWith(
                fontFamily: 'Amiri',
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              IslamicCalendarService.convertToArabicNumbers(_currentMonth.year),
              style: theme.textTheme.titleMedium?.copyWith(
                fontFamily: 'Amiri',
                color: theme.textTheme.bodyMedium?.color?.withValues(
                  alpha: 0.7,
                ),
              ),
            ),
          ],
        ),

        // زر الشهر التالي
        IconButton(
          onPressed: _nextMonth,
          icon: const Icon(Icons.arrow_forward_ios),
          tooltip: 'الشهر التالي',
        ),
      ],
    );
  }

  /// بناء شبكة التقويم
  Widget _buildCalendarGrid() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // رأس أيام الأسبوع
          _buildWeekHeader(),

          // أيام الشهر
          _buildDaysGrid(),
        ],
      ),
    );
  }

  /// بناء رأس أيام الأسبوع
  Widget _buildWeekHeader() {
    const dayNames = ['أح', 'إث', 'ث', 'أر', 'خ', 'ج', 'س'];

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children:
            dayNames
                .map(
                  (day) => Expanded(
                    child: Text(
                      day,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                        fontFamily: 'Amiri',
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                )
                .toList(),
      ),
    );
  }

  /// بناء شبكة الأيام
  Widget _buildDaysGrid() {
    final firstDay = DateTime(_currentMonth.year, _currentMonth.month, 1);
    final lastDay = DateTime(_currentMonth.year, _currentMonth.month + 1, 0);
    final startWeekday = firstDay.weekday % 7; // تحويل لبداية الأسبوع من الأحد

    final days = <Widget>[];

    // إضافة أيام فارغة في بداية الشهر
    for (int i = 0; i < startWeekday; i++) {
      days.add(const SizedBox());
    }

    // إضافة أيام الشهر
    for (int day = 1; day <= lastDay.day; day++) {
      final date = DateTime(_currentMonth.year, _currentMonth.month, day);
      final calendarData = _monthData.firstWhere(
        (data) => data.gregorianDate.day == day,
        orElse:
            () => IslamicCalendarModel(
              hijriDate: _createSimpleHijriDate(date),
              gregorianDate: date,
              events: [],
              moonPhase: MoonPhase.newMoon,
            ),
      );

      days.add(_buildDayCell(calendarData));
    }

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 7,
      children: days,
    );
  }

  /// بناء خلية اليوم
  Widget _buildDayCell(IslamicCalendarModel dayData) {
    final theme = Theme.of(context);
    final isToday = IslamicCalendarService.isToday(dayData.gregorianDate);
    final isSelected =
        _selectedDate.day == dayData.gregorianDate.day &&
        _selectedDate.month == dayData.gregorianDate.month &&
        _selectedDate.year == dayData.gregorianDate.year;

    return GestureDetector(
      onTap: () => _selectDate(dayData.gregorianDate),
      child: Container(
        margin: const EdgeInsets.all(2),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? theme.primaryColor
                  : isToday
                  ? theme.primaryColor.withValues(alpha: 0.2)
                  : null,
          borderRadius: BorderRadius.circular(8),
          border:
              dayData.isFriday
                  ? Border.all(color: Colors.green, width: 1)
                  : null,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // التاريخ الميلادي
            Text(
              IslamicCalendarService.convertToArabicNumbers(
                dayData.gregorianDate.day,
              ),
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color:
                    isSelected
                        ? Colors.white
                        : theme.textTheme.bodyLarge?.color,
                fontFamily: 'Amiri',
              ),
            ),

            // التاريخ الهجري
            Text(
              IslamicCalendarService.convertToArabicNumbers(
                dayData.hijriDate.hDay,
              ),
              style: TextStyle(
                fontSize: 10,
                color:
                    isSelected
                        ? Colors.white70
                        : theme.textTheme.bodyMedium?.color?.withValues(
                          alpha: 0.6,
                        ),
                fontFamily: 'Amiri',
              ),
            ),

            // مؤشر الأحداث
            if (dayData.hasEvents)
              Container(
                width: 4,
                height: 4,
                decoration: BoxDecoration(
                  color: isSelected ? Colors.white : theme.primaryColor,
                  shape: BoxShape.circle,
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// بناء الأحداث القادمة
  Widget _buildUpcomingEvents() {
    final upcomingEvents = IslamicCalendarService.getUpcomingEvents();

    if (upcomingEvents.isEmpty) {
      return const SizedBox();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الأحداث القادمة',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontFamily: 'Amiri',
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: 12),

        ...upcomingEvents
            .take(5)
            .map(
              (event) => Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Color(
                    IslamicCalendarService.getEventColor(event.type),
                  ).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Color(
                      IslamicCalendarService.getEventColor(event.type),
                    ).withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: Color(
                          IslamicCalendarService.getEventColor(event.type),
                        ),
                        shape: BoxShape.circle,
                      ),
                    ),

                    const SizedBox(width: 12),

                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            event.name,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontFamily: 'Amiri',
                            ),
                          ),
                          if (event.description.isNotEmpty)
                            Text(
                              event.description,
                              style: TextStyle(
                                fontSize: 12,
                                color: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.color
                                    ?.withValues(alpha: 0.7),
                                fontFamily: 'Amiri',
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
      ],
    );
  }

  /// الانتقال للشهر السابق
  void _previousMonth() {
    setState(() {
      _currentMonth = DateTime(_currentMonth.year, _currentMonth.month - 1);
    });
    _loadCalendarData();
  }

  /// الانتقال للشهر التالي
  void _nextMonth() {
    setState(() {
      _currentMonth = DateTime(_currentMonth.year, _currentMonth.month + 1);
    });
    _loadCalendarData();
  }

  /// الانتقال لليوم الحالي
  void _goToToday() {
    setState(() {
      _currentMonth = DateTime.now();
      _selectedDate = DateTime.now();
    });
    _loadCalendarData();
  }

  /// تحديد تاريخ
  void _selectDate(DateTime date) {
    setState(() {
      _selectedDate = date;
    });
  }

  /// بناء عنصر وقت الصلاة
  Widget _buildPrayerTimeItem(String name, DateTime time) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          name,
          style: const TextStyle(
            fontSize: 10,
            color: Colors.white70,
            fontFamily: 'Amiri',
          ),
        ),
        Text(
          '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}',
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            fontFamily: 'Amiri',
          ),
        ),
      ],
    );
  }

  /// إنشاء تاريخ هجري مؤقت (حل مؤقت)
  dynamic _createSimpleHijriDate(DateTime date) {
    // حل مؤقت - إرجاع كائن بسيط بدلاً من HijriCalendar
    return SimpleHijriDate(hYear: 1445, hMonth: date.month, hDay: date.day);
  }
}

/// كلاس مؤقت للتاريخ الهجري
class SimpleHijriDate {
  final int hYear;
  final int hMonth;
  final int hDay;

  SimpleHijriDate({
    required this.hYear,
    required this.hMonth,
    required this.hDay,
  });
}
