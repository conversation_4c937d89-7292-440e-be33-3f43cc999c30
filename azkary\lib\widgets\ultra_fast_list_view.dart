import 'package:flutter/material.dart';

/// ListView محسن فائق السرعة للأجهزة ذات الذاكرة المحدودة
class UltraFastListView extends StatelessWidget {
  final int itemCount;
  final IndexedWidgetBuilder itemBuilder;
  final ScrollController? controller;
  final EdgeInsetsGeometry? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final double? cacheExtent;
  final bool addAutomaticKeepAlives;
  final bool addRepaintBoundaries;
  final bool addSemanticIndexes;

  const UltraFastListView({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    this.controller,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
    this.cacheExtent,
    this.addAutomaticKeepAlives = false,
    this.addRepaintBoundaries = false,
    this.addSemanticIndexes = false,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: controller,
      padding:
          padding ?? const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      shrinkWrap: shrinkWrap,
      itemCount: itemCount,
      itemBuilder: (context, index) {
        // تغليف كل عنصر في RepaintBoundary لتحسين الأداء
        return RepaintBoundary(child: itemBuilder(context, index));
      },
      // إعدادات محسنة للأداء الفائق
      cacheExtent: cacheExtent ?? 150, // تقليل جذري للتخزين المؤقت
      physics: physics ?? const ClampingScrollPhysics(), // فيزياء محسنة
      addAutomaticKeepAlives: addAutomaticKeepAlives, // عدم الاحتفاظ بالعناصر
      addRepaintBoundaries: addRepaintBoundaries, // نحن نضيفها يدوياً
      addSemanticIndexes: addSemanticIndexes, // تقليل العمليات الإضافية
    );
  }
}

/// GridView محسن فائق السرعة
class UltraFastGridView extends StatelessWidget {
  final int itemCount;
  final IndexedWidgetBuilder itemBuilder;
  final SliverGridDelegate gridDelegate;
  final ScrollController? controller;
  final EdgeInsetsGeometry? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final double? cacheExtent;

  const UltraFastGridView({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    required this.gridDelegate,
    this.controller,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
    this.cacheExtent,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      controller: controller,
      padding: padding ?? const EdgeInsets.all(8),
      shrinkWrap: shrinkWrap,
      gridDelegate: gridDelegate,
      itemCount: itemCount,
      itemBuilder: (context, index) {
        return RepaintBoundary(child: itemBuilder(context, index));
      },
      // إعدادات محسنة للأداء
      cacheExtent: cacheExtent ?? 200,
      physics: physics ?? const ClampingScrollPhysics(),
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: false,
      addSemanticIndexes: false,
    );
  }
}

/// بطاقة محسنة فائقة السرعة
class UltraFastCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final Color? color;
  final double? elevation;
  final BorderRadius? borderRadius;
  final VoidCallback? onTap;
  final bool enableShadow;

  const UltraFastCard({
    super.key,
    required this.child,
    this.margin,
    this.padding,
    this.color,
    this.elevation,
    this.borderRadius,
    this.onTap,
    this.enableShadow = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    Widget card = Container(
      margin: margin ?? const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      padding: padding ?? const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color ?? theme.cardColor,
        borderRadius: borderRadius ?? BorderRadius.circular(8),
        boxShadow:
            enableShadow && (elevation ?? 2) > 0
                ? [
                  BoxShadow(
                    color: theme.shadowColor.withAlpha(25),
                    blurRadius: elevation ?? 2,
                    offset: Offset(0, (elevation ?? 2) / 2),
                  ),
                ]
                : null,
      ),
      child: child,
    );

    if (onTap != null) {
      card = InkWell(
        onTap: onTap,
        borderRadius: borderRadius ?? BorderRadius.circular(8),
        child: card,
      );
    }

    return RepaintBoundary(child: card);
  }
}

/// صورة محسنة فائقة السرعة
class UltraFastImage extends StatelessWidget {
  final String imagePath;
  final double? width;
  final double? height;
  final BoxFit fit;
  final bool isAsset;
  final FilterQuality filterQuality;

  const UltraFastImage({
    super.key,
    required this.imagePath,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.isAsset = true,
    this.filterQuality = FilterQuality.low,
  });

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child:
          isAsset
              ? Image.asset(
                imagePath,
                width: width,
                height: height,
                fit: fit,
                cacheWidth: width?.round(),
                cacheHeight: height?.round(),
                filterQuality: filterQuality,
                gaplessPlayback: true,
                excludeFromSemantics: true,
              )
              : Image.network(
                imagePath,
                width: width,
                height: height,
                fit: fit,
                cacheWidth: width?.round(),
                cacheHeight: height?.round(),
                filterQuality: filterQuality,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return SizedBox(
                    width: width,
                    height: height,
                    child: const Center(
                      child: SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return SizedBox(
                    width: width,
                    height: height,
                    child: const Icon(Icons.error, size: 24),
                  );
                },
              ),
    );
  }
}

/// أيقونة محسنة فائقة السرعة
class UltraFastIcon extends StatelessWidget {
  final IconData icon;
  final double? size;
  final Color? color;

  const UltraFastIcon({super.key, required this.icon, this.size, this.color});

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(child: Icon(icon, size: size ?? 24, color: color));
  }
}

/// نص محسن فائق السرعة
class UltraFastText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  const UltraFastText({
    super.key,
    required this.text,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: Text(
        text,
        style: style,
        textAlign: textAlign,
        maxLines: maxLines,
        overflow: overflow,
      ),
    );
  }
}

/// زر محسن فائق السرعة
class UltraFastButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget child;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;

  const UltraFastButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.backgroundColor,
    this.foregroundColor,
    this.padding,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: foregroundColor,
          padding:
              padding ??
              const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius ?? BorderRadius.circular(8),
          ),
        ),
        child: child,
      ),
    );
  }
}
