import 'package:http/http.dart' as http;
import '../models/quran_model.dart';
import '../services/surah_mushaf_service.dart';
import '../utils/logger.dart';

/// مساعد تشخيص مشاكل المصحف الشريف
class MushafDebugHelper {
  static final SurahMushafService _service = SurahMushafService();

  /// اختبار شامل لوضع المصحف الشريف
  static Future<bool> runFullDiagnostic() async {
    AppLogger.info('=== بدء التشخيص الشامل لوضع المصحف الشريف ===');
    
    bool allTestsPassed = true;
    
    try {
      // اختبار 1: تهيئة الخدمة
      AppLogger.info('اختبار 1: تهيئة الخدمة...');
      await _service.initialize();
      AppLogger.info('✅ تم تهيئة الخدمة بنجاح');
      
      // اختبار 2: اختبار سورة الفاتحة
      AppLogger.info('اختبار 2: اختبار سورة الفاتحة...');
      final fatihaTest = await _testSurah(Surah(
        number: 1,
        name: 'الفاتحة',
        englishName: 'Al-Fatihah',
        englishNameTranslation: 'The Opening',
        numberOfAyahs: 7,
        revelationType: 'مكية',
      ));
      if (!fatihaTest) allTestsPassed = false;
      
      // اختبار 3: اختبار سورة البقرة
      AppLogger.info('اختبار 3: اختبار سورة البقرة...');
      final baqarahTest = await _testSurah(Surah(
        number: 2,
        name: 'البقرة',
        englishName: 'Al-Baqarah',
        englishNameTranslation: 'The Cow',
        numberOfAyahs: 286,
        revelationType: 'مدنية',
      ));
      if (!baqarahTest) allTestsPassed = false;
      
      // اختبار 4: اختبار سورة الناس
      AppLogger.info('اختبار 4: اختبار سورة الناس...');
      final nasTest = await _testSurah(Surah(
        number: 114,
        name: 'الناس',
        englishName: 'An-Nas',
        englishNameTranslation: 'Mankind',
        numberOfAyahs: 6,
        revelationType: 'مكية',
      ));
      if (!nasTest) allTestsPassed = false;
      
      // اختبار 5: اختبار روابط الصور
      AppLogger.info('اختبار 5: اختبار روابط الصور...');
      final imageTest = await _testImageUrls();
      if (!imageTest) allTestsPassed = false;
      
      AppLogger.info('=== انتهاء التشخيص الشامل ===');
      AppLogger.info(allTestsPassed ? '✅ جميع الاختبارات نجحت' : '❌ بعض الاختبارات فشلت');
      
      return allTestsPassed;
    } catch (e) {
      AppLogger.error('خطأ في التشخيص الشامل: $e');
      return false;
    }
  }

  /// اختبار سورة معينة
  static Future<bool> _testSurah(Surah surah) async {
    try {
      AppLogger.info('  - اختبار السورة: ${surah.name} (رقم ${surah.number})');
      
      final pages = await _service.getSurahPages(surah);
      
      if (pages.isEmpty) {
        AppLogger.error('  ❌ لا توجد صفحات للسورة ${surah.name}');
        return false;
      }
      
      AppLogger.info('  ✅ تم العثور على ${pages.length} صفحة للسورة ${surah.name}');
      
      // اختبار صحة البيانات
      for (final page in pages) {
        if (page.pageNumber < 1 || page.pageNumber > 604) {
          AppLogger.error('  ❌ رقم صفحة غير صحيح: ${page.pageNumber}');
          return false;
        }
        
        if (page.imageUrl.isEmpty) {
          AppLogger.error('  ❌ رابط صورة فارغ للصفحة ${page.pageNumber}');
          return false;
        }
        
        if (!page.surahNumbers.contains(surah.number)) {
          AppLogger.error('  ❌ الصفحة ${page.pageNumber} لا تحتوي على السورة ${surah.number}');
          return false;
        }
      }
      
      AppLogger.info('  ✅ جميع بيانات الصفحات صحيحة للسورة ${surah.name}');
      return true;
    } catch (e) {
      AppLogger.error('  ❌ خطأ في اختبار السورة ${surah.name}: $e');
      return false;
    }
  }

  /// اختبار روابط الصور
  static Future<bool> _testImageUrls() async {
    try {
      AppLogger.info('  - اختبار روابط الصور...');
      
      // اختبار بعض الصفحات العشوائية
      final testPages = [1, 50, 100, 200, 300, 400, 500, 604];
      int successCount = 0;
      
      for (final pageNum in testPages) {
        final url = 'https://everyayah.com/data/QuranPages/${pageNum.toString().padLeft(3, '0')}.png';
        
        try {
          AppLogger.info('    - اختبار رابط الصفحة $pageNum...');
          
          final response = await http.head(Uri.parse(url)).timeout(
            const Duration(seconds: 10),
          );
          
          if (response.statusCode == 200) {
            AppLogger.info('    ✅ الصفحة $pageNum متاحة');
            successCount++;
          } else {
            AppLogger.error('    ❌ الصفحة $pageNum غير متاحة (${response.statusCode})');
          }
        } catch (e) {
          AppLogger.error('    ❌ خطأ في اختبار الصفحة $pageNum: $e');
        }
      }
      
      final successRate = (successCount / testPages.length) * 100;
      AppLogger.info('  معدل نجاح روابط الصور: ${successRate.toStringAsFixed(1)}% ($successCount/${testPages.length})');
      
      // نعتبر الاختبار ناجح إذا كان 50% على الأقل من الروابط تعمل
      final passed = successRate >= 50;
      AppLogger.info(passed ? '  ✅ اختبار روابط الصور نجح' : '  ❌ اختبار روابط الصور فشل');
      
      return passed;
    } catch (e) {
      AppLogger.error('  ❌ خطأ في اختبار روابط الصور: $e');
      return false;
    }
  }

  /// طباعة معلومات تشخيصية مفصلة
  static void printDetailedInfo() {
    AppLogger.info('=== معلومات تشخيصية مفصلة لوضع المصحف الشريف ===');
    AppLogger.info('إجمالي صفحات المصحف: 604');
    AppLogger.info('رابط الصور الأساسي: https://everyayah.com/data/QuranPages/');
    AppLogger.info('تنسيق اسم الملف: XXX.png (مثل 001.png, 050.png, 604.png)');
    AppLogger.info('عدد السور: 114');
    AppLogger.info('');
    AppLogger.info('أمثلة على روابط الصور:');
    AppLogger.info('- الصفحة الأولى: https://everyayah.com/data/QuranPages/001.png');
    AppLogger.info('- الصفحة 50: https://everyayah.com/data/QuranPages/050.png');
    AppLogger.info('- الصفحة الأخيرة: https://everyayah.com/data/QuranPages/604.png');
    AppLogger.info('');
    AppLogger.info('خريطة السور الرئيسية:');
    AppLogger.info('- الفاتحة: صفحة 1');
    AppLogger.info('- البقرة: صفحات 2-49');
    AppLogger.info('- آل عمران: صفحات 50-76');
    AppLogger.info('- النساء: صفحات 77-106');
    AppLogger.info('- المائدة: صفحات 106-127');
    AppLogger.info('- الناس: صفحة 604');
    AppLogger.info('================================================');
  }

  /// اختبار سريع لسورة واحدة
  static Future<bool> quickTestSurah(Surah surah) async {
    AppLogger.info('=== اختبار سريع للسورة: ${surah.name} ===');
    
    try {
      await _service.initialize();
      final result = await _testSurah(surah);
      
      AppLogger.info(result ? '✅ الاختبار نجح' : '❌ الاختبار فشل');
      return result;
    } catch (e) {
      AppLogger.error('❌ خطأ في الاختبار السريع: $e');
      return false;
    }
  }

  /// اختبار الاتصال بالإنترنت
  static Future<bool> testInternetConnection() async {
    try {
      AppLogger.info('اختبار الاتصال بالإنترنت...');
      
      final response = await http.get(
        Uri.parse('https://www.google.com'),
      ).timeout(const Duration(seconds: 10));
      
      final connected = response.statusCode == 200;
      AppLogger.info(connected ? '✅ الاتصال بالإنترنت متاح' : '❌ لا يوجد اتصال بالإنترنت');
      
      return connected;
    } catch (e) {
      AppLogger.error('❌ خطأ في اختبار الاتصال بالإنترنت: $e');
      return false;
    }
  }
}
