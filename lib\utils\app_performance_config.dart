import 'package:flutter/material.dart';

/// إعدادات تحسين الأداء للتطبيق - محسن للأجهزة ذات 6GB RAM
class AppPerformanceConfig {
  // إعدادات الانتقالات المحسنة للأجهزة المحدودة
  static const Duration ultraFastTransition = Duration(milliseconds: 100);
  static const Duration fastTransition = Duration(milliseconds: 150);
  static const Duration mediumTransition = Duration(milliseconds: 200);
  static const Duration slowTransition = Duration(milliseconds: 250);

  // منحنيات محسنة للأداء
  static const Curve ultraFastCurve = Curves.easeOut;
  static const Curve fastCurve = Curves.easeOut;
  static const Curve mediumCurve = Curves.easeInOut;
  static const Curve slowCurve = Curves.easeInOutCubic;

  // إعدادات الذاكرة المحسنة للأجهزة المحدودة
  static const int maxMemoryUsageMB = 80; // حد أقصى 80MB للأجهزة 6GB
  static const int lowMemoryThresholdMB = 60; // تحذير عند 60MB
  static const int criticalMemoryThresholdMB = 70; // حرج عند 70MB

  // إعدادات القوائم المحسنة للأجهزة المحدودة
  static const ScrollPhysics optimizedScrollPhysics = ClampingScrollPhysics();
  static const double optimizedCacheExtent = 200.0; // تقليل للأجهزة المحدودة
  static const double lowMemoryCacheExtent = 150.0; // للأجهزة الضعيفة
  static const EdgeInsets optimizedListPadding = EdgeInsets.symmetric(
    horizontal: 8, // تقليل الحشو
    vertical: 4,
  );

  // إعدادات التحميل التدريجي
  static const int maxItemsPerBatch = 20; // تحميل 20 عنصر في المرة
  static const int preloadThreshold = 5; // بدء التحميل قبل 5 عناصر

  // إعدادات الصور المحسنة
  static const FilterQuality optimizedImageQuality = FilterQuality.medium;
  static const double optimizedImageScale = 0.8;
  static const int maxImageWidth = 1080; // دقة 1080p كما طلب المستخدم
  static const int maxImageHeight = 1920;

  // إعدادات الظلال المحسنة
  static List<BoxShadow> optimizedShadow({
    required BuildContext context,
    double opacity = 0.15,
    double blurRadius = 8.0,
    Offset offset = const Offset(0, 2),
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return [
      BoxShadow(
        color: isDark
            ? Colors.black
                .withAlpha((opacity * 255 * 1.5).round().clamp(0, 255))
            : Colors.black.withAlpha((opacity * 255).round().clamp(0, 255)),
        blurRadius: blurRadius,
        offset: offset,
      ),
    ];
  }

  // إعدادات التدرجات المحسنة
  static LinearGradient optimizedGradient({
    required Color primaryColor,
    bool isDark = false,
    double opacity = 0.1,
  }) {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        primaryColor.withAlpha((opacity * 255).round().clamp(0, 255)),
        Colors.transparent,
      ],
    );
  }

  // إعدادات الحدود المحسنة
  static const BorderRadius smallRadius = BorderRadius.all(Radius.circular(8));
  static const BorderRadius mediumRadius =
      BorderRadius.all(Radius.circular(12));
  static const BorderRadius largeRadius = BorderRadius.all(Radius.circular(20));

  // إعدادات الحشو المحسنة
  static const EdgeInsets smallPadding = EdgeInsets.all(8);
  static const EdgeInsets mediumPadding = EdgeInsets.all(16);
  static const EdgeInsets largePadding = EdgeInsets.all(24);

  // إعدادات الهوامش المحسنة
  static const EdgeInsets smallMargin = EdgeInsets.all(4);
  static const EdgeInsets mediumMargin = EdgeInsets.all(8);
  static const EdgeInsets largeMargin = EdgeInsets.all(16);

  // إعدادات الأيقونات المحسنة
  static const double smallIconSize = 16.0;
  static const double mediumIconSize = 24.0;
  static const double largeIconSize = 32.0;

  // إعدادات النصوص المحسنة
  static const TextStyle optimizedTextStyle = TextStyle(
    fontWeight: FontWeight.normal,
    height: 1.4,
  );

  // إعدادات الرسوم المتحركة المحسنة
  static Widget optimizedFadeIn({
    required Widget child,
    required int index,
    Duration? duration,
    Curve? curve,
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration ?? Duration(milliseconds: 100 + (index * 20)),
      tween: Tween(begin: 0.0, end: 1.0),
      curve: curve ?? fastCurve,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 5 * (1 - value)),
          child: Opacity(opacity: value, child: child),
        );
      },
      child: child,
    );
  }

  // إعدادات البطاقات المحسنة
  static Widget optimizedCard({
    required Widget child,
    required BuildContext context,
    EdgeInsets? margin,
    EdgeInsets? padding,
    Color? backgroundColor,
    BorderRadius? borderRadius,
  }) {
    final theme = Theme.of(context);
    return Container(
      margin: margin ?? mediumMargin,
      padding: padding ?? mediumPadding,
      decoration: BoxDecoration(
        color: backgroundColor ?? theme.cardColor,
        borderRadius: borderRadius ?? mediumRadius,
        boxShadow: optimizedShadow(context: context),
      ),
      child: child,
    );
  }

  // إعدادات القوائم المحسنة
  static Widget optimizedListView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    ScrollController? controller,
    ScrollPhysics? physics,
    EdgeInsets? padding,
    double? cacheExtent,
  }) {
    return ListView.builder(
      controller: controller,
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      physics: physics ?? optimizedScrollPhysics,
      padding: padding ?? optimizedListPadding,
      cacheExtent: cacheExtent ?? optimizedCacheExtent,
      addAutomaticKeepAlives: false, // تحسين الذاكرة
      addRepaintBoundaries: true, // تحسين الرسم
    );
  }

  // إعدادات الشبكات المحسنة
  static Widget optimizedGridView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    required int crossAxisCount,
    ScrollController? controller,
    ScrollPhysics? physics,
    EdgeInsets? padding,
    double childAspectRatio = 1.0,
  }) {
    return GridView.builder(
      controller: controller,
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: childAspectRatio,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      physics: physics ?? optimizedScrollPhysics,
      padding: padding ?? optimizedListPadding,
      cacheExtent: optimizedCacheExtent,
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: true,
    );
  }

  // إعدادات الصور المحسنة
  static Widget optimizedImage({
    required String imagePath,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    bool isAsset = true,
  }) {
    if (isAsset) {
      return Image.asset(
        imagePath,
        width: width,
        height: height,
        fit: fit,
        cacheWidth:
            width != null ? (width * optimizedImageScale).round() : null,
        cacheHeight:
            height != null ? (height * optimizedImageScale).round() : null,
        filterQuality: optimizedImageQuality,
        errorBuilder: (context, error, stackTrace) =>
            _buildErrorWidget(width, height),
      );
    } else {
      return Image.network(
        imagePath,
        width: width,
        height: height,
        fit: fit,
        cacheWidth:
            width != null ? (width * optimizedImageScale).round() : null,
        cacheHeight:
            height != null ? (height * optimizedImageScale).round() : null,
        filterQuality: optimizedImageQuality,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return _buildLoadingWidget(width, height);
        },
        errorBuilder: (context, error, stackTrace) =>
            _buildErrorWidget(width, height),
      );
    }
  }

  // بناء ويدجت الخطأ
  static Widget _buildErrorWidget(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      color: Colors.grey[300],
      child: const Icon(Icons.error, color: Colors.grey),
    );
  }

  // بناء ويدجت التحميل
  static Widget _buildLoadingWidget(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      color: Colors.grey[100],
      child: const Center(
        child: SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      ),
    );
  }

  // إعدادات الأزرار المحسنة
  static Widget optimizedButton({
    required VoidCallback? onPressed,
    required Widget child,
    Color? backgroundColor,
    Color? foregroundColor,
    EdgeInsets? padding,
    BorderRadius? borderRadius,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor,
        foregroundColor: foregroundColor,
        padding:
            padding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: borderRadius ?? smallRadius,
        ),
        elevation: 2,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      child: child,
    );
  }

  // إعدادات الانتقالات المحسنة
  static PageRouteBuilder<T> optimizedPageTransition<T>({
    required Widget page,
    Duration? duration,
    Curve? curve,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration ?? mediumTransition,
      reverseTransitionDuration: Duration(
        milliseconds:
            (duration?.inMilliseconds ?? mediumTransition.inMilliseconds) - 50,
      ),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation.drive(
            Tween(begin: 0.0, end: 1.0).chain(
              CurveTween(curve: curve ?? fastCurve),
            ),
          ),
          child: child,
        );
      },
    );
  }

  // إعدادات الألوان المحسنة
  static Color optimizedColor(Color color, double opacity) {
    return color.withAlpha((opacity * 255).round().clamp(0, 255));
  }

  // إعدادات التخزين المؤقت
  static const Map<String, dynamic> cacheSettings = {
    'maxMemoryCache': 50 * 1024 * 1024, // 50 ميجابايت
    'maxDiskCache': 100 * 1024 * 1024, // 100 ميجابايت
    'maxCacheAge': 7 * 24 * 60 * 60, // 7 أيام
    'maxCacheObjects': 500,
  };

  // إعدادات الأداء العامة
  static const Map<String, dynamic> performanceSettings = {
    'enableRepaintBoundaries': true,
    'enableAutomaticKeepAlives': false,
    'enableSemanticIndexes': false,
    'optimizeScrolling': true,
    'optimizeAnimations': true,
    'optimizeImages': true,
  };
}
