import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:azkary/screens/main_screen.dart';
import 'package:azkary/widgets/app_logo.dart';
import 'package:azkary/widgets/performance_optimized_splash_background.dart';
import 'package:azkary/widgets/animated_welcome_text.dart';
import 'package:azkary/widgets/enhanced_loading_indicator.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  // متغيرات للرسوم المتحركة
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _slideAnimation;
  late Animation<double> _rotateAnimation;

  // متغيرات للنص
  late Animation<double> _textFadeAnimation;

  // متغير للتحقق من اكتمال التحميل
  bool _isLoading = true;
  double _loadingProgress = 0.0;

  @override
  void initState() {
    super.initState();

    // تهيئة وحدة التحكم بالرسوم المتحركة - محسن للسلاسة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000), // تقليل المدة للسرعة
    );

    // تهيئة الرسوم المتحركة للشعار - محسنة
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOutCubic),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.7, curve: Curves.elasticOut),
      ),
    );

    _slideAnimation = Tween<double>(begin: -80.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOutCubic),
      ),
    );

    _rotateAnimation = Tween<double>(begin: -0.1, end: 0.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.3, 0.8, curve: Curves.easeOutBack),
      ),
    );

    // تهيئة الرسوم المتحركة للنص - محسنة
    _textFadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.4, 0.9, curve: Curves.easeOutCubic),
      ),
    );

    // بدء الرسوم المتحركة
    _animationController.forward();

    // محاكاة عملية التحميل
    _simulateLoading();

    // الانتقال إلى الشاشة الرئيسية - محسن للسلاسة والأداء
    Timer(const Duration(milliseconds: 2200), () {
      if (!mounted) return;

      // انتقال سلس ومحسن مع تأثيرات جميلة
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder:
              (context, animation, secondaryAnimation) =>
                  const MainScreen(showDailyAyah: true),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            // انتقال متقدم مع تأثيرات متعددة
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0.0, 0.1),
                end: Offset.zero,
              ).animate(
                CurvedAnimation(parent: animation, curve: Curves.easeOutCubic),
              ),
              child: FadeTransition(
                opacity: animation,
                child: ScaleTransition(
                  scale: Tween<double>(begin: 0.95, end: 1.0).animate(
                    CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeOutBack,
                    ),
                  ),
                  child: child,
                ),
              ),
            );
          },
          transitionDuration: const Duration(milliseconds: 400),
        ),
      );
    });
  }

  // محاكاة عملية التحميل بطريقة سلسة ومحسنة
  void _simulateLoading() {
    // تحميل متدرج وسلس
    const steps = [
      (delay: 100, progress: 0.2),
      (delay: 200, progress: 0.4),
      (delay: 250, progress: 0.6),
      (delay: 200, progress: 0.8),
      (delay: 150, progress: 0.95),
      (delay: 100, progress: 1.0),
    ];

    int currentStep = 0;

    void executeNextStep() {
      if (currentStep >= steps.length || !mounted) return;

      final step = steps[currentStep];
      Future.delayed(Duration(milliseconds: step.delay), () {
        if (!mounted) return;
        setState(() {
          _loadingProgress = step.progress;
          if (step.progress >= 1.0) {
            _isLoading = false;
          }
        });

        currentStep++;
        executeNextStep();
      });
    }

    executeNextStep();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // تعيين وضع الشاشة للعرض بشكل طبيعي مع إظهار شريط الإشعارات
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // ألوان التطبيق
    final primaryColor = const Color(0xFF2E7D32);
    final textColor = isDarkMode ? Colors.white : const Color(0xFF2E7D32);

    return Scaffold(
      body: PerformanceOptimizedSplashBackground(
        isDarkMode: isDarkMode,
        child: SafeArea(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // الشعار مع الرسوم المتحركة المحسنة
                AnimatedBuilder(
                  animation: _animationController,
                  builder: (context, child) {
                    return Transform.translate(
                      offset: Offset(0, _slideAnimation.value),
                      child: Transform.rotate(
                        angle: _rotateAnimation.value,
                        child: Transform.scale(
                          scale: _scaleAnimation.value,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: Container(
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: primaryColor.withValues(alpha: 0.3),
                                    blurRadius: 30,
                                    spreadRadius: 10,
                                  ),
                                ],
                              ),
                              child: AppLogo(size: 160),
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),

                const SizedBox(height: 40),

                // نص الترحيب المحسن
                FadeTransition(
                  opacity: _textFadeAnimation,
                  child: AnimatedWelcomeText(
                    title: 'أذكاري',
                    subtitle: 'تطبيق الأذكار اليومية',
                    textColor: textColor,
                    isDarkMode: isDarkMode,
                  ),
                ),

                const SizedBox(height: 60),

                // مؤشر التحميل المحسن
                if (_isLoading)
                  FadeTransition(
                    opacity: _textFadeAnimation,
                    child: EnhancedLoadingIndicator(
                      progress: _loadingProgress,
                      primaryColor: primaryColor,
                      backgroundColor: textColor,
                      isDarkMode: isDarkMode,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
