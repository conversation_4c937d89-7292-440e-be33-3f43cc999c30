import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/mushaf_page.dart';
import '../utils/mushaf_image_cache.dart';
import '../utils/logger.dart';

/// ويدجت عرض صفحة واحدة من المصحف الشريف
class MushafPageViewer extends StatefulWidget {
  final MushafPage page;
  final MushafViewSettings settings;
  final VoidCallback? onTap;
  final bool enableZoom;

  const MushafPageViewer({
    super.key,
    required this.page,
    required this.settings,
    this.onTap,
    this.enableZoom = true,
  });

  @override
  State<MushafPageViewer> createState() => _MushafPageViewerState();
}

class _MushafPageViewerState extends State<MushafPageViewer> {
  final TransformationController _transformationController =
      TransformationController();
  final MushafImageCache _imageCache = MushafImageCache();
  MushafPageState? _pageState;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPage();
  }

  @override
  void didUpdateWidget(MushafPageViewer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.page.pageNumber != widget.page.pageNumber) {
      _loadPage();
    }
  }

  @override
  void dispose() {
    _transformationController.dispose();
    super.dispose();
  }

  /// تحميل الصفحة
  Future<void> _loadPage() async {
    try {
      setState(() => _isLoading = true);

      final pageState = await _imageCache.getPage(widget.page);

      if (mounted) {
        setState(() {
          _pageState = pageState;
          _isLoading = false;
        });
      }
    } catch (e) {
      AppLogger.error('خطأ في تحميل صفحة المصحف: $e');
      if (mounted) {
        setState(() {
          _pageState = MushafPageState.error(widget.page, e.toString());
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode =
        widget.settings.nightMode || theme.brightness == Brightness.dark;

    return RepaintBoundary(
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: isDarkMode ? const Color(0xFF1A1A1A) : const Color(0xFFFFFDF5),
        ),
        child: Stack(
          children: [
            // الصورة الرئيسية
            _buildMainImage(theme, isDarkMode),

            // معلومات الصفحة (اختيارية)
            if (widget.settings.showPageInfo) _buildPageInfo(theme, isDarkMode),

            // مؤشر التحميل
            if (_isLoading) _buildLoadingIndicator(isDarkMode),
          ],
        ),
      ),
    );
  }

  /// بناء الصورة الرئيسية
  Widget _buildMainImage(ThemeData theme, bool isDarkMode) {
    if (_pageState == null) {
      return const Center(child: CircularProgressIndicator());
    }

    switch (_pageState!.state) {
      case MushafPageLoadingState.loaded:
      case MushafPageLoadingState.cached:
        return _buildImageViewer();

      case MushafPageLoadingState.loading:
        return _buildLoadingState(isDarkMode);

      case MushafPageLoadingState.error:
        return _buildErrorState(theme, isDarkMode);

      default:
        return _buildLoadingState(isDarkMode);
    }
  }

  /// بناء عارض الصورة مع إمكانية التكبير
  Widget _buildImageViewer() {
    final imageWidget = _buildOptimizedImage();

    if (!widget.enableZoom) {
      return GestureDetector(onTap: widget.onTap, child: imageWidget);
    }

    return InteractiveViewer(
      transformationController: _transformationController,
      minScale: 0.8,
      maxScale: 3.0,
      constrained: false,
      child: GestureDetector(
        onTap: widget.onTap,
        onDoubleTap: () {
          // تكبير/تصغير عند النقر المزدوج
          if (_transformationController.value.getMaxScaleOnAxis() > 1.0) {
            _transformationController.value = Matrix4.identity();
          } else {
            _transformationController.value = Matrix4.identity()..scale(2.0);
          }
          HapticFeedback.lightImpact();
        },
        child: imageWidget,
      ),
    );
  }

  /// بناء الصورة المحسنة
  Widget _buildOptimizedImage() {
    final page = _pageState!.page;

    // إذا كانت الصورة محفوظة محلياً
    if (page.isDownloaded && page.localPath != null) {
      return Image.file(
        File(page.localPath!),
        fit: BoxFit.contain,
        width: double.infinity,
        height: double.infinity,
        filterQuality: FilterQuality.high,
        errorBuilder: (context, error, stackTrace) {
          AppLogger.error('خطأ في تحميل الصورة المحلية: $error');
          return _buildNetworkImage();
        },
      );
    }

    return _buildNetworkImage();
  }

  /// بناء صورة الشبكة
  Widget _buildNetworkImage() {
    return Image.network(
      widget.page.imageUrl,
      fit: BoxFit.contain,
      width: double.infinity,
      height: double.infinity,
      filterQuality: FilterQuality.high,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;

        final progress =
            loadingProgress.expectedTotalBytes != null
                ? loadingProgress.cumulativeBytesLoaded /
                    loadingProgress.expectedTotalBytes!
                : null;

        return _buildLoadingStateWithProgress(
          widget.settings.nightMode,
          progress,
        );
      },
      errorBuilder: (context, error, stackTrace) {
        AppLogger.error(
          'خطأ في تحميل صورة المصحف من ${widget.page.imageUrl}: $error',
        );
        return _buildErrorStateWithRetry(
          Theme.of(context),
          widget.settings.nightMode,
          error.toString(),
        );
      },
      headers: const {
        'User-Agent': 'Mozilla/5.0 (Android; Mobile) QuranApp/1.0',
        'Accept': 'image/png,image/jpeg,image/*,*/*;q=0.8',
        'Cache-Control': 'max-age=86400', // تخزين مؤقت ليوم واحد
      },
    );
  }

  /// بناء حالة التحميل مع شريط التقدم
  Widget _buildLoadingStateWithProgress(bool isDarkMode, double? progress) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            value: progress,
            valueColor: AlwaysStoppedAnimation<Color>(
              isDarkMode ? Colors.amber[400]! : Colors.brown[600]!,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل صفحة ${_convertToArabicNumbers(widget.page.pageNumber)}...',
            style: TextStyle(
              fontSize: 16,
              color: isDarkMode ? Colors.white70 : Colors.black87,
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),
          if (progress != null) ...[
            const SizedBox(height: 8),
            Text(
              '${(progress * 100).toStringAsFixed(1)}%',
              style: TextStyle(
                fontSize: 14,
                color: isDarkMode ? Colors.white60 : Colors.black54,
                fontFamily: 'Amiri',
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState(bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              isDarkMode ? Colors.amber[400]! : Colors.brown[600]!,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل صفحة ${_convertToArabicNumbers(widget.page.pageNumber)}...',
            style: TextStyle(
              fontSize: 16,
              color: isDarkMode ? Colors.white70 : Colors.black87,
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),
          if (_pageState?.downloadProgress != null) ...[
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: _pageState!.downloadProgress! / 100,
              backgroundColor: isDarkMode ? Colors.grey[700] : Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                isDarkMode ? Colors.amber[400]! : Colors.brown[600]!,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${_pageState!.downloadProgress!.toStringAsFixed(1)}%',
              style: TextStyle(
                fontSize: 14,
                color: isDarkMode ? Colors.white60 : Colors.black54,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء حالة الخطأ مع إعادة المحاولة
  Widget _buildErrorStateWithRetry(
    ThemeData theme,
    bool isDarkMode,
    String errorMessage,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: isDarkMode ? Colors.red[300] : Colors.red[600],
          ),
          const SizedBox(height: 16),
          Text(
            'خطأ في تحميل الصفحة ${_convertToArabicNumbers(widget.page.pageNumber)}',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.black87,
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              'تأكد من الاتصال بالإنترنت وحاول مرة أخرى',
              style: TextStyle(
                fontSize: 14,
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                fontFamily: 'Amiri',
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 4),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              'رابط الصورة: ${widget.page.imageUrl}',
              style: TextStyle(
                fontSize: 10,
                color: isDarkMode ? Colors.grey[500] : Colors.grey[500],
                fontFamily: 'monospace',
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () {
              // إعادة تحميل الصفحة
              setState(() {
                _pageState = null;
                _isLoading = true;
              });
              _loadPage();
            },
            icon: const Icon(Icons.refresh),
            label: const Text(
              'إعادة المحاولة',
              style: TextStyle(fontFamily: 'Amiri'),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor:
                  isDarkMode ? Colors.amber[400] : Colors.brown[600],
              foregroundColor: isDarkMode ? Colors.black : Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState(ThemeData theme, bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: isDarkMode ? Colors.red[300] : Colors.red[600],
          ),
          const SizedBox(height: 16),
          Text(
            'خطأ في تحميل الصفحة ${_convertToArabicNumbers(widget.page.pageNumber)}',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.black87,
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          if (_pageState?.errorMessage != null)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _pageState!.errorMessage!,
                style: TextStyle(
                  fontSize: 14,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  fontFamily: 'Amiri',
                ),
                textAlign: TextAlign.center,
              ),
            ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _loadPage,
            icon: const Icon(Icons.refresh),
            label: const Text(
              'إعادة المحاولة',
              style: TextStyle(fontFamily: 'Amiri'),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor:
                  isDarkMode ? Colors.amber[400] : Colors.brown[600],
              foregroundColor: isDarkMode ? Colors.black : Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء معلومات الصفحة
  Widget _buildPageInfo(ThemeData theme, bool isDarkMode) {
    return Positioned(
      top: 16,
      left: 16,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: (isDarkMode ? Colors.black : Colors.white).withValues(
            alpha: 0.9,
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.menu_book,
                  size: 16,
                  color: isDarkMode ? Colors.amber[400] : Colors.brown[600],
                ),
                const SizedBox(width: 6),
                Text(
                  'صفحة ${_convertToArabicNumbers(widget.page.pageNumber)}',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black87,
                    fontFamily: 'Amiri',
                  ),
                ),
              ],
            ),
            if (widget.page.surahInfo.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                widget.page.surahInfo,
                style: TextStyle(
                  fontSize: 12,
                  color: isDarkMode ? Colors.grey[300] : Colors.grey[600],
                  fontFamily: 'Amiri',
                ),
              ),
            ],
            const SizedBox(height: 4),
            Text(
              widget.page.juzNameArabic,
              style: TextStyle(
                fontSize: 12,
                color: isDarkMode ? Colors.grey[300] : Colors.grey[600],
                fontFamily: 'Amiri',
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء مؤشر التحميل
  Widget _buildLoadingIndicator(bool isDarkMode) {
    return Positioned(
      bottom: 16,
      right: 16,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(8),
        ),
        child: SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              isDarkMode ? Colors.amber[400]! : Colors.brown[600]!,
            ),
          ),
        ),
      ),
    );
  }

  /// تحويل الأرقام إلى العربية
  String _convertToArabicNumbers(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      final digitInt = int.tryParse(digit);
      return digitInt != null ? arabicNumbers[digitInt] : digit;
    }).join();
  }
}
