import 'dart:async';
import 'package:audioplayers/audioplayers.dart';
import '../models/quran_model.dart';
import '../services/new_quran_api_service.dart';
import '../utils/logger.dart';

/// حالات مشغل الصوت
enum AudioPlayerState {
  stopped,
  playing,
  paused,
  loading,
  error,
}

/// مشغل الصوتيات المتكامل للقرآن الكريم
class IntegratedAudioPlayer {
  static final IntegratedAudioPlayer _instance = IntegratedAudioPlayer._internal();
  factory IntegratedAudioPlayer() => _instance;
  IntegratedAudioPlayer._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();
  final NewQuranApiService _apiService = NewQuranApiService();
  
  // حالة المشغل
  AudioPlayerState _state = AudioPlayerState.stopped;
  QuranAudioTrack? _currentTrack;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  double _volume = 1.0;
  
  // Controllers للأحداث
  final StreamController<AudioPlayerState> _stateController = StreamController<AudioPlayerState>.broadcast();
  final StreamController<Duration> _positionController = StreamController<Duration>.broadcast();
  final StreamController<Duration> _durationController = StreamController<Duration>.broadcast();
  final StreamController<QuranAudioTrack?> _trackController = StreamController<QuranAudioTrack?>.broadcast();
  final StreamController<double> _volumeController = StreamController<double>.broadcast();

  // Streams للاستماع للأحداث
  Stream<AudioPlayerState> get stateStream => _stateController.stream;
  Stream<Duration> get positionStream => _positionController.stream;
  Stream<Duration> get durationStream => _durationController.stream;
  Stream<QuranAudioTrack?> get trackStream => _trackController.stream;
  Stream<double> get volumeStream => _volumeController.stream;

  // Getters للحالة الحالية
  AudioPlayerState get state => _state;
  QuranAudioTrack? get currentTrack => _currentTrack;
  Duration get currentPosition => _currentPosition;
  Duration get totalDuration => _totalDuration;
  double get volume => _volume;
  bool get isPlaying => _state == AudioPlayerState.playing;
  bool get isPaused => _state == AudioPlayerState.paused;
  bool get isLoading => _state == AudioPlayerState.loading;
  bool get isStopped => _state == AudioPlayerState.stopped;

  /// تهيئة المشغل
  Future<void> initialize() async {
    try {
      AppLogger.info('🎵 تهيئة مشغل الصوتيات المتكامل...');

      // الاستماع لأحداث المشغل
      _audioPlayer.onPlayerStateChanged.listen((PlayerState state) {
        switch (state) {
          case PlayerState.playing:
            _updateState(AudioPlayerState.playing);
            break;
          case PlayerState.paused:
            _updateState(AudioPlayerState.paused);
            break;
          case PlayerState.stopped:
            _updateState(AudioPlayerState.stopped);
            break;
          case PlayerState.completed:
            _updateState(AudioPlayerState.stopped);
            _onTrackCompleted();
            break;
          case PlayerState.disposed:
            _updateState(AudioPlayerState.stopped);
            break;
        }
      });

      // الاستماع لموقع التشغيل
      _audioPlayer.onPositionChanged.listen((Duration position) {
        _currentPosition = position;
        _positionController.add(position);
      });

      // الاستماع لمدة التسجيل
      _audioPlayer.onDurationChanged.listen((Duration duration) {
        _totalDuration = duration;
        _durationController.add(duration);
      });

      AppLogger.info('✅ تم تهيئة مشغل الصوتيات المتكامل بنجاح');
    } catch (e) {
      AppLogger.error('❌ خطأ في تهيئة مشغل الصوتيات: $e');
      rethrow;
    }
  }

  /// تشغيل تسجيل صوتي
  Future<void> playTrack(QuranAudioTrack track) async {
    try {
      AppLogger.info('▶️ تشغيل: ${track.reciter.fullName} - السورة ${track.surahNumber}');
      
      _updateState(AudioPlayerState.loading);
      _currentTrack = track;
      _trackController.add(track);

      // التحقق من صحة الرابط
      if (!track.isValidUrl) {
        throw Exception('رابط التسجيل غير صالح');
      }

      // تشغيل التسجيل
      await _audioPlayer.play(UrlSource(track.url));
      
      AppLogger.info('✅ تم بدء تشغيل التسجيل');
    } catch (e) {
      AppLogger.error('❌ خطأ في تشغيل التسجيل: $e');
      _updateState(AudioPlayerState.error);
      rethrow;
    }
  }

  /// تشغيل سورة بقارئ محدد
  Future<void> playSurah(int surahNumber, {int? reciterId}) async {
    try {
      AppLogger.info('🔄 تحميل تسجيلات السورة $surahNumber...');
      
      final audioTracks = await _apiService.getSurahAudio(surahNumber);
      
      if (audioTracks.isEmpty) {
        throw Exception('لا توجد تسجيلات صوتية متاحة لهذه السورة');
      }

      QuranAudioTrack selectedTrack;
      
      if (reciterId != null) {
        // البحث عن القارئ المحدد
        final reciterTracks = audioTracks.where((track) => track.reciter.id == reciterId).toList();
        if (reciterTracks.isEmpty) {
          throw Exception('لا توجد تسجيلات لهذا القارئ في هذه السورة');
        }
        selectedTrack = reciterTracks.first;
      } else {
        // استخدام أول تسجيل متاح
        selectedTrack = audioTracks.first;
      }

      await playTrack(selectedTrack);
    } catch (e) {
      AppLogger.error('❌ خطأ في تشغيل السورة: $e');
      rethrow;
    }
  }

  /// إيقاف التشغيل مؤقتاً
  Future<void> pause() async {
    try {
      await _audioPlayer.pause();
      AppLogger.info('⏸️ تم إيقاف التشغيل مؤقتاً');
    } catch (e) {
      AppLogger.error('❌ خطأ في إيقاف التشغيل: $e');
      rethrow;
    }
  }

  /// استئناف التشغيل
  Future<void> resume() async {
    try {
      await _audioPlayer.resume();
      AppLogger.info('▶️ تم استئناف التشغيل');
    } catch (e) {
      AppLogger.error('❌ خطأ في استئناف التشغيل: $e');
      rethrow;
    }
  }

  /// إيقاف التشغيل
  Future<void> stop() async {
    try {
      await _audioPlayer.stop();
      _currentTrack = null;
      _currentPosition = Duration.zero;
      _totalDuration = Duration.zero;
      _trackController.add(null);
      AppLogger.info('⏹️ تم إيقاف التشغيل');
    } catch (e) {
      AppLogger.error('❌ خطأ في إيقاف التشغيل: $e');
      rethrow;
    }
  }

  /// الانتقال إلى موقع محدد
  Future<void> seek(Duration position) async {
    try {
      await _audioPlayer.seek(position);
      AppLogger.info('⏭️ تم الانتقال إلى ${_formatDuration(position)}');
    } catch (e) {
      AppLogger.error('❌ خطأ في الانتقال: $e');
      rethrow;
    }
  }

  /// تغيير مستوى الصوت (0.0 - 1.0)
  Future<void> setVolume(double volume) async {
    try {
      final clampedVolume = volume.clamp(0.0, 1.0);
      await _audioPlayer.setVolume(clampedVolume);
      _volume = clampedVolume;
      _volumeController.add(_volume);
      AppLogger.info('🔊 تم تغيير مستوى الصوت إلى ${(clampedVolume * 100).round()}%');
    } catch (e) {
      AppLogger.error('❌ خطأ في تغيير مستوى الصوت: $e');
      rethrow;
    }
  }

  /// الحصول على القراء المتاحين لسورة
  Future<List<QuranReciter>> getAvailableReciters(int surahNumber) async {
    try {
      final tracks = await _apiService.getSurahAudio(surahNumber);
      final reciters = tracks.map((track) => track.reciter).toList();
      
      // إزالة المكررات
      final uniqueReciters = <QuranReciter>[];
      final seenIds = <int>{};
      
      for (final reciter in reciters) {
        if (!seenIds.contains(reciter.id)) {
          uniqueReciters.add(reciter);
          seenIds.add(reciter.id);
        }
      }
      
      return uniqueReciters;
    } catch (e) {
      AppLogger.error('❌ خطأ في الحصول على قائمة القراء: $e');
      rethrow;
    }
  }

  /// تبديل حالة التشغيل
  Future<void> togglePlayPause() async {
    if (isPlaying) {
      await pause();
    } else if (isPaused) {
      await resume();
    }
  }

  /// التقدم للأمام (10 ثوان)
  Future<void> seekForward() async {
    final newPosition = _currentPosition + const Duration(seconds: 10);
    if (newPosition <= _totalDuration) {
      await seek(newPosition);
    }
  }

  /// التراجع للخلف (10 ثوان)
  Future<void> seekBackward() async {
    final newPosition = _currentPosition - const Duration(seconds: 10);
    if (newPosition >= Duration.zero) {
      await seek(newPosition);
    } else {
      await seek(Duration.zero);
    }
  }

  /// تحديث حالة المشغل
  void _updateState(AudioPlayerState newState) {
    if (_state != newState) {
      _state = newState;
      _stateController.add(newState);
      AppLogger.info('🎵 حالة المشغل: ${_getStateText(newState)}');
    }
  }

  /// عند انتهاء التسجيل
  void _onTrackCompleted() {
    AppLogger.info('✅ انتهى تشغيل التسجيل');
    // يمكن إضافة منطق للانتقال للتسجيل التالي هنا
  }

  /// تنسيق المدة الزمنية
  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }

  /// الحصول على نص حالة المشغل
  String _getStateText(AudioPlayerState state) {
    switch (state) {
      case AudioPlayerState.stopped:
        return 'متوقف';
      case AudioPlayerState.playing:
        return 'يشغل';
      case AudioPlayerState.paused:
        return 'متوقف مؤقتاً';
      case AudioPlayerState.loading:
        return 'يحمل';
      case AudioPlayerState.error:
        return 'خطأ';
    }
  }

  /// تنظيف الموارد
  Future<void> dispose() async {
    try {
      await _audioPlayer.dispose();
      await _stateController.close();
      await _positionController.close();
      await _durationController.close();
      await _trackController.close();
      await _volumeController.close();
      AppLogger.info('🗑️ تم تنظيف موارد مشغل الصوتيات');
    } catch (e) {
      AppLogger.error('❌ خطأ في تنظيف موارد الصوتيات: $e');
    }
  }

  /// الحصول على معلومات المشغل
  Map<String, dynamic> getPlayerInfo() {
    return {
      'name': 'Integrated Audio Player',
      'version': '1.0',
      'current_state': _getStateText(_state),
      'current_track': _currentTrack?.reciter.fullName,
      'position': _formatDuration(_currentPosition),
      'duration': _formatDuration(_totalDuration),
      'volume': '${(_volume * 100).round()}%',
      'features': 'Play, Pause, Stop, Seek, Volume, Forward, Backward',
    };
  }
}
