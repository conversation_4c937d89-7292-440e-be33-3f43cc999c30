import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import '../models/mushaf_page_model.dart';

/// ويدجت لعرض صفحة المصحف بالتخطيط التقليدي
class MushafPageWidget extends StatelessWidget {
  final MushafPage page;
  final int? highlightedAyahNumber;
  final Function(int ayahNumber)? onAyahTap;

  const MushafPageWidget({
    super.key,
    required this.page,
    this.highlightedAyahNumber,
    this.onAyahTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1A1A1A) : const Color(0xFFFFFDF5),
        border: Border.all(
          color: isDarkMode ? Colors.grey[700]! : Colors.brown[200]!,
          width: 2,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            // رأس الصفحة - اسم السورة ورقم الصفحة
            _buildPageHeader(theme, isDarkMode),

            const SizedBox(height: 20),

            // خط زخرفي علوي
            _buildDecorativeLine(theme, isDarkMode),

            const SizedBox(height: 15),

            // محتوى الصفحة - الآيات
            Expanded(child: _buildPageContent(theme, isDarkMode)),

            const SizedBox(height: 15),

            // خط زخرفي سفلي
            _buildDecorativeLine(theme, isDarkMode),

            const SizedBox(height: 10),

            // تذييل الصفحة - رقم الصفحة
            _buildPageFooter(theme, isDarkMode),
          ],
        ),
      ),
    );
  }

  /// بناء رأس الصفحة
  Widget _buildPageHeader(ThemeData theme, bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: isDarkMode ? Colors.amber[700]! : Colors.brown[600]!,
            width: 1,
          ),
        ),
      ),
      child: Text(
        page.surahName,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: isDarkMode ? Colors.amber[300] : Colors.brown[800],
          fontFamily: 'Amiri',
        ),
        textAlign: TextAlign.center,
        textDirection: TextDirection.rtl,
      ),
    );
  }

  /// بناء خط زخرفي
  Widget _buildDecorativeLine(ThemeData theme, bool isDarkMode) {
    return Container(
      height: 2,
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.transparent,
            isDarkMode ? Colors.amber[700]! : Colors.brown[400]!,
            Colors.transparent,
          ],
        ),
      ),
    );
  }

  /// بناء محتوى الصفحة
  Widget _buildPageContent(ThemeData theme, bool isDarkMode) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: Column(
          children: [
            // محتوى الصفحة القابل للتمرير
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children:
                      page.lines
                          .map(
                            (line) => _buildMushafLine(line, theme, isDarkMode),
                          )
                          .toList(),
                ),
              ),
            ),

            // مساحة إضافية في الأسفل للتصميم
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  /// بناء سطر المصحف
  Widget _buildMushafLine(MushafLine line, ThemeData theme, bool isDarkMode) {
    if (line.isBasmala) {
      return _buildBasmalaLine(line, theme, isDarkMode);
    }

    // التحقق من كون السطر رأس سورة
    if (line.ayahs.isEmpty &&
        (line.text.contains('سورة') || line.text.contains('سُورَة'))) {
      return _buildSurahHeaderLine(line, theme, isDarkMode);
    }

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: RichText(
        textAlign: TextAlign.justify,
        textDirection: TextDirection.rtl,
        text: TextSpan(children: _buildLineSpans(line, theme, isDarkMode)),
      ),
    );
  }

  /// بناء سطر رأس السورة
  Widget _buildSurahHeaderLine(
    MushafLine line,
    ThemeData theme,
    bool isDarkMode,
  ) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 16),
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: BoxDecoration(
        border: Border.symmetric(
          horizontal: BorderSide(
            color: isDarkMode ? Colors.amber[700]! : Colors.brown[400]!,
            width: 2,
          ),
        ),
        gradient: LinearGradient(
          colors: [
            Colors.transparent,
            (isDarkMode ? Colors.amber[700]! : Colors.brown[400]!).withValues(
              alpha: 0.1,
            ),
            Colors.transparent,
          ],
        ),
      ),
      child: Text(
        line.text,
        style: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: isDarkMode ? Colors.amber[200] : Colors.brown[700],
          fontFamily: 'Amiri',
          height: 1.5,
        ),
        textAlign: TextAlign.center,
        textDirection: TextDirection.rtl,
      ),
    );
  }

  /// بناء سطر البسملة
  Widget _buildBasmalaLine(MushafLine line, ThemeData theme, bool isDarkMode) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 20),
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.transparent,
            (isDarkMode ? Colors.amber[700]! : Colors.brown[400]!).withValues(
              alpha: 0.1,
            ),
            Colors.transparent,
          ],
        ),
        border: Border.symmetric(
          horizontal: BorderSide(
            color: isDarkMode ? Colors.amber[700]! : Colors.brown[400]!,
            width: 2,
          ),
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // زخرفة علوية
          Container(
            width: 100,
            height: 2,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.transparent,
                  isDarkMode ? Colors.amber[600]! : Colors.brown[500]!,
                  Colors.transparent,
                ],
              ),
            ),
          ),

          const SizedBox(height: 12),

          // نص البسملة
          Text(
            line.text,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.amber[200] : Colors.brown[700],
              fontFamily: 'Amiri',
              height: 1.8,
              letterSpacing: 1.0,
            ),
            textAlign: TextAlign.center,
            textDirection: TextDirection.rtl,
          ),

          const SizedBox(height: 12),

          // زخرفة سفلية
          Container(
            width: 100,
            height: 2,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.transparent,
                  isDarkMode ? Colors.amber[600]! : Colors.brown[500]!,
                  Colors.transparent,
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء أجزاء النص في السطر
  List<TextSpan> _buildLineSpans(
    MushafLine line,
    ThemeData theme,
    bool isDarkMode,
  ) {
    final spans = <TextSpan>[];
    final text = line.text;
    int currentIndex = 0;

    for (final ayah in line.ayahs) {
      // إضافة النص قبل الآية إذا وجد
      if (ayah.startIndex > currentIndex) {
        spans.add(
          TextSpan(
            text: text.substring(currentIndex, ayah.startIndex),
            style: _getDefaultTextStyle(isDarkMode),
          ),
        );
      }

      // إضافة نص الآية
      final ayahEndIndex = ayah.endIndex == -1 ? text.length : ayah.endIndex;
      final ayahText = text.substring(ayah.startIndex, ayahEndIndex);
      final isHighlighted = highlightedAyahNumber == ayah.ayahNumber;

      spans.add(
        TextSpan(
          text: ayahText,
          style: _getAyahTextStyle(isDarkMode, isHighlighted),
          recognizer:
              onAyahTap != null
                  ? (TapGestureRecognizer()
                    ..onTap = () => onAyahTap!(ayah.ayahNumber))
                  : null,
        ),
      );

      // إضافة رقم الآية إذا كان مطلوباً
      if (ayah.hasAyahNumber) {
        spans.add(
          TextSpan(
            text: ' ﴿${_convertToArabicNumbers(ayah.ayahNumber)}﴾ ',
            style: _getAyahNumberStyle(isDarkMode),
          ),
        );
      }

      currentIndex = ayahEndIndex;
    }

    // إضافة النص المتبقي إذا وجد
    if (currentIndex < text.length) {
      spans.add(
        TextSpan(
          text: text.substring(currentIndex),
          style: _getDefaultTextStyle(isDarkMode),
        ),
      );
    }

    return spans;
  }

  /// الحصول على نمط النص الافتراضي
  TextStyle _getDefaultTextStyle(bool isDarkMode) {
    return TextStyle(
      fontSize: 20,
      height: 2.2,
      fontWeight: FontWeight.w500,
      color: isDarkMode ? Colors.white : Colors.black87,
      fontFamily: 'Amiri',
      letterSpacing: 0.8,
    );
  }

  /// الحصول على نمط نص الآية
  TextStyle _getAyahTextStyle(bool isDarkMode, bool isHighlighted) {
    return TextStyle(
      fontSize: 20,
      height: 2.2,
      fontWeight: FontWeight.w600,
      color:
          isHighlighted
              ? (isDarkMode ? Colors.amber[300] : Colors.brown[700])
              : (isDarkMode ? Colors.white : Colors.black87),
      fontFamily: 'Amiri',
      letterSpacing: 0.8,
      backgroundColor:
          isHighlighted
              ? (isDarkMode
                  ? Colors.amber[700]!.withValues(alpha: 0.3)
                  : Colors.brown[200]!.withValues(alpha: 0.5))
              : null,
    );
  }

  /// الحصول على نمط رقم الآية
  TextStyle _getAyahNumberStyle(bool isDarkMode) {
    return TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.bold,
      color: isDarkMode ? Colors.amber[400] : Colors.brown[600],
      fontFamily: 'Amiri',
    );
  }

  /// بناء تذييل الصفحة
  Widget _buildPageFooter(ThemeData theme, bool isDarkMode) {
    return Text(
      'الصفحة ${_convertToArabicNumbers(page.pageNumber)}',
      style: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        color: isDarkMode ? Colors.amber[400] : Colors.brown[600],
        fontFamily: 'Amiri',
      ),
      textAlign: TextAlign.center,
    );
  }

  /// تحويل الأرقام إلى العربية
  String _convertToArabicNumbers(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      final digitInt = int.tryParse(digit);
      return digitInt != null ? arabicNumbers[digitInt] : digit;
    }).join();
  }
}
