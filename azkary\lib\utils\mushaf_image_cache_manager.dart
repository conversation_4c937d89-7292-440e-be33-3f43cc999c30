import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';
import '../models/mushaf_image_page.dart';

/// مدير التخزين المؤقت المحسن لصور المصحف
class MushafImageCacheManager {
  static final MushafImageCacheManager _instance =
      MushafImageCacheManager._internal();
  factory MushafImageCacheManager() => _instance;
  MushafImageCacheManager._internal();

  static const String _cacheDirectoryName = 'mushaf_images';
  static const String _prefsKey = 'mushaf_cache_info';
  static const int _maxCacheSize = 500 * 1024 * 1024; // 500 MB
  static const int _maxCacheAge = 30; // 30 days

  Directory? _cacheDirectory;
  final Map<int, MushafPageState> _memoryCache = {};
  final Map<int, Future<MushafPageState>> _downloadingPages = {};

  /// تهيئة مدير التخزين المؤقت
  Future<void> initialize() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      _cacheDirectory = Directory('${appDir.path}/$_cacheDirectoryName');

      if (!await _cacheDirectory!.exists()) {
        await _cacheDirectory!.create(recursive: true);
      }

      // تنظيف الملفات القديمة
      await _cleanupOldFiles();

      AppLogger.info('تم تهيئة مدير التخزين المؤقت لصور المصحف');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة مدير التخزين المؤقت: $e');
    }
  }

  /// الحصول على صفحة المصحف مع التخزين المؤقت الذكي
  Future<MushafPageState> getPage(MushafImagePage page) async {
    final pageNumber = page.pageNumber;

    // التحقق من الذاكرة المؤقتة أولاً
    if (_memoryCache.containsKey(pageNumber)) {
      final cachedState = _memoryCache[pageNumber]!;
      if (cachedState.state == MushafPageLoadingState.loaded ||
          cachedState.state == MushafPageLoadingState.cached) {
        return cachedState;
      }
    }

    // التحقق من التحميل الجاري
    if (_downloadingPages.containsKey(pageNumber)) {
      return await _downloadingPages[pageNumber]!;
    }

    // بدء التحميل
    final downloadFuture = _downloadPage(page);
    _downloadingPages[pageNumber] = downloadFuture;

    try {
      final result = await downloadFuture;
      _memoryCache[pageNumber] = result;
      return result;
    } finally {
      _downloadingPages.remove(pageNumber);
    }
  }

  /// تحميل صفحة المصحف
  Future<MushafPageState> _downloadPage(MushafImagePage page) async {
    try {
      // التحقق من وجود الملف محلياً
      final localFile = await _getLocalFile(page.pageNumber);
      if (await localFile.exists()) {
        final updatedPage = page.copyWith(
          isDownloaded: true,
          localPath: localFile.path,
        );
        return MushafPageState.cached(updatedPage);
      }

      // تحميل من الإنترنت
      AppLogger.info(
        'تحميل صفحة المصحف رقم ${page.pageNumber} من: ${page.imageUrl}',
      );

      final response = await http.get(
        Uri.parse(page.imageUrl),
        headers: {
          'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
        },
      );

      if (response.statusCode == 200) {
        // حفظ الملف محلياً
        await localFile.writeAsBytes(response.bodyBytes);

        // تحديث معلومات التخزين المؤقت
        await _updateCacheInfo(page.pageNumber, response.bodyBytes.length);

        final updatedPage = page.copyWith(
          isDownloaded: true,
          localPath: localFile.path,
        );

        AppLogger.info('تم تحميل وحفظ صفحة المصحف رقم ${page.pageNumber}');
        return MushafPageState.loaded(updatedPage);
      } else {
        throw Exception('فشل في تحميل الصورة: ${response.statusCode}');
      }
    } catch (e) {
      AppLogger.error('خطأ في تحميل صفحة المصحف رقم ${page.pageNumber}: $e');
      return MushafPageState.error(page, e.toString());
    }
  }

  /// الحصول على ملف محلي لصفحة معينة
  Future<File> _getLocalFile(int pageNumber) async {
    if (_cacheDirectory == null) {
      await initialize();
    }
    return File('${_cacheDirectory!.path}/page_$pageNumber.jpg');
  }

  /// تحديث معلومات التخزين المؤقت
  Future<void> _updateCacheInfo(int pageNumber, int fileSize) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheInfo = prefs.getStringList(_prefsKey) ?? [];

      final info =
          '$pageNumber:$fileSize:${DateTime.now().millisecondsSinceEpoch}';
      cacheInfo.add(info);

      await prefs.setStringList(_prefsKey, cacheInfo);
    } catch (e) {
      AppLogger.error('خطأ في تحديث معلومات التخزين المؤقت: $e');
    }
  }

  /// تنظيف الملفات القديمة
  Future<void> _cleanupOldFiles() async {
    try {
      if (_cacheDirectory == null || !await _cacheDirectory!.exists()) return;

      final prefs = await SharedPreferences.getInstance();
      final cacheInfo = prefs.getStringList(_prefsKey) ?? [];
      final now = DateTime.now();
      final validInfo = <String>[];
      int totalSize = 0;

      // فحص الملفات وحساب الحجم الإجمالي
      for (final info in cacheInfo) {
        final parts = info.split(':');
        if (parts.length == 3) {
          final pageNumber = int.tryParse(parts[0]);
          final fileSize = int.tryParse(parts[1]);
          final timestamp = int.tryParse(parts[2]);

          if (pageNumber != null && fileSize != null && timestamp != null) {
            final fileDate = DateTime.fromMillisecondsSinceEpoch(timestamp);
            final daysDiff = now.difference(fileDate).inDays;

            final file = await _getLocalFile(pageNumber);
            if (await file.exists() && daysDiff < _maxCacheAge) {
              validInfo.add(info);
              totalSize += fileSize;
            } else {
              // حذف الملف القديم
              try {
                await file.delete();
              } catch (e) {
                AppLogger.error('خطأ في حذف الملف القديم: $e');
              }
            }
          }
        }
      }

      // إذا تجاوز الحجم الحد الأقصى، احذف الملفات الأقدم
      if (totalSize > _maxCacheSize) {
        validInfo.sort((a, b) {
          final timestampA = int.parse(a.split(':')[2]);
          final timestampB = int.parse(b.split(':')[2]);
          return timestampA.compareTo(timestampB);
        });

        while (totalSize > _maxCacheSize && validInfo.isNotEmpty) {
          final oldestInfo = validInfo.removeAt(0);
          final parts = oldestInfo.split(':');
          final pageNumber = int.parse(parts[0]);
          final fileSize = int.parse(parts[1]);

          final file = await _getLocalFile(pageNumber);
          try {
            await file.delete();
            totalSize -= fileSize;
          } catch (e) {
            AppLogger.error('خطأ في حذف الملف: $e');
          }
        }
      }

      // تحديث معلومات التخزين المؤقت
      await prefs.setStringList(_prefsKey, validInfo);

      AppLogger.info(
        'تم تنظيف التخزين المؤقت - الحجم الحالي: ${(totalSize / 1024 / 1024).toStringAsFixed(2)} MB',
      );
    } catch (e) {
      AppLogger.error('خطأ في تنظيف التخزين المؤقت: $e');
    }
  }

  /// تحميل مسبق لصفحات متعددة
  Future<void> preloadPages(List<MushafImagePage> pages) async {
    final futures = pages.map((page) => getPage(page)).toList();
    await Future.wait(futures, eagerError: false);
  }

  /// مسح التخزين المؤقت
  Future<void> clearCache() async {
    try {
      _memoryCache.clear();

      if (_cacheDirectory != null && await _cacheDirectory!.exists()) {
        await _cacheDirectory!.delete(recursive: true);
        await _cacheDirectory!.create(recursive: true);
      }

      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_prefsKey);

      AppLogger.info('تم مسح التخزين المؤقت لصور المصحف');
    } catch (e) {
      AppLogger.error('خطأ في مسح التخزين المؤقت: $e');
    }
  }

  /// الحصول على حجم التخزين المؤقت
  Future<int> getCacheSize() async {
    try {
      if (_cacheDirectory == null || !await _cacheDirectory!.exists()) return 0;

      int totalSize = 0;
      final files = await _cacheDirectory!.list().toList();

      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          totalSize += stat.size;
        }
      }

      return totalSize;
    } catch (e) {
      AppLogger.error('خطأ في حساب حجم التخزين المؤقت: $e');
      return 0;
    }
  }

  /// التحقق من توفر صفحة محلياً
  Future<bool> isPageCached(int pageNumber) async {
    final file = await _getLocalFile(pageNumber);
    return await file.exists();
  }
}
