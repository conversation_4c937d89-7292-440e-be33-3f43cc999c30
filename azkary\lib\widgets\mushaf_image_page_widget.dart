import 'package:flutter/material.dart';
import '../models/mushaf_image_page.dart';
import '../utils/mushaf_image_cache_manager.dart';
import '../utils/logger.dart';

/// ويدجت عرض صفحة واحدة من المصحف التقليدي
class MushafImagePageWidget extends StatefulWidget {
  final MushafImagePage page;
  final VoidCallback? onTap;
  final bool enableZoom;
  final bool showPageInfo;

  const MushafImagePageWidget({
    super.key,
    required this.page,
    this.onTap,
    this.enableZoom = true,
    this.showPageInfo = true,
  });

  @override
  State<MushafImagePageWidget> createState() => _MushafImagePageWidgetState();
}

class _MushafImagePageWidgetState extends State<MushafImagePageWidget> {
  final TransformationController _transformationController =
      TransformationController();
  final MushafImageCacheManager _cacheManager = MushafImageCacheManager();
  MushafPageState? _pageState;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPage();
  }

  @override
  void dispose() {
    _transformationController.dispose();
    super.dispose();
  }

  /// تحميل الصفحة
  Future<void> _loadPage() async {
    try {
      setState(() => _isLoading = true);

      final pageState = await _cacheManager.getPage(widget.page);

      if (mounted) {
        setState(() {
          _pageState = pageState;
          _isLoading = false;
        });
      }
    } catch (e) {
      AppLogger.error('خطأ في تحميل صفحة المصحف: $e');
      if (mounted) {
        setState(() {
          _pageState = MushafPageState.error(widget.page, e.toString());
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1A1A1A) : const Color(0xFFFFFDF5),
      ),
      child: Stack(
        children: [
          // الصورة الرئيسية
          _buildMainImage(theme, isDarkMode),

          // معلومات الصفحة (اختيارية)
          if (widget.showPageInfo) _buildPageInfo(theme, isDarkMode),

          // مؤشر التحميل
          if (_isLoading) _buildLoadingIndicator(),
        ],
      ),
    );
  }

  /// بناء الصورة الرئيسية
  Widget _buildMainImage(ThemeData theme, bool isDarkMode) {
    if (_pageState == null) {
      return const Center(child: CircularProgressIndicator());
    }

    switch (_pageState!.state) {
      case MushafPageLoadingState.loaded:
      case MushafPageLoadingState.cached:
        return _buildImageViewer();

      case MushafPageLoadingState.loading:
        return _buildLoadingState();

      case MushafPageLoadingState.error:
        return _buildErrorState(theme, isDarkMode);

      default:
        return _buildLoadingState();
    }
  }

  /// بناء عارض الصورة مع إمكانية التكبير
  Widget _buildImageViewer() {
    final imageWidget = _buildOptimizedImage();

    if (!widget.enableZoom) {
      return GestureDetector(onTap: widget.onTap, child: imageWidget);
    }

    return InteractiveViewer(
      transformationController: _transformationController,
      minScale: 0.5,
      maxScale: 3.0,
      onInteractionEnd: (details) {
        // إعادة تعيين التكبير عند النقر المزدوج
        if (details.pointerCount == 0) {
          _transformationController.value = Matrix4.identity();
        }
      },
      child: GestureDetector(
        onTap: widget.onTap,
        onDoubleTap: () {
          // تكبير/تصغير عند النقر المزدوج
          if (_transformationController.value.getMaxScaleOnAxis() > 1.0) {
            _transformationController.value = Matrix4.identity();
          } else {
            _transformationController.value = Matrix4.identity()..scale(2.0);
          }
        },
        child: imageWidget,
      ),
    );
  }

  /// بناء الصورة المحسنة
  Widget _buildOptimizedImage() {
    final page = _pageState!.page;

    // إذا كانت الصورة محفوظة محلياً
    if (page.isDownloaded && page.localPath != null) {
      return Image.asset(
        page.localPath!,
        fit: BoxFit.contain,
        width: double.infinity,
        height: double.infinity,
        filterQuality: FilterQuality.high,
        errorBuilder: (context, error, stackTrace) {
          AppLogger.error('خطأ في تحميل الصورة المحلية: $error');
          return _buildNetworkImage();
        },
      );
    }

    return _buildNetworkImage();
  }

  /// بناء صورة الشبكة مع التخزين المؤقت
  Widget _buildNetworkImage() {
    return CachedNetworkImage(
      imageUrl: widget.page.imageUrl,
      fit: BoxFit.contain,
      width: double.infinity,
      height: double.infinity,
      filterQuality: FilterQuality.high,
      placeholder: (context, url) => _buildLoadingState(),
      errorWidget: (context, url, error) {
        AppLogger.error('خطأ في تحميل صورة المصحف: $error');
        return _buildErrorState(
          Theme.of(context),
          Theme.of(context).brightness == Brightness.dark,
        );
      },
      httpHeaders: const {
        'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
      },
    );
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل صفحة ${widget.page.pageNumber}...',
            style: const TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
          if (_pageState?.downloadProgress != null) ...[
            const SizedBox(height: 8),
            LinearProgressIndicator(value: _pageState!.downloadProgress! / 100),
            const SizedBox(height: 8),
            Text(
              '${_pageState!.downloadProgress!.toStringAsFixed(1)}%',
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState(ThemeData theme, bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: isDarkMode ? Colors.red[300] : Colors.red[600],
          ),
          const SizedBox(height: 16),
          Text(
            'خطأ في تحميل الصفحة ${widget.page.pageNumber}',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          if (_pageState?.errorMessage != null)
            Text(
              _pageState!.errorMessage!,
              style: TextStyle(
                fontSize: 14,
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _loadPage,
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// بناء معلومات الصفحة
  Widget _buildPageInfo(ThemeData theme, bool isDarkMode) {
    return Positioned(
      top: 16,
      left: 16,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: (isDarkMode ? Colors.black : Colors.white).withValues(
            alpha: 0.8,
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.menu_book,
              size: 16,
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
            const SizedBox(width: 6),
            Text(
              'صفحة ${_convertToArabicNumbers(widget.page.pageNumber)}',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black87,
                fontFamily: 'Amiri',
              ),
            ),
            if (widget.page.juzNumber != null) ...[
              const SizedBox(width: 8),
              Text(
                'ج${_convertToArabicNumbers(widget.page.juzNumber!)}',
                style: TextStyle(
                  fontSize: 12,
                  color: isDarkMode ? Colors.grey[300] : Colors.grey[600],
                  fontFamily: 'Amiri',
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء مؤشر التحميل
  Widget _buildLoadingIndicator() {
    return Positioned(
      bottom: 16,
      right: 16,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
      ),
    );
  }

  /// تحويل الأرقام إلى العربية
  String _convertToArabicNumbers(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      final digitInt = int.tryParse(digit);
      return digitInt != null ? arabicNumbers[digitInt] : digit;
    }).join();
  }
}
