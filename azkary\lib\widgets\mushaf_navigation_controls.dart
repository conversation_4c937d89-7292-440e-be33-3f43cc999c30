import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// ويدجت أدوات التنقل في المصحف التقليدي
class MushafNavigationControls extends StatelessWidget {
  final int currentPage;
  final int totalPages;
  final VoidCallback? onPreviousPage;
  final VoidCallback? onNextPage;
  final Function(int)? onPageChanged;
  final bool showPageIndicator;
  final bool showNavigationButtons;
  final bool showJumpToPage;

  const MushafNavigationControls({
    super.key,
    required this.currentPage,
    required this.totalPages,
    this.onPreviousPage,
    this.onNextPage,
    this.onPageChanged,
    this.showPageIndicator = true,
    this.showNavigationButtons = true,
    this.showJumpToPage = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: (isDarkMode ? Colors.black : Colors.white).withValues(alpha: 0.9),
        border: Border(
          top: BorderSide(
            color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            // زر الصفحة السابقة
            if (showNavigationButtons) _buildNavigationButton(
              context,
              icon: Icons.chevron_right,
              label: 'السابقة',
              onPressed: currentPage > 1 ? onPreviousPage : null,
              isDarkMode: isDarkMode,
            ),

            // مؤشر الصفحة الحالية
            Expanded(
              child: _buildPageIndicator(context, isDarkMode),
            ),

            // زر الصفحة التالية
            if (showNavigationButtons) _buildNavigationButton(
              context,
              icon: Icons.chevron_left,
              label: 'التالية',
              onPressed: currentPage < totalPages ? onNextPage : null,
              isDarkMode: isDarkMode,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر التنقل
  Widget _buildNavigationButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
    required bool isDarkMode,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed != null ? () {
          HapticFeedback.lightImpact();
          onPressed();
        } : null,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: onPressed != null
                  ? (isDarkMode ? Colors.grey[600]! : Colors.grey[400]!)
                  : (isDarkMode ? Colors.grey[800]! : Colors.grey[200]!),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 20,
                color: onPressed != null
                    ? (isDarkMode ? Colors.white : Colors.black87)
                    : (isDarkMode ? Colors.grey[600] : Colors.grey[400]),
              ),
              const SizedBox(width: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: onPressed != null
                      ? (isDarkMode ? Colors.white : Colors.black87)
                      : (isDarkMode ? Colors.grey[600] : Colors.grey[400]),
                  fontFamily: 'Amiri',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء مؤشر الصفحة
  Widget _buildPageIndicator(BuildContext context, bool isDarkMode) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // رقم الصفحة الحالية
        if (showPageIndicator) ...[
          Text(
            'صفحة ${_convertToArabicNumbers(currentPage)} من ${_convertToArabicNumbers(totalPages)}',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.black87,
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
        ],

        // شريط التقدم
        Row(
          children: [
            Expanded(
              child: LinearProgressIndicator(
                value: currentPage / totalPages,
                backgroundColor: isDarkMode ? Colors.grey[700] : Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  isDarkMode ? Colors.amber[400]! : Colors.brown[600]!,
                ),
                minHeight: 4,
              ),
            ),
            const SizedBox(width: 12),
            
            // زر الانتقال إلى صفحة معينة
            if (showJumpToPage) _buildJumpToPageButton(context, isDarkMode),
          ],
        ),
      ],
    );
  }

  /// بناء زر الانتقال إلى صفحة معينة
  Widget _buildJumpToPageButton(BuildContext context, bool isDarkMode) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _showJumpToPageDialog(context),
        borderRadius: BorderRadius.circular(6),
        child: Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
            border: Border.all(
              color: isDarkMode ? Colors.grey[600]! : Colors.grey[400]!,
              width: 1,
            ),
          ),
          child: Icon(
            Icons.search,
            size: 18,
            color: isDarkMode ? Colors.white : Colors.black87,
          ),
        ),
      ),
    );
  }

  /// عرض حوار الانتقال إلى صفحة معينة
  void _showJumpToPageDialog(BuildContext context) {
    final controller = TextEditingController();
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: isDarkMode ? const Color(0xFF2C2C2C) : Colors.white,
        title: Text(
          'الانتقال إلى صفحة',
          style: TextStyle(
            fontFamily: 'Amiri',
            color: isDarkMode ? Colors.white : Colors.black87,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'أدخل رقم الصفحة (١ - ${_convertToArabicNumbers(totalPages)})',
              style: TextStyle(
                fontSize: 14,
                color: isDarkMode ? Colors.grey[300] : Colors.grey[600],
                fontFamily: 'Amiri',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              keyboardType: TextInputType.number,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
              decoration: InputDecoration(
                hintText: 'رقم الصفحة',
                hintStyle: TextStyle(
                  color: isDarkMode ? Colors.grey[500] : Colors.grey[400],
                  fontFamily: 'Amiri',
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    color: isDarkMode ? Colors.grey[600]! : Colors.grey[400]!,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    color: isDarkMode ? Colors.amber[400]! : Colors.brown[600]!,
                    width: 2,
                  ),
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'إلغاء',
              style: TextStyle(
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                fontFamily: 'Amiri',
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              final pageNumber = int.tryParse(controller.text);
              if (pageNumber != null && pageNumber >= 1 && pageNumber <= totalPages) {
                Navigator.of(context).pop();
                onPageChanged?.call(pageNumber);
                HapticFeedback.mediumImpact();
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'يرجى إدخال رقم صفحة صحيح بين ١ و ${_convertToArabicNumbers(totalPages)}',
                      style: const TextStyle(fontFamily: 'Amiri'),
                    ),
                    backgroundColor: Colors.red[600],
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: isDarkMode ? Colors.amber[400] : Colors.brown[600],
              foregroundColor: isDarkMode ? Colors.black : Colors.white,
            ),
            child: const Text(
              'انتقال',
              style: TextStyle(fontFamily: 'Amiri'),
            ),
          ),
        ],
      ),
    );
  }

  /// تحويل الأرقام إلى العربية
  String _convertToArabicNumbers(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      final digitInt = int.tryParse(digit);
      return digitInt != null ? arabicNumbers[digitInt] : digit;
    }).join();
  }
}
