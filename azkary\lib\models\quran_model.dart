/// نموذج بيانات للسورة القرآنية المحسن
class Surah {
  final int number;
  final String name;
  final String englishName;
  final String englishNameTranslation;
  final int numberOfAyahs;
  final String revelationType;

  // الميزات الجديدة من QuranData API
  final int? wordsCount;
  final int? lettersCount;
  final String? revelationPlace;

  Surah({
    required this.number,
    required this.name,
    required this.englishName,
    required this.englishNameTranslation,
    required this.numberOfAyahs,
    required this.revelationType,
    this.wordsCount,
    this.lettersCount,
    this.revelationPlace,
  });

  /// إنشاء نموذج من بيانات JSON القديمة (للتوافق)
  factory Surah.fromJson(Map<String, dynamic> json) {
    return Surah(
      number: json['number'],
      name: json['name'],
      englishName: json['englishName'],
      englishNameTranslation: json['englishNameTranslation'],
      numberOfAyahs: json['numberOfAyahs'],
      revelationType: json['revelationType'],
      wordsCount: json['wordsCount'],
      lettersCount: json['lettersCount'],
      revelationPlace: json['revelationPlace'],
    );
  }

  /// إنشاء نموذج من QuranSurah الجديد
  factory Surah.fromQuranSurah(dynamic quranSurah) {
    // التحقق من نوع البيانات
    if (quranSurah.runtimeType.toString().contains('QuranSurah')) {
      return Surah(
        number: quranSurah.number,
        name: quranSurah.name.ar,
        englishName: quranSurah.name.en,
        englishNameTranslation: quranSurah.name.transliteration,
        numberOfAyahs: quranSurah.versesCount,
        revelationType:
            quranSurah.revelationPlace.isMeccan ? 'Meccan' : 'Medinan',
        wordsCount: quranSurah.wordsCount,
        lettersCount: quranSurah.lettersCount,
        revelationPlace: quranSurah.revelationPlace.ar,
      );
    }

    // إذا كان Map
    if (quranSurah is Map<String, dynamic>) {
      return Surah.fromJson(quranSurah);
    }

    throw ArgumentError('نوع بيانات غير مدعوم للسورة');
  }

  /// تحويل النموذج إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'name': name,
      'englishName': englishName,
      'englishNameTranslation': englishNameTranslation,
      'numberOfAyahs': numberOfAyahs,
      'revelationType': revelationType,
      'wordsCount': wordsCount,
      'lettersCount': lettersCount,
      'revelationPlace': revelationPlace,
    };
  }

  /// هل السورة مكية؟
  bool get isMeccan =>
      revelationType.toLowerCase().contains('meccan') ||
      (revelationPlace?.contains('مكية') ?? false);

  /// هل السورة مدنية؟
  bool get isMedinan => !isMeccan;

  /// معلومات إضافية عن السورة
  String get detailedInfo {
    final parts = <String>[];
    parts.add('$numberOfAyahs آية');
    if (wordsCount != null) parts.add('$wordsCount كلمة');
    if (lettersCount != null) parts.add('$lettersCount حرف');
    parts.add(isMeccan ? 'مكية' : 'مدنية');
    return parts.join(' • ');
  }
}

/// نموذج بيانات للآية القرآنية المحسن
class Ayah {
  final int number;
  final String text;
  final int numberInSurah;
  final int juz;
  final int page;
  final int hizbQuarter;
  final bool sajda;
  final String? tafsir; // تفسير الآية
  final bool isBismillah; // هل هي البسملة

  // الميزات الجديدة من QuranData API
  final String? englishText; // الترجمة الإنجليزية
  final String? surahName; // اسم السورة (للمرجع)

  Ayah({
    required this.number,
    required this.text,
    required this.numberInSurah,
    required this.juz,
    required this.page,
    required this.hizbQuarter,
    required this.sajda,
    this.tafsir,
    this.isBismillah = false,
    this.englishText,
    this.surahName,
  });

  /// إنشاء نموذج من بيانات JSON القديمة (للتوافق)
  factory Ayah.fromJson(Map<String, dynamic> json) {
    return Ayah(
      number: json['number'],
      text: json['text'],
      numberInSurah: json['numberInSurah'],
      juz: json['juz'],
      page: json['page'],
      hizbQuarter: json['hizbQuarter'],
      sajda: json['sajda'] is bool ? json['sajda'] : false,
      tafsir: json['tafsir'],
      isBismillah: json['isBismillah'] is bool ? json['isBismillah'] : false,
      englishText: json['englishText'],
      surahName: json['surahName'],
    );
  }

  /// إنشاء نموذج من QuranVerse الجديد
  factory Ayah.fromQuranVerse(dynamic quranVerse, {String? surahName}) {
    // التحقق من نوع البيانات
    if (quranVerse.runtimeType.toString().contains('QuranVerse')) {
      return Ayah(
        number: quranVerse.number,
        text: quranVerse.text.ar,
        numberInSurah:
            quranVerse.number, // في API الجديد، number هو رقم الآية في السورة
        juz: quranVerse.juz,
        page: quranVerse.page,
        hizbQuarter: 1, // قيمة افتراضية
        sajda: quranVerse.sajda,
        englishText: quranVerse.text.en,
        surahName: surahName,
        isBismillah:
            quranVerse.number == 1 && quranVerse.text.ar.startsWith('بِسۡمِ'),
      );
    }

    // إذا كان Map
    if (quranVerse is Map<String, dynamic>) {
      return Ayah.fromJson(quranVerse);
    }

    throw ArgumentError('نوع بيانات غير مدعوم للآية');
  }

  /// تحويل النموذج إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'text': text,
      'numberInSurah': numberInSurah,
      'juz': juz,
      'page': page,
      'hizbQuarter': hizbQuarter,
      'sajda': sajda,
      'tafsir': tafsir,
      'isBismillah': isBismillah,
      'englishText': englishText,
      'surahName': surahName,
    };
  }

  /// إنشاء نسخة جديدة من الآية مع تفسير
  Ayah copyWithTafsir(String tafsir) {
    return Ayah(
      number: number,
      text: text,
      numberInSurah: numberInSurah,
      juz: juz,
      page: page,
      hizbQuarter: hizbQuarter,
      sajda: sajda,
      isBismillah: isBismillah,
      tafsir: tafsir,
      englishText: englishText,
      surahName: surahName,
    );
  }

  /// إنشاء نسخة جديدة مع ترجمة
  Ayah copyWithTranslation(String englishText) {
    return Ayah(
      number: number,
      text: text,
      numberInSurah: numberInSurah,
      juz: juz,
      page: page,
      hizbQuarter: hizbQuarter,
      sajda: sajda,
      isBismillah: isBismillah,
      tafsir: tafsir,
      englishText: englishText,
      surahName: surahName,
    );
  }

  /// هل الآية تحتوي على ترجمة؟
  bool get hasTranslation => englishText != null && englishText!.isNotEmpty;

  /// معلومات مختصرة عن الآية
  String get shortInfo => 'الجزء $juz • الصفحة $page${sajda ? ' • سجدة' : ''}';
}

/// نموذج بيانات للقرآن الكريم كاملاً
class QuranData {
  final List<Surah> surahs;

  QuranData({required this.surahs});

  /// إنشاء نموذج من بيانات JSON
  factory QuranData.fromJson(Map<String, dynamic> json) {
    final List<dynamic> surahsJson = json['data']['surahs'];
    final List<Surah> surahs =
        surahsJson.map((surahJson) => Surah.fromJson(surahJson)).toList();
    return QuranData(surahs: surahs);
  }

  /// تحويل النموذج إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'data': {'surahs': surahs.map((surah) => surah.toJson()).toList()},
    };
  }
}

/// نموذج بيانات لنتيجة البحث في الآيات
class AyahSearchResult {
  final Surah surah;
  final Ayah ayah;
  final String searchQuery;
  final List<int> matchPositions; // مواقع الكلمات المطابقة في النص
  final String highlightedText; // النص مع تمييز الكلمات المطابقة

  AyahSearchResult({
    required this.surah,
    required this.ayah,
    required this.searchQuery,
    this.matchPositions = const [],
    String? highlightedText,
  }) : highlightedText = highlightedText ?? ayah.text;

  /// إنشاء نتيجة بحث مع تحديد مواقع الكلمات المطابقة
  factory AyahSearchResult.withHighlighting(
    Surah surah,
    Ayah ayah,
    String searchQuery,
  ) {
    // تنظيف النص من الفراغات الزائدة
    final cleanQuery = searchQuery.trim();

    // تطبيع النص للبحث
    final normalizedQuery = _normalizeArabicText(cleanQuery.toLowerCase());
    final normalizedText = _normalizeArabicText(ayah.text.toLowerCase());

    // البحث عن جميع مواقع الكلمات المطابقة
    final List<int> positions = [];

    // البحث عن الكلمات المطابقة في النص الأصلي (غير المطبع)
    // هذا يساعد في تحديد المواقع الدقيقة للكلمات في النص الأصلي
    final String originalText = ayah.text;
    final List<String> words = originalText.split(' ');

    // البحث عن الكلمات المطابقة
    int currentPosition = 0;
    for (int i = 0; i < words.length; i++) {
      final String word = words[i];
      final String normalizedWord = _normalizeArabicText(word.toLowerCase());

      // إذا كانت الكلمة تحتوي على النص المطلوب
      if (normalizedWord.contains(normalizedQuery)) {
        positions.add(currentPosition);
      }

      // تحديث الموقع الحالي
      currentPosition += word.length + 1; // +1 للمسافة
    }

    // إذا لم يتم العثور على مواقع باستخدام الطريقة السابقة، نستخدم الطريقة التقليدية
    if (positions.isEmpty) {
      int startIndex = 0;
      while (true) {
        final index = normalizedText.indexOf(normalizedQuery, startIndex);
        if (index == -1) break;
        positions.add(index);
        startIndex = index + normalizedQuery.length;
      }
    }

    // إنشاء النص المميز
    String highlighted = ayah.text;
    if (positions.isNotEmpty) {
      // تمييز الكلمات المطابقة بوضع علامات حولها
      final normalizedOriginal = _normalizeArabicText(ayah.text.toLowerCase());
      final queryIndex = normalizedOriginal.indexOf(normalizedQuery);

      if (queryIndex != -1) {
        // العثور على الموقع الدقيق في النص الأصلي
        final beforeMatch = ayah.text.substring(0, queryIndex);
        final match = ayah.text.substring(
          queryIndex,
          queryIndex + normalizedQuery.length,
        );
        final afterMatch = ayah.text.substring(
          queryIndex + normalizedQuery.length,
        );

        highlighted = '$beforeMatch**$match**$afterMatch';
      }
    }

    return AyahSearchResult(
      surah: surah,
      ayah: ayah,
      searchQuery: cleanQuery,
      matchPositions: positions,
      highlightedText: highlighted,
    );
  }

  /// تطبيع النص العربي (إزالة التشكيل والهمزات)
  static String _normalizeArabicText(String text) {
    // إزالة التشكيل
    final withoutTashkeel = text
        .replaceAll('\u064B', '') // فتحتين
        .replaceAll('\u064C', '') // ضمتين
        .replaceAll('\u064D', '') // كسرتين
        .replaceAll('\u064E', '') // فتحة
        .replaceAll('\u064F', '') // ضمة
        .replaceAll('\u0650', '') // كسرة
        .replaceAll('\u0651', '') // شدة
        .replaceAll('\u0652', '') // سكون
        .replaceAll('\u0653', '') // مدة
        .replaceAll('\u0654', '') // همزة فوق الحرف
        .replaceAll('\u0655', ''); // همزة تحت الحرف

    // توحيد أشكال الهمزات والألف
    return withoutTashkeel
        .replaceAll('أ', 'ا')
        .replaceAll('إ', 'ا')
        .replaceAll('آ', 'ا')
        .replaceAll('ى', 'ي')
        .replaceAll('ة', 'ه');
  }

  /// درجة الصلة (للتوافق مع النظام القديم)
  double get relevanceScore => matchPositions.length.toDouble();

  /// معلومات مختصرة عن النتيجة
  String get shortInfo => 'سورة ${surah.name} - آية ${ayah.number}';
}

/// نموذج بيانات للقارئ
class QuranReciter {
  final int id;
  final String arabicName;
  final String englishName;
  final String? rewaya;
  final String? server;

  const QuranReciter({
    required this.id,
    required this.arabicName,
    required this.englishName,
    this.rewaya,
    this.server,
  });

  factory QuranReciter.fromJson(Map<String, dynamic> json) {
    return QuranReciter(
      id: json['id'] ?? 0,
      arabicName: json['arabicName'] ?? json['ar'] ?? '',
      englishName: json['englishName'] ?? json['en'] ?? '',
      rewaya: json['rewaya'],
      server: json['server'],
    );
  }

  /// إنشاء من QuranAudio
  factory QuranReciter.fromQuranAudio(dynamic quranAudio) {
    if (quranAudio.runtimeType.toString().contains('QuranAudio')) {
      return QuranReciter(
        id: quranAudio.id,
        arabicName: quranAudio.reciter.ar,
        englishName: quranAudio.reciter.en,
        rewaya: quranAudio.rewaya?.ar,
        server: quranAudio.server,
      );
    }

    if (quranAudio is Map<String, dynamic>) {
      return QuranReciter.fromJson(quranAudio);
    }

    throw ArgumentError('نوع بيانات غير مدعوم للقارئ');
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'arabicName': arabicName,
      'englishName': englishName,
      'rewaya': rewaya,
      'server': server,
    };
  }

  /// اسم القارئ مع الرواية
  String get fullName {
    if (rewaya != null && rewaya!.isNotEmpty) {
      return '$arabicName ($rewaya)';
    }
    return arabicName;
  }
}

/// نموذج بيانات للتسجيل الصوتي
class QuranAudioTrack {
  final int id;
  final QuranReciter reciter;
  final String url;
  final int surahNumber;
  final String? quality;
  final Duration? duration;

  const QuranAudioTrack({
    required this.id,
    required this.reciter,
    required this.url,
    required this.surahNumber,
    this.quality,
    this.duration,
  });

  factory QuranAudioTrack.fromJson(Map<String, dynamic> json) {
    return QuranAudioTrack(
      id: json['id'] ?? 0,
      reciter: QuranReciter.fromJson(json['reciter'] ?? {}),
      url: json['url'] ?? json['link'] ?? '',
      surahNumber: json['surahNumber'] ?? 0,
      quality: json['quality'],
      duration:
          json['duration'] != null ? Duration(seconds: json['duration']) : null,
    );
  }

  /// إنشاء من QuranAudio
  factory QuranAudioTrack.fromQuranAudio(dynamic quranAudio, int surahNumber) {
    if (quranAudio.runtimeType.toString().contains('QuranAudio')) {
      return QuranAudioTrack(
        id: quranAudio.id,
        reciter: QuranReciter.fromQuranAudio(quranAudio),
        url: quranAudio.link,
        surahNumber: surahNumber,
        quality: 'high', // افتراضي
      );
    }

    if (quranAudio is Map<String, dynamic>) {
      return QuranAudioTrack.fromJson(quranAudio);
    }

    throw ArgumentError('نوع بيانات غير مدعوم للتسجيل الصوتي');
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'reciter': reciter.toJson(),
      'url': url,
      'surahNumber': surahNumber,
      'quality': quality,
      'duration': duration?.inSeconds,
    };
  }

  /// هل الرابط صالح؟
  bool get isValidUrl => url.isNotEmpty && Uri.tryParse(url) != null;

  /// معلومات التسجيل
  String get info {
    final parts = <String>[];
    parts.add(reciter.fullName);
    if (quality != null) parts.add('جودة $quality');
    if (duration != null) {
      final minutes = duration!.inMinutes;
      final seconds = duration!.inSeconds % 60;
      parts.add('$minutes:${seconds.toString().padLeft(2, '0')}');
    }
    return parts.join(' • ');
  }
}
