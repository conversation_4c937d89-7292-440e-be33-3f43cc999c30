import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/theme_mode_enum.dart';
import '../models/ayah_view_mode.dart';
import '../services/notification_service.dart';
import '../services/theme_provider.dart';
import '../widgets/theme_color_picker.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/islamic_background.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final NotificationService _notificationService = NotificationService();

  bool _morningAzkarEnabled = false;
  TimeOfDay _morningAzkarTime = const TimeOfDay(hour: 7, minute: 0);

  bool _eveningAzkarEnabled = false;
  TimeOfDay _eveningAzkarTime = const TimeOfDay(hour: 17, minute: 0);

  bool _dailyZikrEnabled = false;
  TimeOfDay _dailyZikrTime = const TimeOfDay(hour: 12, minute: 0);

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();

    setState(() {
      _morningAzkarEnabled = prefs.getBool('morning_azkar_enabled') ?? false;
      _morningAzkarTime = TimeOfDay(
        hour: prefs.getInt('morning_azkar_hour') ?? 7,
        minute: prefs.getInt('morning_azkar_minute') ?? 0,
      );

      _eveningAzkarEnabled = prefs.getBool('evening_azkar_enabled') ?? false;
      _eveningAzkarTime = TimeOfDay(
        hour: prefs.getInt('evening_azkar_hour') ?? 17,
        minute: prefs.getInt('evening_azkar_minute') ?? 0,
      );

      _dailyZikrEnabled = prefs.getBool('daily_zikr_enabled') ?? false;
      _dailyZikrTime = TimeOfDay(
        hour: prefs.getInt('daily_zikr_hour') ?? 12,
        minute: prefs.getInt('daily_zikr_minute') ?? 0,
      );
    });
  }

  Future<void> _saveSettings() async {
    try {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('جاري حفظ الإعدادات...'),
            duration: Duration(seconds: 1),
          ),
        );
      }

      final prefs = await SharedPreferences.getInstance();

      await prefs.setBool('morning_azkar_enabled', _morningAzkarEnabled);
      await prefs.setInt('morning_azkar_hour', _morningAzkarTime.hour);
      await prefs.setInt('morning_azkar_minute', _morningAzkarTime.minute);

      await prefs.setBool('evening_azkar_enabled', _eveningAzkarEnabled);
      await prefs.setInt('evening_azkar_hour', _eveningAzkarTime.hour);
      await prefs.setInt('evening_azkar_minute', _eveningAzkarTime.minute);

      await prefs.setBool('daily_zikr_enabled', _dailyZikrEnabled);
      await prefs.setInt('daily_zikr_hour', _dailyZikrTime.hour);
      await prefs.setInt('daily_zikr_minute', _dailyZikrTime.minute);

      await _notificationService.cancelAllNotifications();

      if (_morningAzkarEnabled) {
        await _notificationService.scheduleMorningAzkar();
      }

      if (_eveningAzkarEnabled) {
        await _notificationService.scheduleEveningAzkar();
      }

      if (_dailyZikrEnabled) {
        await _notificationService.scheduleDailyZikr();
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ الإعدادات بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ أثناء حفظ الإعدادات'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> _selectTime(
    BuildContext context,
    TimeOfDay initialTime,
    Function(TimeOfDay) onTimeSelected,
  ) async {
    final TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: initialTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            timePickerTheme: TimePickerThemeData(
              dayPeriodTextStyle: const TextStyle(fontFamily: 'Tajawal'),
              hourMinuteTextStyle: const TextStyle(fontFamily: 'Tajawal'),
              helpTextStyle: const TextStyle(fontFamily: 'Tajawal'),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedTime != null) {
      onTimeSelected(pickedTime);
    }
  }

  String _formatTimeOfDay(TimeOfDay timeOfDay) {
    final hours = timeOfDay.hour.toString().padLeft(2, '0');
    final minutes = timeOfDay.minute.toString().padLeft(2, '0');
    return '$hours:$minutes';
  }

  /// بناء بطاقة إعدادات مخصصة
  Widget _buildSettingsCard({
    required BuildContext context,
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color:
              theme.brightness == Brightness.dark
                  ? Colors.grey.withAlpha(50)
                  : Colors.grey.withAlpha(30),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color:
                theme.brightness == Brightness.dark
                    ? Colors.black.withAlpha(30)
                    : Colors.black.withAlpha(10),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withAlpha(20),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: theme.colorScheme.primary, size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            child,
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'الإعدادات',
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
          tooltip: 'رجوع',
        ),
      ),
      body: IslamicBackground(
        opacity: theme.brightness == Brightness.dark ? 0.02 : 0.03,
        showPattern: true,
        showGradient: false,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // عنوان الصفحة مع أيقونة
            Container(
              margin: const EdgeInsets.only(bottom: 24),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withAlpha(20),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.settings,
                      color: theme.colorScheme.primary,
                      size: 28,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'إعدادات التطبيق',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                      Text(
                        'خصص التطبيق حسب تفضيلاتك',
                        style: TextStyle(
                          fontSize: 14,
                          color: theme.colorScheme.onSurface.withAlpha(180),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Theme settings
            _buildSettingsCard(
              context: context,
              title: 'تخصيص المظهر',
              icon: Icons.palette,
              child: Consumer<ThemeProvider>(
                builder: (context, themeProvider, child) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'وضع المظهر',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),

                      RadioListTile<AppThemeMode>(
                        title: const Text('وضع فاتح'),
                        value: AppThemeMode.light,
                        groupValue: themeProvider.themeMode,
                        onChanged: (value) {
                          if (value != null) {
                            themeProvider.setThemeMode(value);
                          }
                        },
                      ),

                      RadioListTile<AppThemeMode>(
                        title: const Text('وضع معتم'),
                        subtitle: const Text('خلفية رمادية داكنة'),
                        value: AppThemeMode.dim,
                        groupValue: themeProvider.themeMode,
                        onChanged: (value) {
                          if (value != null) {
                            themeProvider.setThemeMode(value);
                          }
                        },
                      ),

                      RadioListTile<AppThemeMode>(
                        title: const Text('وضع ليلي'),
                        subtitle: const Text('خلفية سوداء'),
                        value: AppThemeMode.dark,
                        groupValue: themeProvider.themeMode,
                        onChanged: (value) {
                          if (value != null) {
                            themeProvider.setThemeMode(value);
                          }
                        },
                      ),

                      const SizedBox(height: 16),
                      const Divider(),
                      const SizedBox(height: 16),

                      const ThemeColorPicker(),

                      const SizedBox(height: 16),
                      const Divider(),
                      const SizedBox(height: 16),

                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'حجم الخط',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            '${(themeProvider.fontSize * 100).toInt()}%',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          const Text('أصغر'),
                          Expanded(
                            child: Slider(
                              value: themeProvider.fontSize,
                              min: 0.8,
                              max: 1.5,
                              divisions: 7,
                              onChanged: (value) {
                                themeProvider.setFontSize(value);
                              },
                            ),
                          ),
                          const Text('أكبر'),
                        ],
                      ),
                    ],
                  );
                },
              ),
            ),

            // Notifications settings
            _buildSettingsCard(
              context: context,
              title: 'التنبيهات',
              icon: Icons.notifications,
              child: Column(
                children: [
                  SwitchListTile(
                    title: const Text('تنبيه أذكار الصباح'),
                    subtitle: Text(
                      'الوقت: ${_formatTimeOfDay(_morningAzkarTime)}',
                    ),
                    value: _morningAzkarEnabled,
                    onChanged: (value) {
                      setState(() {
                        _morningAzkarEnabled = value;
                      });
                    },
                  ),
                  if (_morningAzkarEnabled)
                    ListTile(
                      title: const Text('تعيين وقت أذكار الصباح'),
                      trailing: const Icon(Icons.access_time),
                      onTap: () async {
                        await _selectTime(context, _morningAzkarTime, (time) {
                          setState(() {
                            _morningAzkarTime = time;
                          });
                        });
                      },
                    ),

                  const Divider(),

                  SwitchListTile(
                    title: const Text('تنبيه أذكار المساء'),
                    subtitle: Text(
                      'الوقت: ${_formatTimeOfDay(_eveningAzkarTime)}',
                    ),
                    value: _eveningAzkarEnabled,
                    onChanged: (value) {
                      setState(() {
                        _eveningAzkarEnabled = value;
                      });
                    },
                  ),
                  if (_eveningAzkarEnabled)
                    ListTile(
                      title: const Text('تعيين وقت أذكار المساء'),
                      trailing: const Icon(Icons.access_time),
                      onTap: () async {
                        await _selectTime(context, _eveningAzkarTime, (time) {
                          setState(() {
                            _eveningAzkarTime = time;
                          });
                        });
                      },
                    ),

                  const Divider(),

                  SwitchListTile(
                    title: const Text('تنبيه ذكر اليوم'),
                    subtitle: Text(
                      'الوقت: ${_formatTimeOfDay(_dailyZikrTime)}',
                    ),
                    value: _dailyZikrEnabled,
                    onChanged: (value) {
                      setState(() {
                        _dailyZikrEnabled = value;
                      });
                    },
                  ),
                  if (_dailyZikrEnabled)
                    ListTile(
                      title: const Text('تعيين وقت ذكر اليوم'),
                      trailing: const Icon(Icons.access_time),
                      onTap: () async {
                        await _selectTime(context, _dailyZikrTime, (time) {
                          setState(() {
                            _dailyZikrTime = time;
                          });
                        });
                      },
                    ),
                ],
              ),
            ),

            // Quran settings
            _buildSettingsCard(
              context: context,
              title: 'إعدادات القرآن الكريم',
              icon: Icons.menu_book,
              child: Consumer<ThemeProvider>(
                builder: (context, themeProvider, child) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'طريقة عرض الآيات داخل السور',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),

                      RadioListTile<AyahViewMode>(
                        title: const Text('عرض كقائمة'),
                        subtitle: const Text('عرض الآيات كقائمة عادية'),
                        value: AyahViewMode.list,
                        groupValue: themeProvider.ayahViewMode,
                        onChanged: (value) {
                          if (value != null) {
                            themeProvider.setAyahViewMode(value);
                          }
                        },
                      ),

                      RadioListTile<AyahViewMode>(
                        title: const Text('عرض كتفسير'),
                        subtitle: const Text(
                          'عرض الآيات مع إمكانية إضافة التفسير',
                        ),
                        value: AyahViewMode.tafsir,
                        groupValue: themeProvider.ayahViewMode,
                        onChanged: (value) {
                          if (value != null) {
                            themeProvider.setAyahViewMode(value);
                          }
                        },
                      ),

                      RadioListTile<AyahViewMode>(
                        title: const Text('تمرير متواصل'),
                        subtitle: const Text('عرض الآيات بشكل متواصل'),
                        value: AyahViewMode.continuousScroll,
                        groupValue: themeProvider.ayahViewMode,
                        onChanged: (value) {
                          if (value != null) {
                            themeProvider.setAyahViewMode(value);
                          }
                        },
                      ),

                      RadioListTile<AyahViewMode>(
                        title: const Text('وضع المصحف'),
                        subtitle: const Text(
                          'عرض الآيات بتخطيط المصحف التقليدي مع ترقيم الصفحات الأصلي',
                        ),
                        value: AyahViewMode.mushaf,
                        groupValue: themeProvider.ayahViewMode,
                        onChanged: (value) {
                          if (value != null) {
                            themeProvider.setAyahViewMode(value);
                          }
                        },
                      ),

                      RadioListTile<AyahViewMode>(
                        title: const Text('المصحف التقليدي'),
                        subtitle: const Text(
                          'عرض صور المصحف التقليدي الأصلي (مصحف المدينة) مع إمكانية تقليب الصفحات',
                        ),
                        value: AyahViewMode.traditionalMushaf,
                        groupValue: themeProvider.ayahViewMode,
                        onChanged: (value) {
                          if (value != null) {
                            themeProvider.setAyahViewMode(value);
                          }
                        },
                      ),
                    ],
                  );
                },
              ),
            ),

            const SizedBox(height: 24),

            // Save button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _saveSettings,
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
                child: const Text(
                  'حفظ الإعدادات',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
