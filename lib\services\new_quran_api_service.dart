import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/quran_model.dart';
import '../utils/logger.dart';

/// خدمة API القرآن الجديدة - الترحيل الكامل إلى https://quran.i8x.net/api/
class NewQuranApiService {
  static final NewQuranApiService _instance = NewQuranApiService._internal();
  factory NewQuranApiService() => _instance;
  NewQuranApiService._internal();

  static const String _baseUrl = 'https://quran.i8x.net/api';
  static const Duration _timeout = Duration(seconds: 30);

  // Cache للبيانات
  List<Surah>? _cachedSurahs;
  List<Surah> _surahs = [];
  final Map<int, List<Ayah>> _cachedAyahs = {};
  final Map<int, List<QuranAudioTrack>> _cachedAudio = {};
  List<Ayah>? _cachedSajdaAyahs;

  /// الحصول على جميع السور
  Future<List<Surah>> getAllSurahs() async {
    try {
      if (_cachedSurahs != null) {
        AppLogger.info('📚 استخدام السور المحفوظة في الذاكرة');
        return _cachedSurahs!;
      }

      AppLogger.info('🔄 تحميل السور من API الجديد...');

      final response = await http.get(
        Uri.parse('$_baseUrl/surahs'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['success'] == true) {
          final List<dynamic> surahsData = data['result'];
          _cachedSurahs =
              surahsData.map((surahData) => _mapToSurah(surahData)).toList();
          _surahs = _cachedSurahs!; // تحديث قائمة السور للبحث

          AppLogger.info(
              '✅ تم تحميل ${_cachedSurahs!.length} سورة من API الجديد');
          return _cachedSurahs!;
        } else {
          throw Exception(
              'فشل في تحميل السور: ${data['error'] ?? 'خطأ غير معروف'}');
        }
      } else {
        throw Exception('خطأ في الشبكة: ${response.statusCode}');
      }
    } catch (e) {
      AppLogger.error('❌ خطأ في تحميل السور: $e');
      rethrow;
    }
  }

  /// الحصول على آيات سورة محددة
  Future<List<Ayah>> getSurahAyahs(int surahNumber) async {
    try {
      if (_cachedAyahs.containsKey(surahNumber)) {
        AppLogger.info(
            '📖 استخدام آيات السورة $surahNumber المحفوظة في الذاكرة');
        return _cachedAyahs[surahNumber]!;
      }

      AppLogger.info('🔄 تحميل آيات السورة $surahNumber من API الجديد...');

      final response = await http.get(
        Uri.parse('$_baseUrl/surah/$surahNumber'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['success'] == true) {
          final surahData = data['result'];
          final List<dynamic> versesData = surahData['verses'];

          final ayahs = versesData
              .map((verseData) => _mapToAyah(verseData, surahNumber))
              .toList();
          _cachedAyahs[surahNumber] = ayahs;

          AppLogger.info('✅ تم تحميل ${ayahs.length} آية للسورة $surahNumber');
          return ayahs;
        } else {
          throw Exception(
              'فشل في تحميل آيات السورة: ${data['error'] ?? 'خطأ غير معروف'}');
        }
      } else {
        throw Exception('خطأ في الشبكة: ${response.statusCode}');
      }
    } catch (e) {
      AppLogger.error('❌ خطأ في تحميل آيات السورة $surahNumber: $e');
      rethrow;
    }
  }

  /// الحصول على التسجيلات الصوتية لسورة
  Future<List<QuranAudioTrack>> getSurahAudio(int surahNumber,
      {String? reciterId}) async {
    try {
      if (_cachedAudio.containsKey(surahNumber)) {
        AppLogger.info(
            '🎵 استخدام تسجيلات السورة $surahNumber المحفوظة في الذاكرة');
        return _cachedAudio[surahNumber]!;
      }

      AppLogger.info('🔄 تحميل تسجيلات السورة $surahNumber من API الجديد...');

      final response = await http.get(
        Uri.parse('$_baseUrl/surah/$surahNumber'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['success'] == true) {
          final surahData = data['result'];
          final List<dynamic> audioData = surahData['audio'] ?? [];

          final audioTracks = audioData
              .map((audio) => _mapToAudioTrack(audio, surahNumber))
              .toList();
          _cachedAudio[surahNumber] = audioTracks;

          AppLogger.info(
              '✅ تم تحميل ${audioTracks.length} تسجيل للسورة $surahNumber');
          return audioTracks;
        } else {
          throw Exception(
              'فشل في تحميل التسجيلات: ${data['error'] ?? 'خطأ غير معروف'}');
        }
      } else {
        throw Exception('خطأ في الشبكة: ${response.statusCode}');
      }
    } catch (e) {
      AppLogger.error('❌ خطأ في تحميل تسجيلات السورة $surahNumber: $e');
      rethrow;
    }
  }

  /// الحصول على آيات السجدة
  Future<List<Ayah>> getSajdaAyahs() async {
    try {
      if (_cachedSajdaAyahs != null) {
        AppLogger.info('🕌 استخدام آيات السجدة المحفوظة في الذاكرة');
        return _cachedSajdaAyahs!;
      }

      AppLogger.info('🔄 تحميل آيات السجدة من API الجديد...');

      final response = await http.get(
        Uri.parse('$_baseUrl/sajda'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['success'] == true) {
          final List<dynamic> sajdaData = data['result'];
          _cachedSajdaAyahs =
              sajdaData.map((sajda) => _mapSajdaToAyah(sajda)).toList();

          AppLogger.info('✅ تم تحميل ${_cachedSajdaAyahs!.length} آية سجدة');
          return _cachedSajdaAyahs!;
        } else {
          throw Exception(
              'فشل في تحميل آيات السجدة: ${data['error'] ?? 'خطأ غير معروف'}');
        }
      } else {
        throw Exception('خطأ في الشبكة: ${response.statusCode}');
      }
    } catch (e) {
      AppLogger.error('❌ خطأ في تحميل آيات السجدة: $e');
      rethrow;
    }
  }

  /// البحث في الآيات
  Future<List<AyahSearchResult>> searchAyahs(String query,
      {int? surahNumber}) async {
    try {
      AppLogger.info(
          '🔍 البحث عن: "$query"${surahNumber != null ? ' في السورة $surahNumber' : ''}');

      if (query.trim().isEmpty) {
        return [];
      }

      final results = <AyahSearchResult>[];

      if (surahNumber != null) {
        // البحث في سورة محددة
        final ayahs = await getSurahAyahs(surahNumber);
        final surah = _surahs.firstWhere((s) => s.number == surahNumber);
        for (final ayah in ayahs) {
          if (_containsQuery(ayah.text, query)) {
            results.add(AyahSearchResult(
              surah: surah,
              ayah: ayah,
              searchQuery: query,
              highlightedText: _highlightQuery(ayah.text, query),
            ));
          }
        }
      } else {
        // البحث في جميع السور
        final surahs = await getAllSurahs();
        for (final surah in surahs) {
          final ayahs = await getSurahAyahs(surah.number);
          for (final ayah in ayahs) {
            if (_containsQuery(ayah.text, query)) {
              results.add(AyahSearchResult(
                surah: surah,
                ayah: ayah,
                searchQuery: query,
                highlightedText: _highlightQuery(ayah.text, query),
              ));
            }
          }
        }
      }

      // ترتيب النتائج حسب الصلة
      results.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));

      AppLogger.info('✅ تم العثور على ${results.length} نتيجة');
      return results;
    } catch (e) {
      AppLogger.error('❌ خطأ في البحث: $e');
      rethrow;
    }
  }

  /// تحويل بيانات السورة من API إلى نموذج Surah
  Surah _mapToSurah(Map<String, dynamic> data) {
    return Surah(
      number: data['number'] ?? 0,
      name: data['name']?['ar'] ?? '',
      englishName: data['name']?['en'] ?? '',
      englishNameTranslation: data['name']?['transliteration'] ?? '',
      numberOfAyahs: data['verses_count'] ?? 0,
      revelationType:
          data['revelation_place']?['en'] == 'meccan' ? 'Meccan' : 'Medinan',
      wordsCount: data['words_count'],
      lettersCount: data['letters_count'],
    );
  }

  /// تحويل بيانات الآية من API إلى نموذج Ayah
  Ayah _mapToAyah(Map<String, dynamic> data, int surahNumber) {
    // معالجة sajda بطريقة آمنة
    bool sajdaValue = false;
    final sajdaData = data['sajda'];
    if (sajdaData is bool) {
      sajdaValue = sajdaData;
    } else if (sajdaData is Map<String, dynamic>) {
      sajdaValue = sajdaData['sajda'] == true;
    } else if (sajdaData != null) {
      sajdaValue = sajdaData.toString().toLowerCase() == 'true';
    }

    return Ayah(
      number: data['number'] ?? 0,
      text: data['text']?['ar'] ?? '',
      numberInSurah: data['number'] ?? 0,
      juz: data['juz'] ?? 0,
      page: data['page'] ?? 0,
      hizbQuarter: 0, // سيتم تحديثه لاحقاً
      sajda: sajdaValue,
      englishText: data['text']?['en'],
      surahNumber: surahNumber,
    );
  }

  /// تحويل بيانات السجدة إلى نموذج Ayah
  Ayah _mapSajdaToAyah(Map<String, dynamic> data) {
    return Ayah(
      number: data['number'] ?? 0,
      text: data['text']?['ar'] ?? '',
      numberInSurah: data['number'] ?? 0,
      juz: data['juz'] ?? 0,
      page: data['page'] ?? 0,
      hizbQuarter: 0, // سيتم تحديثه لاحقاً
      sajda: true, // آيات السجدة دائماً true
      englishText: data['text']?['en'],
      surahName: data['surahName'],
      surahNumber: data['surahNumber'] ?? 0,
    );
  }

  /// تحويل بيانات الصوت إلى نموذج QuranAudioTrack
  QuranAudioTrack _mapToAudioTrack(Map<String, dynamic> data, int surahNumber) {
    return QuranAudioTrack(
      id: data['id'] ?? 0,
      surahNumber: surahNumber,
      url: data['link'] ?? '',
      reciter: QuranReciter(
        id: data['id'] ?? 0,
        arabicName: data['reciter']?['ar'] ?? '',
        englishName: data['reciter']?['en'] ?? '',
        rewaya: data['rewaya']?['ar'] ?? '',
        server: data['server'] ?? '',
      ),
    );
  }

  /// فحص ما إذا كان النص يحتوي على الاستعلام
  bool _containsQuery(String text, String query) {
    final normalizedText = _normalizeArabicText(text.toLowerCase());
    final normalizedQuery = _normalizeArabicText(query.toLowerCase());
    return normalizedText.contains(normalizedQuery);
  }

  /// تطبيع النص العربي
  String _normalizeArabicText(String text) {
    return text
        .replaceAll('\u064B', '') // فتحتين
        .replaceAll('\u064C', '') // ضمتين
        .replaceAll('\u064D', '') // كسرتين
        .replaceAll('\u064E', '') // فتحة
        .replaceAll('\u064F', '') // ضمة
        .replaceAll('\u0650', '') // كسرة
        .replaceAll('\u0651', '') // شدة
        .replaceAll('\u0652', '') // سكون
        .replaceAll('أ', 'ا')
        .replaceAll('إ', 'ا')
        .replaceAll('آ', 'ا')
        .replaceAll('ى', 'ي')
        .replaceAll('ة', 'ه');
  }

  /// تمييز النص المطابق
  String _highlightQuery(String text, String query) {
    final normalizedText = _normalizeArabicText(text.toLowerCase());
    final normalizedQuery = _normalizeArabicText(query.toLowerCase());

    final index = normalizedText.indexOf(normalizedQuery);
    if (index == -1) return text;

    final before = text.substring(0, index);
    final match = text.substring(index, index + query.length);
    final after = text.substring(index + query.length);

    return '$before**$match**$after';
  }

  /// تنظيف الذاكرة المؤقتة
  void clearCache() {
    _cachedSurahs = null;
    _cachedAyahs.clear();
    _cachedAudio.clear();
    _cachedSajdaAyahs = null;
    AppLogger.info('🗑️ تم تنظيف ذاكرة API الجديد');
  }

  /// معلومات الخدمة
  Map<String, dynamic> getServiceInfo() {
    return {
      'name': 'New Quran API Service',
      'version': '1.0',
      'base_url': _baseUrl,
      'cached_surahs': _cachedSurahs?.length ?? 0,
      'cached_ayahs_surahs': _cachedAyahs.length,
      'cached_audio_surahs': _cachedAudio.length,
      'cached_sajda_ayahs': _cachedSajdaAyahs?.length ?? 0,
      'features': 'Complete Migration to quran.i8x.net API',
    };
  }
}
