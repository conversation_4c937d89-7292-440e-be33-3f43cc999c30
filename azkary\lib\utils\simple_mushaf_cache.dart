import 'package:http/http.dart' as http;
import '../models/mushaf_page.dart';
import '../utils/logger.dart';

/// مدير تخزين مؤقت بسيط لصور المصحف (بدون path_provider)
class SimpleMushafCache {
  static final SimpleMushafCache _instance = SimpleMushafCache._internal();
  factory SimpleMushafCache() => _instance;
  SimpleMushafCache._internal();

  static const int _preloadDistance = 2; // عدد الصفحات للتحميل المسبق

  final Map<int, MushafPageState> _memoryCache = {};
  final Map<int, Future<MushafPageState>> _downloadingPages = {};
  final Set<int> _preloadedPages = {};

  /// تهيئة مدير التخزين المؤقت
  Future<void> initialize() async {
    try {
      AppLogger.info('تم تهيئة مدير التخزين المؤقت البسيط لصور المصحف');
      AppLogger.info('ملاحظة: يتم استخدام الذاكرة فقط (بدون تخزين محلي)');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة مدير التخزين المؤقت: $e');
    }
  }

  /// الحصول على صفحة المصحف مع التخزين المؤقت الذكي
  Future<MushafPageState> getPage(MushafPage page) async {
    final pageNumber = page.pageNumber;

    // التحقق من الذاكرة المؤقتة أولاً
    if (_memoryCache.containsKey(pageNumber)) {
      final cachedState = _memoryCache[pageNumber]!;
      if (cachedState.isReady) {
        AppLogger.info('تم العثور على الصفحة $pageNumber في الذاكرة المؤقتة');
        return cachedState;
      }
    }

    // التحقق من التحميل الجاري
    if (_downloadingPages.containsKey(pageNumber)) {
      AppLogger.info('الصفحة $pageNumber قيد التحميل، انتظار...');
      return await _downloadingPages[pageNumber]!;
    }

    // بدء التحميل
    final downloadFuture = _downloadPage(page);
    _downloadingPages[pageNumber] = downloadFuture;

    try {
      final result = await downloadFuture;
      _memoryCache[pageNumber] = result;
      return result;
    } finally {
      _downloadingPages.remove(pageNumber);
    }
  }

  /// تحميل صفحة المصحف
  Future<MushafPageState> _downloadPage(MushafPage page) async {
    try {
      AppLogger.info('تحميل صفحة المصحف رقم ${page.pageNumber} من: ${page.imageUrl}');

      final response = await http
          .get(
            Uri.parse(page.imageUrl),
            headers: {
              'User-Agent': 'Mozilla/5.0 (Android; Mobile) QuranApp/1.0',
              'Accept': 'image/png,image/jpeg,image/*,*/*;q=0.8',
              'Cache-Control': 'max-age=86400',
            },
          )
          .timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        AppLogger.info('✅ تم تحميل صفحة المصحف رقم ${page.pageNumber} بنجاح (${response.bodyBytes.length} bytes)');
        
        final updatedPage = page.copyWith(
          isDownloaded: false, // لا نحفظ محلياً
          lastAccessed: DateTime.now(),
        );

        return MushafPageState.loaded(updatedPage);
      } else {
        final errorMsg = 'فشل في تحميل الصورة: HTTP ${response.statusCode}';
        AppLogger.error('❌ $errorMsg - URL: ${page.imageUrl}');
        throw Exception(errorMsg);
      }
    } catch (e) {
      final errorMsg = 'خطأ في تحميل صفحة المصحف رقم ${page.pageNumber}: $e';
      AppLogger.error('❌ $errorMsg - URL: ${page.imageUrl}');
      return MushafPageState.error(page, e.toString());
    }
  }

  /// تحميل مسبق للصفحات المجاورة
  Future<void> preloadAdjacentPages(
    int currentPage,
    List<MushafPage> allPages,
  ) async {
    final pagesToPreload = <MushafPage>[];

    // تحديد الصفحات للتحميل المسبق
    for (int i = -_preloadDistance; i <= _preloadDistance; i++) {
      final pageIndex = currentPage - 1 + i; // تحويل إلى index
      if (pageIndex >= 0 && pageIndex < allPages.length) {
        final page = allPages[pageIndex];
        if (!_preloadedPages.contains(page.pageNumber) &&
            !_memoryCache.containsKey(page.pageNumber)) {
          pagesToPreload.add(page);
        }
      }
    }

    // تحميل مسبق في الخلفية
    for (final page in pagesToPreload) {
      _preloadedPages.add(page.pageNumber);
      AppLogger.info('تحميل مسبق للصفحة ${page.pageNumber}');
      getPage(page).catchError((e) {
        AppLogger.error('خطأ في التحميل المسبق للصفحة ${page.pageNumber}: $e');
        _preloadedPages.remove(page.pageNumber);
        return MushafPageState.error(page, e.toString());
      });
    }
  }

  /// مسح التخزين المؤقت
  Future<void> clearCache() async {
    try {
      _memoryCache.clear();
      _preloadedPages.clear();
      AppLogger.info('تم مسح التخزين المؤقت لصور المصحف');
    } catch (e) {
      AppLogger.error('خطأ في مسح التخزين المؤقت: $e');
    }
  }

  /// الحصول على حجم التخزين المؤقت
  Future<int> getCacheSize() async {
    try {
      // تقدير حجم الذاكرة المؤقتة
      return _memoryCache.length * 1024 * 100; // تقدير 100KB لكل صفحة
    } catch (e) {
      AppLogger.error('خطأ في حساب حجم التخزين المؤقت: $e');
      return 0;
    }
  }

  /// التحقق من توفر صفحة محلياً
  Future<bool> isPageCached(int pageNumber) async {
    return _memoryCache.containsKey(pageNumber);
  }

  /// تنظيف الذاكرة المؤقتة
  void clearMemoryCache() {
    _memoryCache.clear();
    _preloadedPages.clear();
    AppLogger.info('تم مسح الذاكرة المؤقتة للمصحف');
  }

  /// الحصول على إحصائيات التخزين المؤقت
  Map<String, dynamic> getCacheStats() {
    return {
      'cached_pages': _memoryCache.length,
      'downloading_pages': _downloadingPages.length,
      'preloaded_pages': _preloadedPages.length,
      'estimated_size_kb': _memoryCache.length * 100,
    };
  }

  /// طباعة إحصائيات التخزين المؤقت
  void printCacheStats() {
    final stats = getCacheStats();
    AppLogger.info('=== إحصائيات التخزين المؤقت ===');
    AppLogger.info('الصفحات المحفوظة: ${stats['cached_pages']}');
    AppLogger.info('الصفحات قيد التحميل: ${stats['downloading_pages']}');
    AppLogger.info('الصفحات المحملة مسبقاً: ${stats['preloaded_pages']}');
    AppLogger.info('الحجم التقديري: ${stats['estimated_size_kb']} KB');
    AppLogger.info('================================');
  }
}
