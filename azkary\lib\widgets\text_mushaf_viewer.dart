import 'package:flutter/material.dart';
import '../models/quran_model.dart';
import '../services/quran_api_service.dart';
import '../utils/logger.dart';

/// عارض المصحف النصي (بديل مؤقت للصور)
class TextMushafViewer extends StatefulWidget {
  final Surah surah;

  const TextMushafViewer({super.key, required this.surah});

  @override
  State<TextMushafViewer> createState() => _TextMushafViewerState();
}

class _TextMushafViewerState extends State<TextMushafViewer> {
  List<Ayah> _ayahs = [];
  Set<int> _pages = {};
  int _currentPageIndex = 0;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadSurahData();
  }

  /// تحميل بيانات السورة
  Future<void> _loadSurahData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      AppLogger.info('تحميل بيانات السورة ${widget.surah.name}...');

      // تحميل آيات السورة
      final ayahs = await QuranApiService.getSurahAyahs(widget.surah.number);
      final pages = await QuranApiService.getSurahPages(widget.surah.number);

      setState(() {
        _ayahs = ayahs;
        _pages = pages;
        _isLoading = false;
      });

      AppLogger.info('✅ تم تحميل ${ayahs.length} آية في ${pages.length} صفحة');
    } catch (e) {
      AppLogger.error('❌ خطأ في تحميل بيانات السورة: $e');
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  /// الحصول على آيات الصفحة الحالية
  List<Ayah> _getCurrentPageAyahs() {
    if (_pages.isEmpty) return [];

    final pagesList = _pages.toList()..sort();
    if (_currentPageIndex >= pagesList.length) return [];

    final currentPage = pagesList[_currentPageIndex];
    return _ayahs.where((ayah) => ayah.page == currentPage).toList();
  }

  /// الانتقال للصفحة التالية
  void _nextPage() {
    if (_currentPageIndex < _pages.length - 1) {
      setState(() {
        _currentPageIndex++;
      });
    }
  }

  /// الانتقال للصفحة السابقة
  void _previousPage() {
    if (_currentPageIndex > 0) {
      setState(() {
        _currentPageIndex--;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDarkMode ? Colors.black : Colors.white,
      appBar: AppBar(
        title: Text(
          'المصحف الشريف - ${widget.surah.name}',
          style: const TextStyle(fontFamily: 'Amiri'),
        ),
        backgroundColor: isDarkMode ? Colors.grey[900] : Colors.brown[600],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _loadSurahData,
            icon: const Icon(Icons.refresh),
            tooltip: 'إعادة تحميل',
          ),
        ],
      ),
      body: _buildBody(isDarkMode),
      bottomNavigationBar: _buildBottomBar(isDarkMode),
    );
  }

  /// بناء محتوى الصفحة
  Widget _buildBody(bool isDarkMode) {
    if (_isLoading) {
      return _buildLoadingState(isDarkMode);
    }

    if (_error != null) {
      return _buildErrorState(isDarkMode);
    }

    if (_ayahs.isEmpty) {
      return _buildEmptyState(isDarkMode);
    }

    return _buildMushafContent(isDarkMode);
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState(bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              isDarkMode ? Colors.amber[400]! : Colors.brown[600]!,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل السورة ${widget.surah.name}...',
            style: TextStyle(
              fontSize: 16,
              color: isDarkMode ? Colors.white70 : Colors.black87,
              fontFamily: 'Amiri',
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState(bool isDarkMode) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: isDarkMode ? Colors.red[300] : Colors.red[600],
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل السورة',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black87,
                fontFamily: 'Amiri',
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error ?? 'خطأ غير معروف',
              style: TextStyle(
                fontSize: 14,
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                fontFamily: 'Amiri',
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _loadSurahData,
              icon: const Icon(Icons.refresh),
              label: const Text(
                'إعادة المحاولة',
                style: TextStyle(fontFamily: 'Amiri'),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    isDarkMode ? Colors.amber[400] : Colors.brown[600],
                foregroundColor: isDarkMode ? Colors.black : Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState(bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.book_outlined,
            size: 64,
            color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد آيات متاحة',
            style: TextStyle(
              fontSize: 18,
              color: isDarkMode ? Colors.white70 : Colors.black87,
              fontFamily: 'Amiri',
            ),
          ),
        ],
      ),
    );
  }

  /// بناء محتوى المصحف
  Widget _buildMushafContent(bool isDarkMode) {
    final currentPageAyahs = _getCurrentPageAyahs();
    final pagesList = _pages.toList()..sort();
    final currentPage = pagesList.isNotEmpty ? pagesList[_currentPageIndex] : 0;

    return Column(
      children: [
        // شريط معلومات الصفحة
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isDarkMode ? Colors.grey[800] : Colors.brown[50],
            border: Border(
              bottom: BorderSide(
                color: isDarkMode ? Colors.grey[700]! : Colors.brown[200]!,
              ),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الصفحة ${_convertToArabicNumbers(currentPage)}',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black87,
                  fontFamily: 'Amiri',
                ),
              ),
              Text(
                '${_convertToArabicNumbers(_currentPageIndex + 1)} من ${_convertToArabicNumbers(_pages.length)}',
                style: TextStyle(
                  fontSize: 14,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  fontFamily: 'Amiri',
                ),
              ),
            ],
          ),
        ),
        // محتوى الآيات
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children:
                  currentPageAyahs
                      .map((ayah) => _buildAyahWidget(ayah, isDarkMode))
                      .toList(),
            ),
          ),
        ),
      ],
    );
  }

  /// بناء widget للآية
  Widget _buildAyahWidget(Ayah ayah, bool isDarkMode) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[800] : Colors.brown[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDarkMode ? Colors.grey[700]! : Colors.brown[200]!,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // نص الآية
          Text(
            ayah.text,
            style: TextStyle(
              fontSize: 20,
              height: 2.0,
              color: isDarkMode ? Colors.white : Colors.black87,
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.right,
            textDirection: TextDirection.rtl,
          ),
          const SizedBox(height: 8),
          // معلومات الآية
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'آية ${_convertToArabicNumbers(ayah.numberInSurah)}',
                style: TextStyle(
                  fontSize: 12,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  fontFamily: 'Amiri',
                ),
              ),
              Text(
                'الجزء ${_convertToArabicNumbers(ayah.juz)}',
                style: TextStyle(
                  fontSize: 12,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  fontFamily: 'Amiri',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء شريط التنقل السفلي
  Widget _buildBottomBar(bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[900] : Colors.brown[600],
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            onPressed: _currentPageIndex > 0 ? _previousPage : null,
            icon: const Icon(Icons.arrow_back_ios),
            color: Colors.white,
            tooltip: 'الصفحة السابقة',
          ),
          Text(
            '${_convertToArabicNumbers(_currentPageIndex + 1)} / ${_convertToArabicNumbers(_pages.length)}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontFamily: 'Amiri',
            ),
          ),
          IconButton(
            onPressed: _currentPageIndex < _pages.length - 1 ? _nextPage : null,
            icon: const Icon(Icons.arrow_forward_ios),
            color: Colors.white,
            tooltip: 'الصفحة التالية',
          ),
        ],
      ),
    );
  }

  /// تحويل الأرقام إلى العربية
  String _convertToArabicNumbers(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      final index = int.tryParse(digit);
      return index != null ? arabicNumbers[index] : digit;
    }).join();
  }
}
