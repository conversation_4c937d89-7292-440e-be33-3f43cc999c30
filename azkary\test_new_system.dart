import 'package:flutter/material.dart';
import 'lib/services/new_quran_api_service.dart';
import 'lib/services/integrated_audio_player.dart';
import 'lib/models/quran_model.dart';

/// اختبار شامل للنظام الجديد - الترحيل الكامل للـ API
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  print('🚀 بدء اختبار النظام الجديد - الترحيل الكامل للـ API...\n');

  await testNewQuranApiService();
  await testIntegratedAudioPlayer();
  
  print('\n🎉 تم إنجاز جميع الاختبارات بنجاح!');
  print('✅ النظام الجديد جاهز للاستخدام مع API الجديد حصرياً');
}

/// اختبار خدمة API الجديدة
Future<void> testNewQuranApiService() async {
  print('📡 === اختبار خدمة API الجديدة ===');
  
  final apiService = NewQuranApiService();
  
  try {
    // اختبار 1: تحميل جميع السور
    print('🔄 اختبار 1: تحميل جميع السور...');
    final surahs = await apiService.getAllSurahs();
    print('✅ تم تحميل ${surahs.length} سورة');
    
    if (surahs.isNotEmpty) {
      final firstSurah = surahs.first;
      print('   أول سورة: ${firstSurah.name} (${firstSurah.englishName})');
      print('   عدد الآيات: ${firstSurah.numberOfAyahs}');
      print('   نوع النزول: ${firstSurah.revelationType}');
      if (firstSurah.wordsCount != null) {
        print('   عدد الكلمات: ${firstSurah.wordsCount}');
      }
      if (firstSurah.lettersCount != null) {
        print('   عدد الحروف: ${firstSurah.lettersCount}');
      }
    }
    print('');

    // اختبار 2: تحميل آيات سورة الفاتحة
    print('🔄 اختبار 2: تحميل آيات سورة الفاتحة...');
    final fatihahAyahs = await apiService.getSurahAyahs(1);
    print('✅ تم تحميل ${fatihahAyahs.length} آية من سورة الفاتحة');
    
    if (fatihahAyahs.isNotEmpty) {
      final firstAyah = fatihahAyahs.first;
      print('   الآية الأولى: ${firstAyah.text}');
      print('   الجزء: ${firstAyah.juz}');
      print('   الصفحة: ${firstAyah.page}');
      if (firstAyah.englishText != null) {
        print('   الترجمة: ${firstAyah.englishText}');
      }
    }
    print('');

    // اختبار 3: تحميل التسجيلات الصوتية
    print('🔄 اختبار 3: تحميل التسجيلات الصوتية للفاتحة...');
    final audioTracks = await apiService.getSurahAudio(1);
    print('✅ تم تحميل ${audioTracks.length} تسجيل صوتي');
    
    if (audioTracks.isNotEmpty) {
      final firstTrack = audioTracks.first;
      print('   القارئ الأول: ${firstTrack.reciter.fullName}');
      print('   الرواية: ${firstTrack.reciter.rewaya}');
      print('   الرابط: ${firstTrack.url}');
      print('   صحة الرابط: ${firstTrack.isValidUrl ? 'صحيح' : 'غير صحيح'}');
    }
    print('');

    // اختبار 4: تحميل آيات السجدة
    print('🔄 اختبار 4: تحميل آيات السجدة...');
    final sajdaAyahs = await apiService.getSajdaAyahs();
    print('✅ تم تحميل ${sajdaAyahs.length} آية سجدة');
    
    if (sajdaAyahs.isNotEmpty) {
      final firstSajda = sajdaAyahs.first;
      print('   أول آية سجدة: ${firstSajda.text.substring(0, 50)}...');
      print('   الجزء: ${firstSajda.juz}');
      print('   الصفحة: ${firstSajda.page}');
      if (firstSajda.surahName != null) {
        print('   السورة: ${firstSajda.surahName}');
      }
    }
    print('');

    // اختبار 5: البحث في الآيات
    print('🔄 اختبار 5: البحث في الآيات...');
    final searchResults = await apiService.searchAyahs('الحمد لله');
    print('✅ تم العثور على ${searchResults.length} نتيجة بحث');
    
    if (searchResults.isNotEmpty) {
      final firstResult = searchResults.first;
      print('   أول نتيجة: ${firstResult.ayah.text.substring(0, 50)}...');
      print('   درجة الصلة: ${firstResult.relevanceScore}');
      print('   معلومات مختصرة: ${firstResult.shortInfo}');
    }
    print('');

    // عرض معلومات الخدمة
    final serviceInfo = apiService.getServiceInfo();
    print('📊 معلومات خدمة API:');
    serviceInfo.forEach((key, value) {
      print('   $key: $value');
    });
    print('');

  } catch (e) {
    print('❌ خطأ في اختبار API: $e');
  }
}

/// اختبار مشغل الصوتيات المتكامل
Future<void> testIntegratedAudioPlayer() async {
  print('🎵 === اختبار مشغل الصوتيات المتكامل ===');
  
  final audioPlayer = IntegratedAudioPlayer();
  
  try {
    // تهيئة المشغل
    print('🔄 تهيئة مشغل الصوتيات...');
    await audioPlayer.initialize();
    print('✅ تم تهيئة مشغل الصوتيات بنجاح');
    print('');

    // اختبار الحصول على القراء المتاحين
    print('🔄 اختبار الحصول على القراء المتاحين للفاتحة...');
    final reciters = await audioPlayer.getAvailableReciters(1);
    print('✅ تم العثور على ${reciters.length} قارئ متاح');
    
    if (reciters.isNotEmpty) {
      print('   أول 5 قراء:');
      for (int i = 0; i < 5 && i < reciters.length; i++) {
        final reciter = reciters[i];
        print('     ${i + 1}. ${reciter.fullName} (${reciter.rewaya})');
      }
    }
    print('');

    // اختبار معلومات المشغل
    final playerInfo = audioPlayer.getPlayerInfo();
    print('📊 معلومات مشغل الصوتيات:');
    playerInfo.forEach((key, value) {
      print('   $key: $value');
    });
    print('');

    // اختبار تغيير مستوى الصوت
    print('🔄 اختبار تغيير مستوى الصوت...');
    await audioPlayer.setVolume(0.8);
    print('✅ تم تغيير مستوى الصوت إلى 80%');
    print('   مستوى الصوت الحالي: ${(audioPlayer.volume * 100).round()}%');
    print('');

    // اختبار الحالات
    print('🔄 اختبار حالات المشغل...');
    print('   الحالة الحالية: ${_getStateText(audioPlayer.state)}');
    print('   يشغل: ${audioPlayer.isPlaying}');
    print('   متوقف مؤقتاً: ${audioPlayer.isPaused}');
    print('   متوقف: ${audioPlayer.isStopped}');
    print('   يحمل: ${audioPlayer.isLoading}');
    print('');

    print('✅ جميع اختبارات مشغل الصوتيات نجحت');

  } catch (e) {
    print('❌ خطأ في اختبار مشغل الصوتيات: $e');
  }
}

/// الحصول على نص حالة المشغل
String _getStateText(AudioPlayerState state) {
  switch (state) {
    case AudioPlayerState.stopped:
      return 'متوقف';
    case AudioPlayerState.playing:
      return 'يشغل';
    case AudioPlayerState.paused:
      return 'متوقف مؤقتاً';
    case AudioPlayerState.loading:
      return 'يحمل';
    case AudioPlayerState.error:
      return 'خطأ';
  }
}

/// دالة مساعدة لطباعة معلومات السورة
void printSurahInfo(Surah surah) {
  print('السورة ${surah.number}: ${surah.name}');
  print('  الاسم الإنجليزي: ${surah.englishName}');
  print('  النقل الصوتي: ${surah.englishNameTranslation}');
  print('  نوع النزول: ${surah.revelationType}');
  print('  عدد الآيات: ${surah.numberOfAyahs}');
  if (surah.wordsCount != null) {
    print('  عدد الكلمات: ${surah.wordsCount}');
  }
  if (surah.lettersCount != null) {
    print('  عدد الحروف: ${surah.lettersCount}');
  }
}

/// دالة مساعدة لطباعة معلومات الآية
void printAyahInfo(Ayah ayah) {
  print('الآية ${ayah.number}:');
  print('  النص العربي: ${ayah.text}');
  if (ayah.englishText != null) {
    print('  الترجمة: ${ayah.englishText}');
  }
  print('  الجزء: ${ayah.juz}');
  print('  الصفحة: ${ayah.page}');
  if (ayah.sajda) {
    print('  سجدة: نعم');
  }
}

/// دالة مساعدة لطباعة معلومات التسجيل الصوتي
void printAudioInfo(QuranAudioTrack audio) {
  print('التسجيل ${audio.id}:');
  print('  القارئ: ${audio.reciter.fullName}');
  print('  الرواية: ${audio.reciter.rewaya}');
  print('  الخادم: ${audio.reciter.server}');
  print('  الرابط: ${audio.url}');
  print('  صحة الرابط: ${audio.isValidUrl ? 'صحيح' : 'غير صحيح'}');
}

/// دالة مساعدة لطباعة معلومات القارئ
void printReciterInfo(QuranReciter reciter) {
  print('القارئ ${reciter.id}:');
  print('  الاسم العربي: ${reciter.arabicName}');
  print('  الاسم الإنجليزي: ${reciter.englishName}');
  print('  الرواية: ${reciter.rewaya}');
  print('  الخادم: ${reciter.server}');
}

/// دالة مساعدة لطباعة نتائج البحث
void printSearchResults(List<AyahSearchResult> results) {
  print('نتائج البحث (${results.length} نتيجة):');
  for (int i = 0; i < results.length && i < 5; i++) {
    final result = results[i];
    print('  ${i + 1}. ${result.shortInfo}');
    print('     النص: ${result.ayah.text.substring(0, 50)}...');
    print('     درجة الصلة: ${result.relevanceScore}');
  }
}
