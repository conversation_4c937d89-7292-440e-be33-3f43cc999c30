import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// زر إعدادات موحد ومتناسق لجميع شاشات التطبيق
class SettingsButton extends StatelessWidget {
  final VoidCallback onPressed;
  final String? tooltip;
  final Color? color;
  final double size;
  final EdgeInsetsGeometry? margin;
  final bool showBackground;

  const SettingsButton({
    super.key,
    required this.onPressed,
    this.tooltip = 'الإعدادات',
    this.color,
    this.size = 24,
    this.margin,
    this.showBackground = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final buttonColor = color ?? theme.colorScheme.onSurface;

    Widget iconButton = IconButton(
      icon: Icon(
        Icons.settings,
        color: buttonColor,
        size: size,
      ),
      tooltip: tooltip,
      onPressed: () {
        HapticFeedback.lightImpact(); // تأثير اهتزاز خفيف
        onPressed();
      },
      padding: EdgeInsets.zero,
      constraints: const BoxConstraints(
        minWidth: 40,
        minHeight: 40,
      ),
    );

    if (showBackground) {
      iconButton = Container(
        margin: margin ?? const EdgeInsets.only(left: 4),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: theme.colorScheme.primary.withAlpha(20),
          border: Border.all(
            color: theme.colorScheme.primary.withAlpha(30),
            width: 1,
          ),
        ),
        child: iconButton,
      );
    } else if (margin != null) {
      iconButton = Container(
        margin: margin,
        child: iconButton,
      );
    }

    return iconButton;
  }
}

/// زر إعدادات مخصص للاستخدام في AppBar
class AppBarSettingsButton extends StatelessWidget {
  final VoidCallback onPressed;
  final String? tooltip;

  const AppBarSettingsButton({
    super.key,
    required this.onPressed,
    this.tooltip = 'الإعدادات',
  });

  @override
  Widget build(BuildContext context) {
    return SettingsButton(
      onPressed: onPressed,
      tooltip: tooltip,
      showBackground: true,
      margin: const EdgeInsets.only(left: 8),
    );
  }
}

/// زر إعدادات مخصص للاستخدام في القوائم
class ListSettingsButton extends StatelessWidget {
  final VoidCallback onPressed;
  final String? tooltip;

  const ListSettingsButton({
    super.key,
    required this.onPressed,
    this.tooltip = 'الإعدادات',
  });

  @override
  Widget build(BuildContext context) {
    return SettingsButton(
      onPressed: onPressed,
      tooltip: tooltip,
      showBackground: false,
      size: 22,
    );
  }
}
