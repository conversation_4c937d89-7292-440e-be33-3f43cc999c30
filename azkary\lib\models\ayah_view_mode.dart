/// نموذج لطرق عرض الآيات داخل السور
enum AyahViewMode {
  /// عرض الآيات كقائمة عادية
  list,

  /// عرض الآيات بتنسيق تفسيري
  tafsir,

  /// عرض الآيات بتمرير عمودي مستمر
  continuousScroll,

  /// عرض الآيات بتخطيط المصحف التقليدي
  mushaf,
}

/// امتداد لتحويل طريقة عرض الآيات إلى نص
extension AyahViewModeExtension on AyahViewMode {
  /// الحصول على اسم طريقة العرض بالعربية
  String get arabicName {
    switch (this) {
      case AyahViewMode.list:
        return 'قائمة';
      case AyahViewMode.tafsir:
        return 'تفسير';
      case AyahViewMode.continuousScroll:
        return 'تمرير متواصل';
      case AyahViewMode.mushaf:
        return 'وضع المصحف';
    }
  }

  /// الحصول على وصف طريقة العرض بالعربية
  String get description {
    switch (this) {
      case AyahViewMode.list:
        return 'عرض الآيات كقائمة عادية';
      case AyahViewMode.tafsir:
        return 'عرض الآيات مع إمكانية إضافة التفسير';
      case AyahViewMode.continuousScroll:
        return 'عرض الآيات بشكل متواصل مع علامات الأجزاء والأحزاب';
      case AyahViewMode.mushaf:
        return 'عرض الآيات بتخطيط المصحف التقليدي مع ترقيم الصفحات الأصلي';
    }
  }

  /// الحصول على أيقونة طريقة العرض
  String get icon {
    switch (this) {
      case AyahViewMode.list:
        return 'view_list';
      case AyahViewMode.tafsir:
        return 'format_align_center';
      case AyahViewMode.continuousScroll:
        return 'vertical_align_center';
      case AyahViewMode.mushaf:
        return 'menu_book';
    }
  }
}
