import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/mushaf_page.dart';
import '../utils/logger.dart';

/// خدمة إدارة المصحف الشريف
class MushafService {
  static final MushafService _instance = MushafService._internal();
  factory MushafService() => _instance;
  MushafService._internal();

  // بيانات المصحف الثابتة - 604 صفحة
  static const int totalPages = 604;
  static const String baseImageUrl = 'https://everyayah.com/data/QuranPages/';
  static const String _lastPageKey = 'mushaf_last_page';
  static const String _settingsKey = 'mushaf_settings';
  
  List<MushafPage>? _allPages;
  MushafViewSettings _settings = const MushafViewSettings();
  int _lastViewedPage = 1;

  /// تهيئة الخدمة وتحميل البيانات
  Future<void> initialize() async {
    try {
      await _loadMushafData();
      await _loadSettings();
      await _loadLastViewedPage();
      AppLogger.info('تم تهيئة خدمة المصحف الشريف');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة خدمة المصحف: $e');
    }
  }

  /// تحميل بيانات المصحف
  Future<void> _loadMushafData() async {
    if (_allPages != null) return;

    // بيانات السور مع صفحات البداية والنهاية في المصحف
    final surahsData = _getSurahsPageData();
    
    // إنشاء قائمة جميع الصفحات
    _allPages = List.generate(totalPages, (index) {
      final pageNumber = index + 1;
      return MushafPage(
        pageNumber: pageNumber,
        imageUrl: '$baseImageUrl${pageNumber.toString().padLeft(3, '0')}.png',
        surahNumbers: _getSurahsInPage(pageNumber, surahsData),
        surahNames: _getSurahNamesInPage(pageNumber, surahsData),
        juzNumber: _getJuzForPage(pageNumber),
        hizbNumber: _getHizbForPage(pageNumber),
      );
    });
  }

  /// الحصول على جميع الصفحات
  Future<List<MushafPage>> getAllPages() async {
    await _loadMushafData();
    return _allPages!;
  }

  /// الحصول على صفحة معينة
  Future<MushafPage?> getPage(int pageNumber) async {
    if (pageNumber < 1 || pageNumber > totalPages) return null;
    
    await _loadMushafData();
    return _allPages![pageNumber - 1];
  }

  /// الحصول على صفحات سورة معينة
  Future<List<MushafPage>> getPagesForSurah(int surahNumber) async {
    await _loadMushafData();
    
    final surahData = _getSurahsPageData().firstWhere(
      (data) => data['surahNumber'] == surahNumber,
      orElse: () => throw Exception('السورة غير موجودة'),
    );

    final pages = <MushafPage>[];
    final startPage = surahData['startPage'] as int;
    final endPage = surahData['endPage'] as int;
    
    for (int i = startPage; i <= endPage; i++) {
      final page = await getPage(i);
      if (page != null) pages.add(page);
    }

    return pages;
  }

  /// الحصول على رقم الصفحة الأولى لسورة معينة
  Future<int> getFirstPageForSurah(int surahNumber) async {
    final surahData = _getSurahsPageData().firstWhere(
      (data) => data['surahNumber'] == surahNumber,
      orElse: () => throw Exception('السورة غير موجودة'),
    );

    return surahData['startPage'] as int;
  }

  /// حفظ آخر صفحة تم عرضها
  Future<void> saveLastViewedPage(int pageNumber) async {
    if (!_settings.autoSavePosition) return;
    
    _lastViewedPage = pageNumber;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_lastPageKey, pageNumber);
    AppLogger.info('تم حفظ آخر صفحة: $pageNumber');
  }

  /// تحميل آخر صفحة تم عرضها
  Future<void> _loadLastViewedPage() async {
    final prefs = await SharedPreferences.getInstance();
    _lastViewedPage = prefs.getInt(_lastPageKey) ?? 1;
  }

  /// الحصول على آخر صفحة تم عرضها
  int get lastViewedPage => _lastViewedPage;

  /// حفظ إعدادات العرض
  Future<void> saveSettings(MushafViewSettings settings) async {
    _settings = settings;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_settingsKey, jsonEncode(settings.toJson()));
    AppLogger.info('تم حفظ إعدادات المصحف');
  }

  /// تحميل إعدادات العرض
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final settingsJson = prefs.getString(_settingsKey);
    if (settingsJson != null) {
      try {
        final settingsMap = jsonDecode(settingsJson) as Map<String, dynamic>;
        _settings = MushafViewSettings.fromJson(settingsMap);
      } catch (e) {
        AppLogger.error('خطأ في تحميل إعدادات المصحف: $e');
      }
    }
  }

  /// الحصول على إعدادات العرض الحالية
  MushafViewSettings get settings => _settings;

  /// الحصول على السور في صفحة معينة
  List<int> _getSurahsInPage(int pageNumber, List<Map<String, dynamic>> surahsData) {
    final surahs = <int>[];
    
    for (final surahData in surahsData) {
      final startPage = surahData['startPage'] as int;
      final endPage = surahData['endPage'] as int;
      
      if (pageNumber >= startPage && pageNumber <= endPage) {
        surahs.add(surahData['surahNumber'] as int);
      }
    }
    
    return surahs;
  }

  /// الحصول على أسماء السور في صفحة معينة
  List<String> _getSurahNamesInPage(int pageNumber, List<Map<String, dynamic>> surahsData) {
    final names = <String>[];
    
    for (final surahData in surahsData) {
      final startPage = surahData['startPage'] as int;
      final endPage = surahData['endPage'] as int;
      
      if (pageNumber >= startPage && pageNumber <= endPage) {
        names.add(surahData['surahName'] as String);
      }
    }
    
    return names;
  }

  /// الحصول على رقم الجزء للصفحة
  int _getJuzForPage(int pageNumber) {
    // تقسيم تقريبي للأجزاء على الصفحات
    // كل جزء تقريباً 20 صفحة
    return ((pageNumber - 1) ~/ 20) + 1;
  }

  /// الحصول على رقم الحزب للصفحة
  int _getHizbForPage(int pageNumber) {
    // كل حزب تقريباً 10 صفحات
    return ((pageNumber - 1) ~/ 10) + 1;
  }

  /// البحث عن صفحة تحتوي على سورة معينة
  Future<int?> findPageBySurah(String surahName) async {
    final surahData = _getSurahsPageData().firstWhere(
      (data) => (data['surahName'] as String).contains(surahName),
      orElse: () => {},
    );
    
    if (surahData.isNotEmpty) {
      return surahData['startPage'] as int;
    }
    
    return null;
  }

  /// الحصول على معلومات الجزء للصفحة
  String getJuzInfo(int pageNumber) {
    final juzNumber = _getJuzForPage(pageNumber);
    const arabicNumbers = ['', 'الأول', 'الثاني', 'الثالث', 'الرابع', 'الخامس', 
                          'السادس', 'السابع', 'الثامن', 'التاسع', 'العاشر',
                          'الحادي عشر', 'الثاني عشر', 'الثالث عشر', 'الرابع عشر', 'الخامس عشر',
                          'السادس عشر', 'السابع عشر', 'الثامن عشر', 'التاسع عشر', 'العشرون',
                          'الحادي والعشرون', 'الثاني والعشرون', 'الثالث والعشرون', 'الرابع والعشرون',
                          'الخامس والعشرون', 'السادس والعشرون', 'السابع والعشرون', 'الثامن والعشرون',
                          'التاسع والعشرون', 'الثلاثون'];
    
    return juzNumber > 0 && juzNumber <= 30 
        ? 'الجزء ${arabicNumbers[juzNumber]}' 
        : 'الجزء $juzNumber';
  }

  /// بيانات السور مع صفحات البداية والنهاية (عينة من السور الأولى)
  List<Map<String, dynamic>> _getSurahsPageData() {
    return [
      {'surahNumber': 1, 'surahName': 'الفاتحة', 'startPage': 1, 'endPage': 1},
      {'surahNumber': 2, 'surahName': 'البقرة', 'startPage': 2, 'endPage': 49},
      {'surahNumber': 3, 'surahName': 'آل عمران', 'startPage': 50, 'endPage': 76},
      {'surahNumber': 4, 'surahName': 'النساء', 'startPage': 77, 'endPage': 106},
      {'surahNumber': 5, 'surahName': 'المائدة', 'startPage': 106, 'endPage': 127},
      {'surahNumber': 6, 'surahName': 'الأنعام', 'startPage': 128, 'endPage': 150},
      {'surahNumber': 7, 'surahName': 'الأعراف', 'startPage': 151, 'endPage': 176},
      {'surahNumber': 8, 'surahName': 'الأنفال', 'startPage': 177, 'endPage': 186},
      {'surahNumber': 9, 'surahName': 'التوبة', 'startPage': 187, 'endPage': 206},
      {'surahNumber': 10, 'surahName': 'يونس', 'startPage': 208, 'endPage': 221},
      // يمكن إضافة باقي السور هنا...
      {'surahNumber': 114, 'surahName': 'الناس', 'startPage': 604, 'endPage': 604},
    ];
  }
}
