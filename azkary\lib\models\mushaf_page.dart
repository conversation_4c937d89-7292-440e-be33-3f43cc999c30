/// نموذج بيانات صفحة المصحف الشريف
class MushafPage {
  final int pageNumber;
  final String imageUrl;
  final List<int> surahNumbers; // أرقام السور في الصفحة
  final List<String> surahNames; // أسماء السور في الصفحة
  final int juzNumber; // رقم الجزء
  final int hizbNumber; // رقم الحزب
  final bool isDownloaded; // هل تم تحميل الصورة محلياً
  final String? localPath; // المسار المحلي للصورة
  final DateTime? lastAccessed; // آخر وقت وصول

  const MushafPage({
    required this.pageNumber,
    required this.imageUrl,
    required this.surahNumbers,
    required this.surahNames,
    required this.juzNumber,
    required this.hizbNumber,
    this.isDownloaded = false,
    this.localPath,
    this.lastAccessed,
  });

  /// إنشاء نموذج من بيانات JSON
  factory MushafPage.fromJson(Map<String, dynamic> json) {
    return MushafPage(
      pageNumber: json['pageNumber'] ?? 0,
      imageUrl: json['imageUrl'] ?? '',
      surahNumbers: List<int>.from(json['surahNumbers'] ?? []),
      surahNames: List<String>.from(json['surahNames'] ?? []),
      juzNumber: json['juzNumber'] ?? 1,
      hizbNumber: json['hizbNumber'] ?? 1,
      isDownloaded: json['isDownloaded'] ?? false,
      localPath: json['localPath'],
      lastAccessed: json['lastAccessed'] != null 
          ? DateTime.parse(json['lastAccessed']) 
          : null,
    );
  }

  /// تحويل النموذج إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'pageNumber': pageNumber,
      'imageUrl': imageUrl,
      'surahNumbers': surahNumbers,
      'surahNames': surahNames,
      'juzNumber': juzNumber,
      'hizbNumber': hizbNumber,
      'isDownloaded': isDownloaded,
      'localPath': localPath,
      'lastAccessed': lastAccessed?.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من النموذج
  MushafPage copyWith({
    int? pageNumber,
    String? imageUrl,
    List<int>? surahNumbers,
    List<String>? surahNames,
    int? juzNumber,
    int? hizbNumber,
    bool? isDownloaded,
    String? localPath,
    DateTime? lastAccessed,
  }) {
    return MushafPage(
      pageNumber: pageNumber ?? this.pageNumber,
      imageUrl: imageUrl ?? this.imageUrl,
      surahNumbers: surahNumbers ?? this.surahNumbers,
      surahNames: surahNames ?? this.surahNames,
      juzNumber: juzNumber ?? this.juzNumber,
      hizbNumber: hizbNumber ?? this.hizbNumber,
      isDownloaded: isDownloaded ?? this.isDownloaded,
      localPath: localPath ?? this.localPath,
      lastAccessed: lastAccessed ?? this.lastAccessed,
    );
  }

  /// الحصول على اسم الجزء بالعربية
  String get juzNameArabic {
    const arabicNumbers = ['', 'الأول', 'الثاني', 'الثالث', 'الرابع', 'الخامس', 
                          'السادس', 'السابع', 'الثامن', 'التاسع', 'العاشر',
                          'الحادي عشر', 'الثاني عشر', 'الثالث عشر', 'الرابع عشر', 'الخامس عشر',
                          'السادس عشر', 'السابع عشر', 'الثامن عشر', 'التاسع عشر', 'العشرون',
                          'الحادي والعشرون', 'الثاني والعشرون', 'الثالث والعشرون', 'الرابع والعشرون',
                          'الخامس والعشرون', 'السادس والعشرون', 'السابع والعشرون', 'الثامن والعشرون',
                          'التاسع والعشرون', 'الثلاثون'];
    
    return juzNumber > 0 && juzNumber <= 30 
        ? 'الجزء ${arabicNumbers[juzNumber]}' 
        : 'الجزء $juzNumber';
  }

  /// الحصول على معلومات السور في الصفحة
  String get surahInfo {
    if (surahNames.isEmpty) return '';
    if (surahNames.length == 1) return surahNames.first;
    return surahNames.join(' - ');
  }

  /// تحويل رقم الصفحة إلى الأرقام العربية
  String get pageNumberArabic {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return pageNumber.toString().split('').map((digit) {
      final digitInt = int.tryParse(digit);
      return digitInt != null ? arabicNumbers[digitInt] : digit;
    }).join();
  }

  @override
  String toString() {
    return 'MushafPage(pageNumber: $pageNumber, juzNumber: $juzNumber, surahNames: $surahNames)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MushafPage && other.pageNumber == pageNumber;
  }

  @override
  int get hashCode => pageNumber.hashCode;
}

/// حالة تحميل صفحة المصحف
enum MushafPageLoadingState {
  /// لم يتم التحميل بعد
  notLoaded,
  
  /// جاري التحميل
  loading,
  
  /// تم التحميل بنجاح
  loaded,
  
  /// فشل في التحميل
  error,
  
  /// متاح محلياً
  cached,
}

/// نموذج لحالة صفحة المصحف مع معلومات التحميل
class MushafPageState {
  final MushafPage page;
  final MushafPageLoadingState state;
  final String? errorMessage;
  final double? downloadProgress;

  const MushafPageState({
    required this.page,
    required this.state,
    this.errorMessage,
    this.downloadProgress,
  });

  /// إنشاء حالة تحميل
  factory MushafPageState.loading(MushafPage page, {double? progress}) {
    return MushafPageState(
      page: page,
      state: MushafPageLoadingState.loading,
      downloadProgress: progress,
    );
  }

  /// إنشاء حالة نجاح
  factory MushafPageState.loaded(MushafPage page) {
    return MushafPageState(
      page: page,
      state: MushafPageLoadingState.loaded,
    );
  }

  /// إنشاء حالة خطأ
  factory MushafPageState.error(MushafPage page, String error) {
    return MushafPageState(
      page: page,
      state: MushafPageLoadingState.error,
      errorMessage: error,
    );
  }

  /// إنشاء حالة مخزنة محلياً
  factory MushafPageState.cached(MushafPage page) {
    return MushafPageState(
      page: page,
      state: MushafPageLoadingState.cached,
    );
  }

  /// هل الصفحة جاهزة للعرض؟
  bool get isReady => state == MushafPageLoadingState.loaded || 
                     state == MushafPageLoadingState.cached;

  /// هل الصفحة في حالة خطأ؟
  bool get hasError => state == MushafPageLoadingState.error;

  /// هل الصفحة قيد التحميل؟
  bool get isLoading => state == MushafPageLoadingState.loading;

  @override
  String toString() {
    return 'MushafPageState(page: ${page.pageNumber}, state: $state, error: $errorMessage)';
  }
}

/// إعدادات عرض المصحف
class MushafViewSettings {
  final bool nightMode;
  final double brightness;
  final bool showPageInfo;
  final bool enableZoom;
  final bool autoSavePosition;

  const MushafViewSettings({
    this.nightMode = false,
    this.brightness = 1.0,
    this.showPageInfo = true,
    this.enableZoom = true,
    this.autoSavePosition = true,
  });

  /// إنشاء نسخة محدثة من الإعدادات
  MushafViewSettings copyWith({
    bool? nightMode,
    double? brightness,
    bool? showPageInfo,
    bool? enableZoom,
    bool? autoSavePosition,
  }) {
    return MushafViewSettings(
      nightMode: nightMode ?? this.nightMode,
      brightness: brightness ?? this.brightness,
      showPageInfo: showPageInfo ?? this.showPageInfo,
      enableZoom: enableZoom ?? this.enableZoom,
      autoSavePosition: autoSavePosition ?? this.autoSavePosition,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'nightMode': nightMode,
      'brightness': brightness,
      'showPageInfo': showPageInfo,
      'enableZoom': enableZoom,
      'autoSavePosition': autoSavePosition,
    };
  }

  /// إنشاء من JSON
  factory MushafViewSettings.fromJson(Map<String, dynamic> json) {
    return MushafViewSettings(
      nightMode: json['nightMode'] ?? false,
      brightness: (json['brightness'] ?? 1.0).toDouble(),
      showPageInfo: json['showPageInfo'] ?? true,
      enableZoom: json['enableZoom'] ?? true,
      autoSavePosition: json['autoSavePosition'] ?? true,
    );
  }
}
