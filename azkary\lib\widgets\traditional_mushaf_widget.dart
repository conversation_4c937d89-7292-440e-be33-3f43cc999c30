import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/mushaf_image_page.dart';
import '../models/quran_model.dart';
import '../services/mushaf_page_service.dart';
import '../utils/mushaf_image_cache_manager.dart';
import '../utils/logger.dart';
import 'mushaf_image_page_widget.dart';
import 'mushaf_navigation_controls.dart';

/// ويدجت المصحف التقليدي مع صور الصفحات الأصلية
class TraditionalMushafWidget extends StatefulWidget {
  final Surah surah;
  final Function(int pageNumber)? onPageChanged;
  final bool enableZoom;
  final bool showNavigationControls;
  final bool preloadAdjacentPages;

  const TraditionalMushafWidget({
    super.key,
    required this.surah,
    this.onPageChanged,
    this.enableZoom = true,
    this.showNavigationControls = true,
    this.preloadAdjacentPages = true,
  });

  @override
  State<TraditionalMushafWidget> createState() => _TraditionalMushafWidgetState();
}

class _TraditionalMushafWidgetState extends State<TraditionalMushafWidget> {
  final PageController _pageController = PageController();
  final MushafPageService _pageService = MushafPageService();
  final MushafImageCacheManager _cacheManager = MushafImageCacheManager();

  List<MushafImagePage> _pages = [];
  int _currentPageIndex = 0;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _initializeMushaf();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// تهيئة المصحف وتحميل الصفحات
  Future<void> _initializeMushaf() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // تهيئة الخدمات
      await _pageService.initialize();
      await _cacheManager.initialize();

      // تحميل صفحات السورة
      final pages = await _pageService.getPagesForSurah(widget.surah.number);
      
      if (pages.isEmpty) {
        throw Exception('لم يتم العثور على صفحات للسورة');
      }

      setState(() {
        _pages = pages;
        _isLoading = false;
      });

      // تحميل مسبق للصفحات المجاورة
      if (widget.preloadAdjacentPages) {
        _preloadAdjacentPages();
      }

      AppLogger.info('تم تحميل ${pages.length} صفحة للسورة ${widget.surah.name}');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة المصحف: $e');
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  /// تحميل مسبق للصفحات المجاورة
  Future<void> _preloadAdjacentPages() async {
    if (_pages.isEmpty) return;

    final pagesToPreload = <MushafImagePage>[];
    
    // الصفحة الحالية والصفحات المجاورة
    for (int i = -2; i <= 2; i++) {
      final index = _currentPageIndex + i;
      if (index >= 0 && index < _pages.length) {
        pagesToPreload.add(_pages[index]);
      }
    }

    // تحميل مسبق في الخلفية
    _cacheManager.preloadPages(pagesToPreload).catchError((e) {
      AppLogger.error('خطأ في التحميل المسبق: $e');
    });
  }

  /// تغيير الصفحة
  void _onPageChanged(int index) {
    if (index < 0 || index >= _pages.length) return;

    setState(() => _currentPageIndex = index);
    
    final page = _pages[index];
    widget.onPageChanged?.call(page.pageNumber);

    // تحميل مسبق للصفحات المجاورة الجديدة
    if (widget.preloadAdjacentPages) {
      Future.delayed(const Duration(milliseconds: 500), _preloadAdjacentPages);
    }

    // تأثير اهتزاز خفيف
    HapticFeedback.selectionClick();
  }

  /// الانتقال إلى الصفحة السابقة
  void _goToPreviousPage() {
    if (_currentPageIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// الانتقال إلى الصفحة التالية
  void _goToNextPage() {
    if (_currentPageIndex < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// الانتقال إلى صفحة معينة
  void _jumpToPage(int pageNumber) {
    final index = _pages.indexWhere((page) => page.pageNumber == pageNumber);
    if (index != -1) {
      _pageController.animateToPage(
        index,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1A1A1A) : const Color(0xFFFFFDF5),
      ),
      child: Column(
        children: [
          // رأس المصحف
          _buildMushafHeader(theme, isDarkMode),

          // محتوى المصحف
          Expanded(child: _buildMushafContent(theme, isDarkMode)),

          // أدوات التنقل
          if (widget.showNavigationControls && _pages.isNotEmpty)
            MushafNavigationControls(
              currentPage: _pages.isNotEmpty ? _pages[_currentPageIndex].pageNumber : 1,
              totalPages: 604, // إجمالي صفحات المصحف
              onPreviousPage: _goToPreviousPage,
              onNextPage: _goToNextPage,
              onPageChanged: _jumpToPage,
            ),
        ],
      ),
    );
  }

  /// بناء رأس المصحف
  Widget _buildMushafHeader(ThemeData theme, bool isDarkMode) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF2C2C2C) : Colors.white,
        border: Border(
          bottom: BorderSide(
            color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        bottom: false,
        child: Column(
          children: [
            // اسم السورة
            Text(
              widget.surah.name,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.amber[400] : Colors.brown[700],
                fontFamily: 'Amiri',
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            
            // معلومات السورة
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildInfoChip(
                  '${widget.surah.numberOfAyahs} آية',
                  isDarkMode,
                ),
                const SizedBox(width: 12),
                _buildInfoChip(
                  widget.surah.revelationType == 'Meccan' ? 'مكية' : 'مدنية',
                  isDarkMode,
                ),
                if (_pages.isNotEmpty) ...[
                  const SizedBox(width: 12),
                  _buildInfoChip(
                    '${_pages.length} صفحة',
                    isDarkMode,
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء شريحة معلومات
  Widget _buildInfoChip(String text, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: (isDarkMode ? Colors.grey[700] : Colors.grey[200])?.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isDarkMode ? Colors.grey[600]! : Colors.grey[300]!,
          width: 1,
        ),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: isDarkMode ? Colors.white : Colors.black87,
          fontFamily: 'Amiri',
        ),
      ),
    );
  }

  /// بناء محتوى المصحف
  Widget _buildMushafContent(ThemeData theme, bool isDarkMode) {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_error != null) {
      return _buildErrorState(theme, isDarkMode);
    }

    if (_pages.isEmpty) {
      return _buildEmptyState(theme, isDarkMode);
    }

    return PageView.builder(
      controller: _pageController,
      onPageChanged: _onPageChanged,
      itemCount: _pages.length,
      itemBuilder: (context, index) {
        final page = _pages[index];
        return MushafImagePageWidget(
          page: page,
          enableZoom: widget.enableZoom,
          showPageInfo: true,
        );
      },
    );
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'جاري تحميل صفحات المصحف...',
            style: TextStyle(fontSize: 16, fontFamily: 'Amiri'),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState(ThemeData theme, bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: isDarkMode ? Colors.red[300] : Colors.red[600],
          ),
          const SizedBox(height: 16),
          Text(
            'خطأ في تحميل المصحف',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.black87,
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            _error!,
            style: TextStyle(
              fontSize: 14,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _initializeMushaf,
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة', style: TextStyle(fontFamily: 'Amiri')),
          ),
        ],
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState(ThemeData theme, bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.menu_book_outlined,
            size: 64,
            color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد صفحات متاحة',
            style: TextStyle(
              fontSize: 18,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
