import 'package:flutter/material.dart';

/// خلفية محسنة للأداء لشاشة البداية - بدون تأثيرات ثقيلة
class PerformanceOptimizedSplashBackground extends StatelessWidget {
  final Widget child;
  final bool isDarkMode;

  const PerformanceOptimizedSplashBackground({
    super.key,
    required this.child,
    required this.isDarkMode,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: _buildStaticGradient(),
      ),
      child: Stack(
        children: [
          // زخرفة إسلامية ثابتة بسيطة
          Positioned.fill(
            child: RepaintBoundary(
              child: CustomPaint(
                painter: St<PERSON><PERSON>sla<PERSON>PatternPainter(
                  isDarkMode: isDarkMode,
                ),
              ),
            ),
          ),

          // المحتوى الرئيسي
          child,
        ],
      ),
    );
  }

  LinearGradient _buildStaticGradient() {
    if (isDarkMode) {
      return const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color(0xFF0D1B2A),
          Color(0xFF1B263B),
          Color(0xFF415A77),
        ],
        stops: [0.0, 0.5, 1.0],
      );
    } else {
      return const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color(0xFFF8F9FA),
          Color(0xFFE9ECEF),
          Color(0xFFDEE2E6),
        ],
        stops: [0.0, 0.5, 1.0],
      );
    }
  }
}

/// رسام زخارف إسلامية ثابتة - محسن للأداء
class StaticIslamicPatternPainter extends CustomPainter {
  final bool isDarkMode;

  StaticIslamicPatternPainter({required this.isDarkMode});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    paint.color = isDarkMode 
      ? Colors.white.withValues(alpha: 0.05)
      : const Color(0xFF2E7D32).withValues(alpha: 0.08);

    // رسم شبكة مبسطة جداً
    final patternSize = 200.0; // أكبر بكثير لتقليل العدد
    final cols = (size.width / patternSize).ceil();
    final rows = (size.height / patternSize).ceil();

    // رسم كل نمط ثالث فقط
    for (int i = 0; i < cols; i += 3) {
      for (int j = 0; j < rows; j += 3) {
        final centerX = i * patternSize;
        final centerY = j * patternSize;
        
        _drawMinimalPattern(
          canvas,
          paint,
          Offset(centerX, centerY),
          patternSize * 0.2,
        );
      }
    }
  }

  void _drawMinimalPattern(Canvas canvas, Paint paint, Offset center, double radius) {
    // رسم دائرة بسيطة فقط
    canvas.drawCircle(center, radius, paint);
    
    // خطوط بسيطة متقاطعة
    canvas.drawLine(
      Offset(center.dx - radius, center.dy),
      Offset(center.dx + radius, center.dy),
      paint,
    );
    canvas.drawLine(
      Offset(center.dx, center.dy - radius),
      Offset(center.dx, center.dy + radius),
      paint,
    );
  }

  @override
  bool shouldRepaint(StaticIslamicPatternPainter oldDelegate) {
    return oldDelegate.isDarkMode != isDarkMode;
  }
}

/// خلفية بديلة بدون أي تأثيرات - للأداء الأقصى
class MinimalSplashBackground extends StatelessWidget {
  final Widget child;
  final bool isDarkMode;

  const MinimalSplashBackground({
    super.key,
    required this.child,
    required this.isDarkMode,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: isDarkMode 
          ? const Color(0xFF1B263B)
          : const Color(0xFFF8F9FA),
      ),
      child: child,
    );
  }
}

/// خلفية متوسطة التعقيد - توازن بين الجمال والأداء
class BalancedSplashBackground extends StatelessWidget {
  final Widget child;
  final bool isDarkMode;

  const BalancedSplashBackground({
    super.key,
    required this.child,
    required this.isDarkMode,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: isDarkMode 
            ? [
                const Color(0xFF0D1B2A),
                const Color(0xFF1B263B),
              ]
            : [
                const Color(0xFFF8F9FA),
                const Color(0xFFE9ECEF),
              ],
        ),
      ),
      child: Stack(
        children: [
          // نمط بسيط جداً
          Positioned.fill(
            child: RepaintBoundary(
              child: Opacity(
                opacity: 0.03,
                child: CustomPaint(
                  painter: VerySimplePatternPainter(
                    color: isDarkMode 
                      ? Colors.white
                      : const Color(0xFF2E7D32),
                  ),
                ),
              ),
            ),
          ),
          child,
        ],
      ),
    );
  }
}

/// رسام نمط بسيط جداً
class VerySimplePatternPainter extends CustomPainter {
  final Color color;

  VerySimplePatternPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color.withValues(alpha: 0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // رسم شبكة بسيطة فقط
    final spacing = 100.0;
    
    // خطوط عمودية
    for (double x = 0; x < size.width; x += spacing) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }
    
    // خطوط أفقية
    for (double y = 0; y < size.height; y += spacing) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(VerySimplePatternPainter oldDelegate) {
    return oldDelegate.color != color;
  }
}
