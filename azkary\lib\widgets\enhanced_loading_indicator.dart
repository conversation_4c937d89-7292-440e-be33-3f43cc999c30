import 'package:flutter/material.dart';
import 'dart:math' as math;

/// مؤشر تحميل محسن مع تأثيرات بصرية جميلة
class EnhancedLoadingIndicator extends StatefulWidget {
  final double progress;
  final Color primaryColor;
  final Color backgroundColor;
  final double width;
  final double height;
  final bool isDarkMode;

  const EnhancedLoadingIndicator({
    super.key,
    required this.progress,
    required this.primaryColor,
    required this.backgroundColor,
    this.width = 250,
    this.height = 8,
    required this.isDarkMode,
  });

  @override
  State<EnhancedLoadingIndicator> createState() => _EnhancedLoadingIndicatorState();
}

class _EnhancedLoadingIndicatorState extends State<EnhancedLoadingIndicator>
    with TickerProviderStateMixin {
  late AnimationController _shimmerController;
  late AnimationController _pulseController;
  late Animation<double> _shimmerAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();

    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    )..repeat(reverse: true);

    _shimmerAnimation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _shimmerController,
      curve: Curves.easeInOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _shimmerController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // شريط التقدم المحسن
        AnimatedBuilder(
          animation: Listenable.merge([_shimmerController, _pulseController]),
          builder: (context, child) {
            return Transform.scale(
              scale: _pulseAnimation.value,
              child: Container(
                width: widget.width,
                height: widget.height,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(widget.height / 2),
                  boxShadow: [
                    BoxShadow(
                      color: widget.primaryColor.withValues(alpha: 0.3),
                      blurRadius: 8,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(widget.height / 2),
                  child: Stack(
                    children: [
                      // الخلفية
                      Container(
                        width: double.infinity,
                        height: double.infinity,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              widget.backgroundColor.withValues(alpha: 0.3),
                              widget.backgroundColor.withValues(alpha: 0.1),
                            ],
                          ),
                        ),
                      ),

                      // شريط التقدم
                      FractionallySizedBox(
                        widthFactor: widget.progress,
                        child: Container(
                          height: double.infinity,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                widget.primaryColor,
                                widget.primaryColor.withValues(alpha: 0.8),
                                widget.primaryColor,
                              ],
                              stops: [0.0, 0.5, 1.0],
                            ),
                          ),
                        ),
                      ),

                      // تأثير الشيمر
                      if (widget.progress < 1.0)
                        Positioned.fill(
                          child: Transform.translate(
                            offset: Offset(
                              _shimmerAnimation.value * widget.width,
                              0,
                            ),
                            child: Container(
                              width: widget.width * 0.3,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    Colors.transparent,
                                    Colors.white.withValues(alpha: 0.4),
                                    Colors.transparent,
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),

        const SizedBox(height: 16),

        // نص التقدم مع تأثيرات
        AnimatedBuilder(
          animation: _pulseController,
          builder: (context, child) {
            return Transform.scale(
              scale: _pulseAnimation.value,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 10,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(25),
                  gradient: LinearGradient(
                    colors: [
                      widget.primaryColor.withValues(alpha: 0.1),
                      widget.primaryColor.withValues(alpha: 0.05),
                    ],
                  ),
                  border: Border.all(
                    color: widget.primaryColor.withValues(alpha: 0.3),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: widget.primaryColor.withValues(alpha: 0.2),
                      blurRadius: 10,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // أيقونة التحميل
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          widget.primaryColor,
                        ),
                      ),
                    ),

                    const SizedBox(width: 12),

                    // نص التقدم
                    Text(
                      'جاري التحميل... ${(widget.progress * 100).toInt()}%',
                      style: TextStyle(
                        color: widget.primaryColor.withValues(alpha: 0.9),
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}

/// مؤشر تحميل دائري محسن
class EnhancedCircularProgress extends StatefulWidget {
  final double progress;
  final Color primaryColor;
  final double size;
  final double strokeWidth;

  const EnhancedCircularProgress({
    super.key,
    required this.progress,
    required this.primaryColor,
    this.size = 60,
    this.strokeWidth = 4,
  });

  @override
  State<EnhancedCircularProgress> createState() => _EnhancedCircularProgressState();
}

class _EnhancedCircularProgressState extends State<EnhancedCircularProgress>
    with SingleTickerProviderStateMixin {
  late AnimationController _rotationController;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();

    _rotationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(_rotationController);
  }

  @override
  void dispose() {
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _rotationAnimation,
      builder: (context, child) {
        return Transform.rotate(
          angle: _rotationAnimation.value,
          child: SizedBox(
            width: widget.size,
            height: widget.size,
            child: CircularProgressIndicator(
              value: widget.progress,
              strokeWidth: widget.strokeWidth,
              backgroundColor: widget.primaryColor.withValues(alpha: 0.2),
              valueColor: AlwaysStoppedAnimation<Color>(widget.primaryColor),
              strokeCap: StrokeCap.round,
            ),
          ),
        );
      },
    );
  }
}
