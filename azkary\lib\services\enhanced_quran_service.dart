import '../models/quran_model.dart';
import '../services/quran_data_service.dart';
import '../utils/logger.dart';

/// خدمة القرآن الكريم المحسنة التي تدمج النظام القديم مع الجديد
class EnhancedQuranService {
  static final EnhancedQuranService _instance =
      EnhancedQuranService._internal();
  factory EnhancedQuranService() => _instance;
  EnhancedQuranService._internal();

  final QuranDataService _quranDataService = QuranDataService();

  // Cache للبيانات
  List<Surah>? _cachedSurahs;
  final Map<int, List<Ayah>> _cachedAyahs = {};
  final Map<int, List<QuranAudioTrack>> _cachedAudio = {};

  /// الحصول على جميع السور مع البيانات المحسنة
  Future<List<Surah>> getAllSurahs({bool forceRefresh = false}) async {
    try {
      if (_cachedSurahs != null && !forceRefresh) {
        AppLogger.info(
          '📚 إرجاع السور من الذاكرة المؤقتة (${_cachedSurahs!.length} سورة)',
        );
        return _cachedSurahs!;
      }

      AppLogger.info('🔄 تحميل السور من API الجديد...');

      // تحميل السور من API الجديد
      final quranSurahs = await _quranDataService.getAllSurahs();

      // تحويل إلى نموذج Surah المحسن
      final surahs =
          quranSurahs.map((quranSurah) {
            return Surah.fromQuranSurah(quranSurah);
          }).toList();

      _cachedSurahs = surahs;
      AppLogger.info('✅ تم تحميل ${surahs.length} سورة بنجاح');

      return surahs;
    } catch (e) {
      AppLogger.error('❌ خطأ في تحميل السور: $e');
      rethrow;
    }
  }

  /// الحصول على سورة محددة
  Future<Surah?> getSurah(int surahNumber) async {
    try {
      final surahs = await getAllSurahs();
      return surahs.firstWhere(
        (surah) => surah.number == surahNumber,
        orElse: () => throw Exception('السورة رقم $surahNumber غير موجودة'),
      );
    } catch (e) {
      AppLogger.error('❌ خطأ في الحصول على السورة $surahNumber: $e');
      return null;
    }
  }

  /// الحصول على آيات سورة محددة
  Future<List<Ayah>> getSurahAyahs(
    int surahNumber, {
    bool forceRefresh = false,
  }) async {
    try {
      if (_cachedAyahs.containsKey(surahNumber) && !forceRefresh) {
        AppLogger.info('📖 إرجاع آيات السورة $surahNumber من الذاكرة المؤقتة');
        return _cachedAyahs[surahNumber]!;
      }

      AppLogger.info('🔄 تحميل آيات السورة $surahNumber من API الجديد...');

      // الحصول على معلومات السورة
      final surah = await getSurah(surahNumber);
      if (surah == null) {
        throw Exception('السورة رقم $surahNumber غير موجودة');
      }

      // تحميل الآيات من API الجديد
      final quranVerses = await _quranDataService.getSurahVerses(surahNumber);

      // تحويل إلى نموذج Ayah المحسن
      final ayahs =
          quranVerses.map((quranVerse) {
            return Ayah.fromQuranVerse(quranVerse, surahName: surah.name);
          }).toList();

      _cachedAyahs[surahNumber] = ayahs;
      AppLogger.info('✅ تم تحميل ${ayahs.length} آية للسورة ${surah.name}');

      return ayahs;
    } catch (e) {
      AppLogger.error('❌ خطأ في تحميل آيات السورة $surahNumber: $e');
      rethrow;
    }
  }

  /// الحصول على التسجيلات الصوتية لسورة محددة
  Future<List<QuranAudioTrack>> getSurahAudio(
    int surahNumber, {
    bool forceRefresh = false,
  }) async {
    try {
      if (_cachedAudio.containsKey(surahNumber) && !forceRefresh) {
        AppLogger.info(
          '🎧 إرجاع التسجيلات الصوتية للسورة $surahNumber من الذاكرة المؤقتة',
        );
        return _cachedAudio[surahNumber]!;
      }

      AppLogger.info(
        '🔄 تحميل التسجيلات الصوتية للسورة $surahNumber من API الجديد...',
      );

      // تحميل التسجيلات من API الجديد
      final quranAudios = await _quranDataService.getSurahAudio(surahNumber);

      // تحويل إلى نموذج QuranAudioTrack
      final audioTracks =
          quranAudios.map((quranAudio) {
            return QuranAudioTrack.fromQuranAudio(quranAudio, surahNumber);
          }).toList();

      _cachedAudio[surahNumber] = audioTracks;
      AppLogger.info(
        '✅ تم تحميل ${audioTracks.length} تسجيل صوتي للسورة $surahNumber',
      );

      return audioTracks;
    } catch (e) {
      AppLogger.error(
        '❌ خطأ في تحميل التسجيلات الصوتية للسورة $surahNumber: $e',
      );
      rethrow;
    }
  }

  /// الحصول على آيات السجدة
  Future<List<Ayah>> getSajdaAyahs() async {
    try {
      AppLogger.info('🕋 تحميل آيات السجدة من API الجديد...');

      // تحميل آيات السجدة من API الجديد
      final quranVerses = await _quranDataService.getSajdaVerses();

      // تحويل إلى نموذج Ayah
      final ayahs = <Ayah>[];
      for (final quranVerse in quranVerses) {
        // الحصول على اسم السورة (يمكن تحسين هذا لاحقاً)
        final surahName = 'سورة'; // مؤقت
        final ayah = Ayah.fromQuranVerse(quranVerse, surahName: surahName);
        ayahs.add(ayah);
      }

      AppLogger.info('✅ تم تحميل ${ayahs.length} آية سجدة');
      return ayahs;
    } catch (e) {
      AppLogger.error('❌ خطأ في تحميل آيات السجدة: $e');
      rethrow;
    }
  }

  /// البحث في الآيات
  Future<List<AyahSearchResult>> searchAyahs(String query) async {
    try {
      AppLogger.info('🔍 البحث عن: "$query"');

      if (query.trim().isEmpty) {
        return [];
      }

      final results = <AyahSearchResult>[];
      final surahs = await getAllSurahs();

      // البحث في كل سورة
      for (final surah in surahs) {
        try {
          final ayahs = await getSurahAyahs(surah.number);

          for (final ayah in ayahs) {
            // البحث في النص العربي
            if (_containsQuery(ayah.text, query)) {
              final searchResult = AyahSearchResult.withHighlighting(
                surah,
                ayah,
                query,
              );
              results.add(searchResult);
            }

            // البحث في الترجمة الإنجليزية إذا كانت متوفرة
            if (ayah.hasTranslation &&
                _containsQuery(ayah.englishText!, query)) {
              final searchResult = AyahSearchResult.withHighlighting(
                surah,
                ayah,
                query,
              );
              if (!results.contains(searchResult)) {
                results.add(searchResult);
              }
            }
          }
        } catch (e) {
          AppLogger.warning('تخطي السورة ${surah.name} بسبب خطأ: $e');
          continue;
        }
      }

      AppLogger.info(
        '✅ تم العثور على ${results.length} نتيجة للبحث عن "$query"',
      );
      return results;
    } catch (e) {
      AppLogger.error('❌ خطأ في البحث: $e');
      rethrow;
    }
  }

  /// فحص ما إذا كان النص يحتوي على الاستعلام
  bool _containsQuery(String text, String query) {
    final normalizedText = _normalizeArabicText(text.toLowerCase());
    final normalizedQuery = _normalizeArabicText(query.toLowerCase());
    return normalizedText.contains(normalizedQuery);
  }

  /// تطبيع النص العربي (إزالة التشكيل والهمزات)
  String _normalizeArabicText(String text) {
    // إزالة التشكيل
    final withoutTashkeel = text
        .replaceAll('\u064B', '') // فتحتين
        .replaceAll('\u064C', '') // ضمتين
        .replaceAll('\u064D', '') // كسرتين
        .replaceAll('\u064E', '') // فتحة
        .replaceAll('\u064F', '') // ضمة
        .replaceAll('\u0650', '') // كسرة
        .replaceAll('\u0651', '') // شدة
        .replaceAll('\u0652', '') // سكون
        .replaceAll('\u0653', '') // مدة
        .replaceAll('\u0654', '') // همزة فوق الحرف
        .replaceAll('\u0655', ''); // همزة تحت الحرف

    // توحيد أشكال الهمزات والألف
    return withoutTashkeel
        .replaceAll('أ', 'ا')
        .replaceAll('إ', 'ا')
        .replaceAll('آ', 'ا')
        .replaceAll('ى', 'ي')
        .replaceAll('ة', 'ه');
  }

  /// الحصول على إحصائيات القرآن الكريم
  Future<Map<String, dynamic>> getQuranStatistics() async {
    try {
      final surahs = await getAllSurahs();

      int totalAyahs = 0;
      int totalWords = 0;
      int totalLetters = 0;
      int meccanSurahs = 0;
      int medinanSurahs = 0;

      for (final surah in surahs) {
        totalAyahs += surah.numberOfAyahs;
        if (surah.wordsCount != null) totalWords += surah.wordsCount!;
        if (surah.lettersCount != null) totalLetters += surah.lettersCount!;

        if (surah.isMeccan) {
          meccanSurahs++;
        } else {
          medinanSurahs++;
        }
      }

      return {
        'totalSurahs': surahs.length,
        'totalAyahs': totalAyahs,
        'totalWords': totalWords,
        'totalLetters': totalLetters,
        'meccanSurahs': meccanSurahs,
        'medinanSurahs': medinanSurahs,
      };
    } catch (e) {
      AppLogger.error('❌ خطأ في الحصول على إحصائيات القرآن: $e');
      rethrow;
    }
  }

  /// مسح الذاكرة المؤقتة
  void clearCache() {
    _cachedSurahs = null;
    _cachedAyahs.clear();
    _cachedAudio.clear();
    AppLogger.info('🗑️ تم مسح الذاكرة المؤقتة');
  }

  /// اختبار الاتصال بـ API
  Future<bool> testConnection() async {
    return await _quranDataService.testConnection();
  }

  /// الحصول على معلومات الخدمة
  Map<String, String> getServiceInfo() {
    return {
      'name': 'Enhanced Quran Service',
      'version': '2.0',
      'api_source': 'Quran-Data GitHub',
      'features': 'Surahs, Ayahs, Audio, Search, Statistics',
      'cache_enabled': 'true',
      'compatibility': 'Backward Compatible',
    };
  }
}
