/// نماذج البيانات الجديدة للقرآن الكريم
/// باستخدام مصدر Quran-Data من GitHub
/// المصدر: https://github.com/rn0x/Quran-Data
library;

import 'quran_model.dart';

/// نموذج الاسم متعدد اللغات
class QuranName {
  final String ar;
  final String en;
  final String transliteration;

  const QuranName({
    required this.ar,
    required this.en,
    required this.transliteration,
  });

  factory QuranName.fromJson(Map<String, dynamic> json) {
    return QuranName(
      ar: json['ar'] ?? '',
      en: json['en'] ?? '',
      transliteration: json['transliteration'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'ar': ar, 'en': en, 'transliteration': transliteration};
  }
}

/// نموذج مكان النزول
class QuranRevelationPlace {
  final String ar;
  final String en;

  const QuranRevelationPlace({required this.ar, required this.en});

  factory QuranRevelationPlace.fromJson(Map<String, dynamic> json) {
    return QuranRevelationPlace(ar: json['ar'] ?? '', en: json['en'] ?? '');
  }

  Map<String, dynamic> toJson() {
    return {'ar': ar, 'en': en};
  }

  /// هل السورة مكية؟
  bool get isMeccan =>
      ar.contains('مكية') || en.toLowerCase().contains('meccan');

  /// هل السورة مدنية؟
  bool get isMedinan =>
      ar.contains('مدنية') || en.toLowerCase().contains('medinan');
}

/// نموذج السورة الأساسي
class QuranSurah {
  final int number;
  final QuranName name;
  final QuranRevelationPlace revelationPlace;
  final int versesCount;
  final int wordsCount;
  final int lettersCount;

  const QuranSurah({
    required this.number,
    required this.name,
    required this.revelationPlace,
    required this.versesCount,
    required this.wordsCount,
    required this.lettersCount,
  });

  factory QuranSurah.fromJson(Map<String, dynamic> json) {
    return QuranSurah(
      number: json['number'] ?? 0,
      name: QuranName.fromJson(json['name'] ?? {}),
      revelationPlace: QuranRevelationPlace.fromJson(
        json['revelation_place'] ?? {},
      ),
      versesCount: json['verses_count'] ?? 0,
      wordsCount: json['words_count'] ?? 0,
      lettersCount: json['letters_count'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'name': name.toJson(),
      'revelation_place': revelationPlace.toJson(),
      'verses_count': versesCount,
      'words_count': wordsCount,
      'letters_count': lettersCount,
    };
  }

  /// تحويل إلى نموذج Surah القديم للتوافق
  Surah toOldSurah() {
    return Surah(
      number: number,
      name: name.ar,
      englishName: name.en,
      englishNameTranslation: name.transliteration,
      numberOfAyahs: versesCount,
      revelationType: revelationPlace.isMeccan ? 'Meccan' : 'Medinan',
    );
  }
}

/// نموذج النص متعدد اللغات
class QuranText {
  final String ar;
  final String en;

  const QuranText({required this.ar, required this.en});

  factory QuranText.fromJson(Map<String, dynamic> json) {
    return QuranText(ar: json['ar'] ?? '', en: json['en'] ?? '');
  }

  Map<String, dynamic> toJson() {
    return {'ar': ar, 'en': en};
  }
}

/// نموذج معلومات السجدة
class QuranSajda {
  final bool sajda;

  const QuranSajda({required this.sajda});

  factory QuranSajda.fromJson(dynamic json) {
    if (json is bool) {
      return QuranSajda(sajda: json);
    } else if (json is Map<String, dynamic>) {
      return QuranSajda(sajda: json['sajda'] == true);
    } else if (json is String) {
      return QuranSajda(sajda: json.toLowerCase() == 'true');
    } else if (json is int) {
      return QuranSajda(sajda: json == 1);
    }
    return const QuranSajda(sajda: false);
  }

  Map<String, dynamic> toJson() {
    return {'sajda': sajda};
  }
}

/// نموذج الآية
class QuranVerse {
  final int number;
  final QuranText text;
  final int juz;
  final int page;
  final bool sajda;

  const QuranVerse({
    required this.number,
    required this.text,
    required this.juz,
    required this.page,
    required this.sajda,
  });

  factory QuranVerse.fromJson(Map<String, dynamic> json) {
    return QuranVerse(
      number: json['number'] ?? 0,
      text: QuranText.fromJson(json['text'] ?? {}),
      juz: json['juz'] ?? 0,
      page: json['page'] ?? 0,
      sajda: json['sajda'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'text': text.toJson(),
      'juz': juz,
      'page': page,
      'sajda': sajda,
    };
  }

  /// هل الآية تحتوي على سجدة؟
  bool get hasSajda => sajda;
}

/// نموذج القارئ
class QuranReciter {
  final String ar;
  final String en;

  const QuranReciter({required this.ar, required this.en});

  factory QuranReciter.fromJson(Map<String, dynamic> json) {
    return QuranReciter(ar: json['ar'] ?? '', en: json['en'] ?? '');
  }

  Map<String, dynamic> toJson() {
    return {'ar': ar, 'en': en};
  }
}

/// نموذج الرواية
class QuranRewaya {
  final String ar;
  final String en;

  const QuranRewaya({required this.ar, required this.en});

  factory QuranRewaya.fromJson(Map<String, dynamic> json) {
    return QuranRewaya(ar: json['ar'] ?? '', en: json['en'] ?? '');
  }

  Map<String, dynamic> toJson() {
    return {'ar': ar, 'en': en};
  }
}

/// نموذج التسجيل الصوتي
class QuranAudio {
  final int id;
  final QuranReciter reciter;
  final QuranRewaya? rewaya;
  final String? server;
  final String link;

  const QuranAudio({
    required this.id,
    required this.reciter,
    this.rewaya,
    this.server,
    required this.link,
  });

  factory QuranAudio.fromJson(Map<String, dynamic> json) {
    return QuranAudio(
      id: json['id'] ?? 0,
      reciter: QuranReciter.fromJson(json['reciter'] ?? {}),
      rewaya:
          json['rewaya'] != null ? QuranRewaya.fromJson(json['rewaya']) : null,
      server: json['server'],
      link: json['link'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'reciter': reciter.toJson(),
      'rewaya': rewaya?.toJson(),
      'server': server,
      'link': link,
    };
  }
}

/// نموذج تفاصيل السورة الكاملة
class QuranSurahDetail {
  final int number;
  final QuranName name;
  final QuranRevelationPlace revelationPlace;
  final int versesCount;
  final int wordsCount;
  final int lettersCount;
  final List<QuranVerse> verses;
  final List<QuranAudio> audio;

  const QuranSurahDetail({
    required this.number,
    required this.name,
    required this.revelationPlace,
    required this.versesCount,
    required this.wordsCount,
    required this.lettersCount,
    required this.verses,
    required this.audio,
  });

  factory QuranSurahDetail.fromJson(Map<String, dynamic> json) {
    return QuranSurahDetail(
      number: json['number'] ?? 0,
      name: QuranName.fromJson(json['name'] ?? {}),
      revelationPlace: QuranRevelationPlace.fromJson(
        json['revelation_place'] ?? {},
      ),
      versesCount: json['verses_count'] ?? 0,
      wordsCount: json['words_count'] ?? 0,
      lettersCount: json['letters_count'] ?? 0,
      verses:
          (json['verses'] as List<dynamic>?)
              ?.map((v) => QuranVerse.fromJson(v))
              .toList() ??
          [],
      audio:
          (json['audio'] as List<dynamic>?)
              ?.map((a) => QuranAudio.fromJson(a))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'name': name.toJson(),
      'revelation_place': revelationPlace.toJson(),
      'verses_count': versesCount,
      'words_count': wordsCount,
      'letters_count': lettersCount,
      'verses': verses.map((v) => v.toJson()).toList(),
      'audio': audio.map((a) => a.toJson()).toList(),
    };
  }

  /// تحويل إلى نموذج Surah القديم للتوافق
  Surah toOldSurah() {
    return Surah(
      number: number,
      name: name.ar,
      englishName: name.en,
      englishNameTranslation: name.transliteration,
      numberOfAyahs: versesCount,
      revelationType: revelationPlace.isMeccan ? 'Meccan' : 'Medinan',
    );
  }
}

/// نموذج معلومات الصفحة
class QuranPageInfo {
  final int page;
  final QuranPageImage? image;
  final QuranPagePosition start;
  final QuranPagePosition end;

  const QuranPageInfo({
    required this.page,
    this.image,
    required this.start,
    required this.end,
  });

  factory QuranPageInfo.fromJson(Map<String, dynamic> json) {
    return QuranPageInfo(
      page: json['page'] ?? 0,
      image:
          json['image'] != null ? QuranPageImage.fromJson(json['image']) : null,
      start: QuranPagePosition.fromJson(json['start'] ?? {}),
      end: QuranPagePosition.fromJson(json['end'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'page': page,
      'image': image?.toJson(),
      'start': start.toJson(),
      'end': end.toJson(),
    };
  }
}

/// نموذج صورة الصفحة
class QuranPageImage {
  final String url;

  const QuranPageImage({required this.url});

  factory QuranPageImage.fromJson(Map<String, dynamic> json) {
    return QuranPageImage(url: json['url'] ?? '');
  }

  Map<String, dynamic> toJson() {
    return {'url': url};
  }
}

/// نموذج موقع في الصفحة
class QuranPagePosition {
  final int surahNumber;
  final int verse;
  final QuranName name;

  const QuranPagePosition({
    required this.surahNumber,
    required this.verse,
    required this.name,
  });

  factory QuranPagePosition.fromJson(Map<String, dynamic> json) {
    return QuranPagePosition(
      surahNumber: json['surah_number'] ?? 0,
      verse: json['verse'] ?? 0,
      name: QuranName.fromJson(json['name'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {'surah_number': surahNumber, 'verse': verse, 'name': name.toJson()};
  }
}
