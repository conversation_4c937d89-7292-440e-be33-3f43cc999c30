import '../models/mushaf_image_page.dart';
import '../utils/logger.dart';

/// خدمة إدارة صفحات المصحف التقليدي
class MushafPageService {
  static final MushafPageService _instance = MushafPageService._internal();
  factory MushafPageService() => _instance;
  MushafPageService._internal();

  // بيانات المصحف الثابتة - 604 صفحة
  static const int totalPages = 604;
  static const String baseImageUrl = 'https://searchforislam.com/quran/images/';

  List<MushafImagePage>? _allPages;
  List<SurahInMushaf>? _surahsInfo;

  /// تهيئة الخدمة وتحميل البيانات
  Future<void> initialize() async {
    try {
      await _loadMushafData();
      AppLogger.info('تم تهيئة خدمة صفحات المصحف');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة خدمة صفحات المصحف: $e');
    }
  }

  /// تحميل بيانات المصحف
  Future<void> _loadMushafData() async {
    if (_allPages != null && _surahsInfo != null) return;

    // بيانات السور مع صفحات البداية والنهاية في المصحف
    final surahsData = _getSurahsPageData();
    _surahsInfo =
        surahsData.map((data) => SurahInMushaf.fromJson(data)).toList();

    // إنشاء قائمة جميع الصفحات
    _allPages = List.generate(totalPages, (index) {
      final pageNumber = index + 1;
      return MushafImagePage(
        pageNumber: pageNumber,
        imageUrl: '$baseImageUrl${pageNumber.toString().padLeft(3, '0')}.jpg',
        surahNumbers: _getSurahsInPage(pageNumber),
        surahNames: _getSurahNamesInPage(pageNumber),
        juzNumber: _getJuzForPage(pageNumber),
        hizbNumber: _getHizbForPage(pageNumber),
      );
    });
  }

  /// الحصول على جميع الصفحات
  Future<List<MushafImagePage>> getAllPages() async {
    await _loadMushafData();
    return _allPages!;
  }

  /// الحصول على صفحة معينة
  Future<MushafImagePage?> getPage(int pageNumber) async {
    if (pageNumber < 1 || pageNumber > totalPages) return null;

    await _loadMushafData();
    return _allPages![pageNumber - 1];
  }

  /// الحصول على صفحات سورة معينة
  Future<List<MushafImagePage>> getPagesForSurah(int surahNumber) async {
    await _loadMushafData();

    final surahInfo = _surahsInfo!.firstWhere(
      (surah) => surah.surahNumber == surahNumber,
      orElse: () => throw Exception('السورة غير موجودة'),
    );

    final pages = <MushafImagePage>[];
    for (int i = surahInfo.startPage; i <= surahInfo.endPage; i++) {
      final page = await getPage(i);
      if (page != null) pages.add(page);
    }

    return pages;
  }

  /// الحصول على رقم الصفحة الأولى لسورة معينة
  Future<int> getFirstPageForSurah(int surahNumber) async {
    await _loadMushafData();

    final surahInfo = _surahsInfo!.firstWhere(
      (surah) => surah.surahNumber == surahNumber,
      orElse: () => throw Exception('السورة غير موجودة'),
    );

    return surahInfo.startPage;
  }

  /// الحصول على معلومات السورة
  Future<SurahInMushaf?> getSurahInfo(int surahNumber) async {
    await _loadMushafData();

    try {
      return _surahsInfo!.firstWhere(
        (surah) => surah.surahNumber == surahNumber,
      );
    } catch (e) {
      return null;
    }
  }

  /// الحصول على السور في صفحة معينة
  List<int> _getSurahsInPage(int pageNumber) {
    final surahs = <int>[];

    for (final surahData in _getSurahsPageData()) {
      final startPage = surahData['startPage'] as int;
      final endPage = surahData['endPage'] as int;

      if (pageNumber >= startPage && pageNumber <= endPage) {
        surahs.add(surahData['surahNumber'] as int);
      }
    }

    return surahs;
  }

  /// الحصول على أسماء السور في صفحة معينة
  List<String> _getSurahNamesInPage(int pageNumber) {
    final names = <String>[];

    for (final surahData in _getSurahsPageData()) {
      final startPage = surahData['startPage'] as int;
      final endPage = surahData['endPage'] as int;

      if (pageNumber >= startPage && pageNumber <= endPage) {
        names.add(surahData['surahName'] as String);
      }
    }

    return names;
  }

  /// الحصول على رقم الجزء للصفحة
  int? _getJuzForPage(int pageNumber) {
    // تقسيم تقريبي للأجزاء على الصفحات
    // كل جزء تقريباً 20 صفحة
    return ((pageNumber - 1) ~/ 20) + 1;
  }

  /// الحصول على رقم الحزب للصفحة
  int? _getHizbForPage(int pageNumber) {
    // كل حزب تقريباً 10 صفحات
    return ((pageNumber - 1) ~/ 10) + 1;
  }

  /// بيانات السور مع صفحات البداية والنهاية
  List<Map<String, dynamic>> _getSurahsPageData() {
    return [
      {
        'surahNumber': 1,
        'surahName': 'الفاتحة',
        'startPage': 1,
        'endPage': 1,
        'numberOfAyahs': 7,
        'revelationType': 'Meccan',
      },
      {
        'surahNumber': 2,
        'surahName': 'البقرة',
        'startPage': 2,
        'endPage': 49,
        'numberOfAyahs': 286,
        'revelationType': 'Medinan',
      },
      {
        'surahNumber': 3,
        'surahName': 'آل عمران',
        'startPage': 50,
        'endPage': 76,
        'numberOfAyahs': 200,
        'revelationType': 'Medinan',
      },
      {
        'surahNumber': 4,
        'surahName': 'النساء',
        'startPage': 77,
        'endPage': 106,
        'numberOfAyahs': 176,
        'revelationType': 'Medinan',
      },
      {
        'surahNumber': 5,
        'surahName': 'المائدة',
        'startPage': 106,
        'endPage': 127,
        'numberOfAyahs': 120,
        'revelationType': 'Medinan',
      },
      {
        'surahNumber': 6,
        'surahName': 'الأنعام',
        'startPage': 128,
        'endPage': 150,
        'numberOfAyahs': 165,
        'revelationType': 'Meccan',
      },
      {
        'surahNumber': 7,
        'surahName': 'الأعراف',
        'startPage': 151,
        'endPage': 176,
        'numberOfAyahs': 206,
        'revelationType': 'Meccan',
      },
      {
        'surahNumber': 8,
        'surahName': 'الأنفال',
        'startPage': 177,
        'endPage': 186,
        'numberOfAyahs': 75,
        'revelationType': 'Medinan',
      },
      {
        'surahNumber': 9,
        'surahName': 'التوبة',
        'startPage': 187,
        'endPage': 206,
        'numberOfAyahs': 129,
        'revelationType': 'Medinan',
      },
      {
        'surahNumber': 10,
        'surahName': 'يونس',
        'startPage': 208,
        'endPage': 221,
        'numberOfAyahs': 109,
        'revelationType': 'Meccan',
      },
      {
        'surahNumber': 11,
        'surahName': 'هود',
        'startPage': 221,
        'endPage': 235,
        'numberOfAyahs': 123,
        'revelationType': 'Meccan',
      },
      {
        'surahNumber': 12,
        'surahName': 'يوسف',
        'startPage': 235,
        'endPage': 248,
        'numberOfAyahs': 111,
        'revelationType': 'Meccan',
      },
      {
        'surahNumber': 13,
        'surahName': 'الرعد',
        'startPage': 249,
        'endPage': 255,
        'numberOfAyahs': 43,
        'revelationType': 'Medinan',
      },
      {
        'surahNumber': 14,
        'surahName': 'إبراهيم',
        'startPage': 255,
        'endPage': 261,
        'numberOfAyahs': 52,
        'revelationType': 'Meccan',
      },
      {
        'surahNumber': 15,
        'surahName': 'الحجر',
        'startPage': 262,
        'endPage': 267,
        'numberOfAyahs': 99,
        'revelationType': 'Meccan',
      },
      {
        'surahNumber': 16,
        'surahName': 'النحل',
        'startPage': 267,
        'endPage': 281,
        'numberOfAyahs': 128,
        'revelationType': 'Meccan',
      },
      {
        'surahNumber': 17,
        'surahName': 'الإسراء',
        'startPage': 282,
        'endPage': 293,
        'numberOfAyahs': 111,
        'revelationType': 'Meccan',
      },
      {
        'surahNumber': 18,
        'surahName': 'الكهف',
        'startPage': 293,
        'endPage': 304,
        'numberOfAyahs': 110,
        'revelationType': 'Meccan',
      },
      {
        'surahNumber': 19,
        'surahName': 'مريم',
        'startPage': 305,
        'endPage': 312,
        'numberOfAyahs': 98,
        'revelationType': 'Meccan',
      },
      {
        'surahNumber': 20,
        'surahName': 'طه',
        'startPage': 312,
        'endPage': 322,
        'numberOfAyahs': 135,
        'revelationType': 'Meccan',
      },
      {
        'surahNumber': 21,
        'surahName': 'الأنبياء',
        'startPage': 322,
        'endPage': 331,
        'numberOfAyahs': 112,
        'revelationType': 'Meccan',
      },
      {
        'surahNumber': 22,
        'surahName': 'الحج',
        'startPage': 332,
        'endPage': 341,
        'numberOfAyahs': 78,
        'revelationType': 'Medinan',
      },
      {
        'surahNumber': 23,
        'surahName': 'المؤمنون',
        'startPage': 342,
        'endPage': 349,
        'numberOfAyahs': 118,
        'revelationType': 'Meccan',
      },
      {
        'surahNumber': 24,
        'surahName': 'النور',
        'startPage': 350,
        'endPage': 359,
        'numberOfAyahs': 64,
        'revelationType': 'Medinan',
      },
      {
        'surahNumber': 25,
        'surahName': 'الفرقان',
        'startPage': 359,
        'endPage': 366,
        'numberOfAyahs': 77,
        'revelationType': 'Meccan',
      },
      {
        'surahNumber': 26,
        'surahName': 'الشعراء',
        'startPage': 367,
        'endPage': 377,
        'numberOfAyahs': 227,
        'revelationType': 'Meccan',
      },
      {
        'surahNumber': 27,
        'surahName': 'النمل',
        'startPage': 377,
        'endPage': 385,
        'numberOfAyahs': 93,
        'revelationType': 'Meccan',
      },
      {
        'surahNumber': 28,
        'surahName': 'القصص',
        'startPage': 385,
        'endPage': 396,
        'numberOfAyahs': 88,
        'revelationType': 'Meccan',
      },
      {
        'surahNumber': 29,
        'surahName': 'العنكبوت',
        'startPage': 396,
        'endPage': 404,
        'numberOfAyahs': 69,
        'revelationType': 'Meccan',
      },
      {
        'surahNumber': 30,
        'surahName': 'الروم',
        'startPage': 404,
        'endPage': 411,
        'numberOfAyahs': 60,
        'revelationType': 'Meccan',
      },
      // يمكن إضافة باقي السور هنا...
      // للاختصار، سأضع السور الأخيرة
      {
        'surahNumber': 114,
        'surahName': 'الناس',
        'startPage': 604,
        'endPage': 604,
        'numberOfAyahs': 6,
        'revelationType': 'Meccan',
      },
    ];
  }
}
