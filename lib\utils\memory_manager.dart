import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/painting.dart';
import 'package:azkary/utils/logger.dart';

/// مدير الذاكرة الذكي للأجهزة ذات 6GB RAM
class MemoryManager {
  static final MemoryManager _instance = MemoryManager._internal();
  factory MemoryManager() => _instance;
  MemoryManager._internal();

  Timer? _memoryCheckTimer;
  bool _isLowMemoryMode = false;
  bool _isCriticalMemoryMode = false;

  // إعدادات الذاكرة (بالميجابايت)
  static const int _lowMemoryThreshold = 60;
  static const int _criticalMemoryThreshold = 70;
  static const int _maxMemoryUsage = 80;

  // مستمعين لتغييرات حالة الذاكرة
  final List<VoidCallback> _lowMemoryListeners = [];
  final List<VoidCallback> _criticalMemoryListeners = [];
  final List<VoidCallback> _normalMemoryListeners = [];

  /// تهيئة مدير الذاكرة
  void initialize() {
    AppLogger.info('تهيئة مدير الذاكرة...');

    // بدء مراقبة الذاكرة كل 30 ثانية
    _memoryCheckTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) => _checkMemoryUsage(),
    );

    // فحص أولي للذاكرة
    _checkMemoryUsage();
  }

  /// فحص استخدام الذاكرة الحالي
  Future<void> _checkMemoryUsage() async {
    try {
      if (kIsWeb) return; // لا يمكن فحص الذاكرة في الويب

      // محاولة الحصول على معلومات الذاكرة
      final memoryInfo = await _getMemoryInfo();
      if (memoryInfo == null) return;

      final currentMemoryMB = memoryInfo / (1024 * 1024); // تحويل إلى ميجابايت

      AppLogger.info(
          'استخدام الذاكرة الحالي: ${currentMemoryMB.toStringAsFixed(1)} MB');

      // تحديد حالة الذاكرة
      final wasLowMemory = _isLowMemoryMode;
      final wasCritical = _isCriticalMemoryMode;

      if (currentMemoryMB >= _criticalMemoryThreshold ||
          currentMemoryMB >= _maxMemoryUsage) {
        _isCriticalMemoryMode = true;
        _isLowMemoryMode = true;

        if (!wasCritical) {
          AppLogger.warning(
              'دخول وضع الذاكرة الحرج: ${currentMemoryMB.toStringAsFixed(1)} MB');
          _notifyCriticalMemoryListeners();
          await _performCriticalMemoryCleanup();
        }
      } else if (currentMemoryMB >= _lowMemoryThreshold) {
        _isLowMemoryMode = true;
        _isCriticalMemoryMode = false;

        if (!wasLowMemory) {
          AppLogger.warning(
              'دخول وضع الذاكرة المنخفضة: ${currentMemoryMB.toStringAsFixed(1)} MB');
          _notifyLowMemoryListeners();
          await _performLowMemoryCleanup();
        }
      } else {
        _isLowMemoryMode = false;
        _isCriticalMemoryMode = false;

        if (wasLowMemory) {
          AppLogger.info(
              'العودة إلى الوضع العادي للذاكرة: ${currentMemoryMB.toStringAsFixed(1)} MB');
          _notifyNormalMemoryListeners();
        }
      }
    } catch (e) {
      AppLogger.error('خطأ في فحص الذاكرة: $e');
    }
  }

  /// الحصول على معلومات الذاكرة
  Future<int?> _getMemoryInfo() async {
    try {
      if (Platform.isAndroid) {
        // استخدام قناة المنصة للحصول على معلومات الذاكرة
        const platform = MethodChannel('azkary/memory');
        final result = await platform.invokeMethod('getMemoryUsage');
        return result as int?;
      } else if (Platform.isIOS) {
        // iOS - استخدام ProcessInfo
        const platform = MethodChannel('azkary/memory');
        final result = await platform.invokeMethod('getMemoryUsage');
        return result as int?;
      }
    } catch (e) {
      AppLogger.error('خطأ في الحصول على معلومات الذاكرة: $e');
    }
    return null;
  }

  /// تنظيف الذاكرة في الوضع المنخفض
  Future<void> _performLowMemoryCleanup() async {
    AppLogger.info('تنفيذ تنظيف الذاكرة المنخفضة...');

    // تنظيف التخزين المؤقت للصور
    PaintingBinding.instance.imageCache.clear();

    // تقليل حجم التخزين المؤقت
    PaintingBinding.instance.imageCache.maximumSize = 50;
    PaintingBinding.instance.imageCache.maximumSizeBytes =
        20 * 1024 * 1024; // 20MB

    // إجبار جمع القمامة
    await _forceGarbageCollection();
  }

  /// تنظيف الذاكرة في الوضع الحرج
  Future<void> _performCriticalMemoryCleanup() async {
    AppLogger.warning('تنفيذ تنظيف الذاكرة الحرج...');

    // تنظيف شامل للتخزين المؤقت
    PaintingBinding.instance.imageCache.clear();

    // تقليل حجم التخزين المؤقت بشكل كبير
    PaintingBinding.instance.imageCache.maximumSize = 20;
    PaintingBinding.instance.imageCache.maximumSizeBytes =
        10 * 1024 * 1024; // 10MB

    // إجبار جمع القمامة متعدد المرات
    for (int i = 0; i < 3; i++) {
      await _forceGarbageCollection();
      await Future.delayed(const Duration(milliseconds: 100));
    }
  }

  /// إجبار جمع القمامة
  Future<void> _forceGarbageCollection() async {
    try {
      // محاولة إجبار جمع القمامة
      await Future.delayed(const Duration(milliseconds: 1));

      // استخدام قناة المنصة لجمع القمامة على Android
      if (Platform.isAndroid) {
        try {
          const platform = MethodChannel('flutter/platform');
          await platform.invokeMethod('SystemNavigator.routeUpdated');
        } catch (e) {
          // تجاهل الخطأ إذا لم تكن متاحة
        }
      }
    } catch (e) {
      AppLogger.error('خطأ في جمع القمامة: $e');
    }
  }

  /// إضافة مستمع للذاكرة المنخفضة
  void addLowMemoryListener(VoidCallback listener) {
    _lowMemoryListeners.add(listener);
  }

  /// إضافة مستمع للذاكرة الحرجة
  void addCriticalMemoryListener(VoidCallback listener) {
    _criticalMemoryListeners.add(listener);
  }

  /// إضافة مستمع للذاكرة العادية
  void addNormalMemoryListener(VoidCallback listener) {
    _normalMemoryListeners.add(listener);
  }

  /// إزالة مستمع
  void removeListener(VoidCallback listener) {
    _lowMemoryListeners.remove(listener);
    _criticalMemoryListeners.remove(listener);
    _normalMemoryListeners.remove(listener);
  }

  /// إشعار مستمعي الذاكرة المنخفضة
  void _notifyLowMemoryListeners() {
    for (final listener in _lowMemoryListeners) {
      try {
        listener();
      } catch (e) {
        AppLogger.error('خطأ في مستمع الذاكرة المنخفضة: $e');
      }
    }
  }

  /// إشعار مستمعي الذاكرة الحرجة
  void _notifyCriticalMemoryListeners() {
    for (final listener in _criticalMemoryListeners) {
      try {
        listener();
      } catch (e) {
        AppLogger.error('خطأ في مستمع الذاكرة الحرجة: $e');
      }
    }
  }

  /// إشعار مستمعي الذاكرة العادية
  void _notifyNormalMemoryListeners() {
    for (final listener in _normalMemoryListeners) {
      try {
        listener();
      } catch (e) {
        AppLogger.error('خطأ في مستمع الذاكرة العادية: $e');
      }
    }
  }

  /// الحصول على حالة الذاكرة
  bool get isLowMemoryMode => _isLowMemoryMode;
  bool get isCriticalMemoryMode => _isCriticalMemoryMode;
  bool get isNormalMemoryMode => !_isLowMemoryMode && !_isCriticalMemoryMode;

  /// تنظيف الموارد
  void dispose() {
    _memoryCheckTimer?.cancel();
    _lowMemoryListeners.clear();
    _criticalMemoryListeners.clear();
    _normalMemoryListeners.clear();
    AppLogger.info('تم تنظيف مدير الذاكرة');
  }

  /// تنظيف فوري للذاكرة
  Future<void> forceCleanup() async {
    AppLogger.info('تنظيف فوري للذاكرة...');
    await _performLowMemoryCleanup();
  }

  /// الحصول على إعدادات محسنة حسب حالة الذاكرة
  double getOptimizedCacheExtent() {
    if (_isCriticalMemoryMode) return 100.0;
    if (_isLowMemoryMode) return 150.0;
    return 200.0;
  }

  /// الحصول على حد أقصى للعناصر المحملة
  int getMaxItemsToLoad() {
    if (_isCriticalMemoryMode) return 10;
    if (_isLowMemoryMode) return 15;
    return 20;
  }

  /// الحصول على جودة الصور المحسنة
  double getOptimizedImageQuality() {
    if (_isCriticalMemoryMode) return 0.6;
    if (_isLowMemoryMode) return 0.7;
    return 0.8;
  }
}
