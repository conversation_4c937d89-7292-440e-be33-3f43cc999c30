import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../models/quran_model.dart';
import '../services/quran_provider.dart';
import '../services/theme_provider.dart';
import '../models/quran_view_mode.dart';
import '../widgets/enhanced_surah_card.dart';
import 'surah_detail_screen.dart';

class QuranScreen extends StatefulWidget {
  const QuranScreen({super.key});

  @override
  State<QuranScreen> createState() => _QuranScreenState();
}

class _QuranScreenState extends State<QuranScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeQuranProvider();
    });
  }

  Future<void> _initializeQuranProvider() async {
    final quranProvider = Provider.of<QuranProvider>(context, listen: false);
    if (!_isInitialized) {
      await quranProvider.initialize();
      _isInitialized = true;
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final themeProvider = Provider.of<ThemeProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('القرآن الكريم'),
        centerTitle: true,
        actions: [
          // زر تغيير وضع العرض
          PopupMenuButton<QuranViewMode>(
            tooltip: 'طريقة العرض',
            icon: Icon(
              Icons.view_list,
              color: theme.colorScheme.onSurface,
              size: 24,
            ),
            onSelected: (QuranViewMode mode) {
              HapticFeedback.lightImpact();
              themeProvider.setQuranViewMode(mode);
            },
            itemBuilder: (BuildContext context) =>
                <PopupMenuEntry<QuranViewMode>>[
              PopupMenuItem<QuranViewMode>(
                value: QuranViewMode.list,
                child: Row(
                  children: [
                    Icon(
                      Icons.view_list,
                      color: themeProvider.quranViewMode == QuranViewMode.list
                          ? theme.colorScheme.primary
                          : null,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'قائمة',
                      style: TextStyle(
                        color: themeProvider.quranViewMode == QuranViewMode.list
                            ? theme.colorScheme.primary
                            : null,
                        fontWeight:
                            themeProvider.quranViewMode == QuranViewMode.list
                                ? FontWeight.bold
                                : null,
                      ),
                    ),
                  ],
                ),
              ),
              PopupMenuItem<QuranViewMode>(
                value: QuranViewMode.pages,
                child: Row(
                  children: [
                    Icon(
                      Icons.view_module,
                      color: themeProvider.quranViewMode == QuranViewMode.pages
                          ? theme.colorScheme.primary
                          : null,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'صفحات',
                      style: TextStyle(
                        color:
                            themeProvider.quranViewMode == QuranViewMode.pages
                                ? theme.colorScheme.primary
                                : null,
                        fontWeight:
                            themeProvider.quranViewMode == QuranViewMode.pages
                                ? FontWeight.bold
                                : null,
                      ),
                    ),
                  ],
                ),
              ),
              PopupMenuItem<QuranViewMode>(
                value: QuranViewMode.mushaf,
                child: Row(
                  children: [
                    Icon(
                      Icons.menu_book,
                      color: themeProvider.quranViewMode == QuranViewMode.mushaf
                          ? theme.colorScheme.primary
                          : null,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'مصحف',
                      style: TextStyle(
                        color:
                            themeProvider.quranViewMode == QuranViewMode.mushaf
                                ? theme.colorScheme.primary
                                : null,
                        fontWeight:
                            themeProvider.quranViewMode == QuranViewMode.mushaf
                                ? FontWeight.bold
                                : null,
                      ),
                    ),
                  ],
                ),
              ),
              PopupMenuItem<QuranViewMode>(
                value: QuranViewMode.uthmaniContinuous,
                child: Row(
                  children: [
                    Icon(
                      Icons.text_fields, // Using a generic text icon for now
                      color: themeProvider.quranViewMode ==
                              QuranViewMode.uthmaniContinuous
                          ? theme.colorScheme.primary
                          : null,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'عثماني مستمر',
                      style: TextStyle(
                        color: themeProvider.quranViewMode ==
                                QuranViewMode.uthmaniContinuous
                            ? theme.colorScheme.primary
                            : null,
                        fontWeight: themeProvider.quranViewMode ==
                                QuranViewMode.uthmaniContinuous
                            ? FontWeight.bold
                            : null,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Stack(
        children: [
          Positioned.fill(
            child: Opacity(
              opacity: 0.05,
              child: Container(
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withAlpha(26),
                ),
              ),
            ),
          ),
          Consumer<QuranProvider>(
            builder: (context, quranProvider, child) {
              if (quranProvider.isLoading) {
                return _buildLoadingState();
              }

              if (quranProvider.error.isNotEmpty) {
                return _buildErrorState(quranProvider.error);
              }

              if (quranProvider.surahs.isEmpty) {
                return _buildEmptyState();
              }

              return _buildSurahsList(quranProvider.surahs);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 10,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Container(
            height: 80,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      },
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                final quranProvider = Provider.of<QuranProvider>(
                  context,
                  listen: false,
                );
                quranProvider.loadSurahs();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.menu_book,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد سور متاحة',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'يرجى التحقق من اتصالك بالإنترنت أو إعادة تحميل البيانات',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                HapticFeedback.lightImpact();
                final quranProvider = Provider.of<QuranProvider>(
                  context,
                  listen: false,
                );
                quranProvider.loadSurahs();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('تحديث'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSurahsList(List<Surah> surahs) {
    final themeProvider = Provider.of<ThemeProvider>(context);

    // عرض السور حسب وضع العرض المحدد
    switch (themeProvider.quranViewMode) {
      case QuranViewMode.list:
        return RepaintBoundary(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: surahs.length,
            addAutomaticKeepAlives: false, // تحسين الذاكرة
            addRepaintBoundaries: true, // تحسين الرسم
            cacheExtent: 500, // تحسين التخزين المؤقت
            itemBuilder: (context, index) {
              final surah = surahs[index];
              return RepaintBoundary(
                child: EnhancedSurahCard(
                  surah: surah,
                  showDetailedInfo: true,
                  showAudioButton: true,
                ),
              );
            },
          ),
        );

      case QuranViewMode.pages:
        return RepaintBoundary(
          child: GridView.builder(
            padding: const EdgeInsets.all(16),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2, // عرض سورتين في كل صف
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.5, // نسبة العرض للارتفاع
            ),
            itemCount: surahs.length,
            addAutomaticKeepAlives: false, // تحسين الذاكرة
            addRepaintBoundaries: true, // تحسين الرسم
            cacheExtent: 500, // تحسين التخزين المؤقت
            itemBuilder: (context, index) {
              final surah = surahs[index];
              return RepaintBoundary(
                child: EnhancedSurahCard(
                  surah: surah,
                  showDetailedInfo: false,
                  showAudioButton: true,
                ),
              );
            },
          ),
        );

      case QuranViewMode.mushaf:
        return GridView.builder(
          padding: const EdgeInsets.all(16),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3, // عرض ثلاث سور في كل صف
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 0.8, // نسبة العرض للارتفاع
          ),
          itemCount: surahs.length,
          itemBuilder: (context, index) {
            final surah = surahs[index];
            return _buildSurahMushafItem(surah);
          },
        );

      case QuranViewMode.uthmaniContinuous:
        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: surahs.length,
          itemBuilder: (context, index) {
            final surah = surahs[index];
            return EnhancedSurahCard(
              surah: surah,
              showDetailedInfo: true,
              showAudioButton: true,
            );
          },
        );
    }
  }

  // عنصر المصحف (وضع المصحف)
  Widget _buildSurahMushafItem(Surah surah) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          HapticFeedback.lightImpact();
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => SurahDetailScreen(surah: surah),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // رقم السورة
              Container(
                width: 30,
                height: 30,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withAlpha(25),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: theme.colorScheme.primary.withAlpha(75),
                    width: 1,
                  ),
                ),
                child: Center(
                  child: Text(
                    '${surah.number}',
                    style: TextStyle(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 6),

              // اسم السورة
              Text(
                surah.name,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 2),

              // عدد الآيات
              Text(
                '${surah.numberOfAyahs} آية',
                style: theme.textTheme.bodySmall?.copyWith(
                  fontSize: 10,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 2),

              // نوع السورة
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                decoration: BoxDecoration(
                  color: surah.revelationType == 'Meccan'
                      ? Colors.amber.withAlpha(50)
                      : Colors.green.withAlpha(50),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  surah.revelationType == 'Meccan' ? 'مكية' : 'مدنية',
                  style: TextStyle(
                    fontSize: 8,
                    color: surah.revelationType == 'Meccan'
                        ? Colors.amber.shade800
                        : Colors.green.shade800,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
