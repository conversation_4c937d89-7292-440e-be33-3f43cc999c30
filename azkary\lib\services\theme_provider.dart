import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/theme_model.dart';
import '../models/theme_mode_enum.dart';
import '../models/quran_view_mode.dart';
import '../models/ayah_view_mode.dart';
import '../theme/app_theme.dart';

class ThemeProvider extends ChangeNotifier {
  // الألوان المتاحة
  final List<ThemeModel> _availableThemes = ThemeModel.availableThemes;

  // اللون الحالي
  ThemeModel _currentTheme =
      ThemeModel.availableThemes[0]; // اللون الافتراضي هو الأخضر

  // وضع السمة (فاتح، معتم، ليلي)
  AppThemeMode _themeMode = AppThemeMode.light;

  // حجم الخط
  double _fontSize = 1.0; // القيمة الافتراضية (1.0 = 100%)

  // طريقة عرض القرآن الكريم
  QuranViewMode _quranViewMode = QuranViewMode.list; // القيمة الافتراضية

  // طريقة عرض الآيات داخل السور
  AyahViewMode _ayahViewMode =
      AyahViewMode
          .continuousScroll; // تغيير القيمة الافتراضية إلى وضع التمرير المتواصل

  // كائنات السمة
  ThemeData? _lightThemeData;
  ThemeData? _dimThemeData;
  ThemeData? _darkThemeData;

  // الحصول على قائمة الألوان المتاحة
  List<ThemeModel> get availableThemes => _availableThemes;

  // الحصول على اللون الحالي
  ThemeModel get currentThemeColor => _currentTheme;

  // الحصول على وضع السمة الحالي
  AppThemeMode get themeMode => _themeMode;

  // للتوافق مع الكود القديم
  bool get isDarkMode => _themeMode == AppThemeMode.dark;

  // الحصول على حجم الخط
  double get fontSize => _fontSize;

  // الحصول على طريقة عرض القرآن الكريم
  QuranViewMode get quranViewMode => _quranViewMode;

  // الحصول على طريقة عرض الآيات
  AyahViewMode get ayahViewMode => _ayahViewMode;

  // الحصول على السمة الحالية
  ThemeData get currentTheme {
    switch (_themeMode) {
      case AppThemeMode.light:
        return lightTheme;
      case AppThemeMode.dim:
        return dimTheme;
      case AppThemeMode.dark:
        return darkTheme;
    }
  }

  // الحصول على سمة الوضع الفاتح
  ThemeData get lightTheme {
    // إذا لم يتم إنشاء السمة بعد، قم بإنشائها
    _lightThemeData ??= AppTheme.lightTheme(
      primaryColor: _currentTheme.primaryColor,
      fontSize: _fontSize,
    );
    return _lightThemeData!;
  }

  // الحصول على سمة الوضع المعتم
  ThemeData get dimTheme {
    // إذا لم يتم إنشاء السمة بعد، قم بإنشائها
    _dimThemeData ??= AppTheme.dimTheme(
      primaryColor: _currentTheme.primaryColor,
      fontSize: _fontSize,
    );
    return _dimThemeData!;
  }

  // الحصول على سمة الوضع الليلي
  ThemeData get darkTheme {
    // إذا لم يتم إنشاء السمة بعد، قم بإنشائها
    _darkThemeData ??= AppTheme.darkTheme(
      primaryColor: _currentTheme.primaryColor,
      fontSize: _fontSize,
    );
    return _darkThemeData!;
  }

  // المُنشئ - يقوم بتحميل الإعدادات عند إنشاء المزود
  ThemeProvider() {
    _loadThemePreference();
  }

  // تحميل إعدادات السمة من التخزين المحلي
  Future<void> _loadThemePreference() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();

    // تحميل وضع السمة (فاتح، معتم، ليلي)
    String themeModeStr = prefs.getString('theme_mode') ?? 'light';
    _themeMode = AppThemeMode.fromString(themeModeStr);

    // تحميل لون السمة
    int themeColorValue =
        prefs.getInt('theme_color') ?? _availableThemes[0].colorValue;

    // تحميل حجم الخط
    _fontSize = prefs.getDouble('font_size') ?? 1.0;

    // تحميل طريقة عرض القرآن الكريم
    String quranViewModeStr = prefs.getString('quran_view_mode') ?? 'list';
    _quranViewMode = _getQuranViewModeFromString(quranViewModeStr);

    // تحميل طريقة عرض الآيات
    String ayahViewModeStr =
        prefs.getString('ayah_view_mode') ?? 'continuousScroll';
    _ayahViewMode = _getAyahViewModeFromString(ayahViewModeStr);

    // البحث عن اللون في القائمة المتاحة
    int themeIndex = _availableThemes.indexWhere(
      (theme) => theme.colorValue == themeColorValue,
    );

    // إذا لم يتم العثور على اللون، استخدم اللون الافتراضي
    if (themeIndex != -1) {
      _currentTheme = _availableThemes[themeIndex];
    }

    notifyListeners();
  }

  // تبديل وضع السمة بين الفاتح والمعتم والليلي
  Future<void> toggleTheme() async {
    // تبديل الوضع بالتسلسل: فاتح -> معتم -> ليلي -> فاتح
    switch (_themeMode) {
      case AppThemeMode.light:
        _themeMode = AppThemeMode.dim;
        break;
      case AppThemeMode.dim:
        _themeMode = AppThemeMode.dark;
        break;
      case AppThemeMode.dark:
        _themeMode = AppThemeMode.light;
        break;
    }

    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('theme_mode', _themeMode.toString().split('.').last);

    // إعادة بناء السمة بالكامل لتطبيق التغييرات فوراً
    _rebuildTheme();

    // طباعة حالة وضع السمة للتأكد من تغييره
    debugPrint(
      'Theme mode changed to: ${_themeMode.toString().split('.').last}',
    );

    // إخطار المستمعين بالتغيير
    notifyListeners();
  }

  // تعيين وضع السمة مباشرة
  Future<void> setThemeMode(AppThemeMode mode) async {
    if (_themeMode != mode) {
      _themeMode = mode;
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'theme_mode',
        _themeMode.toString().split('.').last,
      );

      // إعادة بناء السمة بالكامل لتطبيق التغييرات فوراً
      _rebuildTheme();

      // إخطار المستمعين بالتغيير
      notifyListeners();
    }
  }

  // تغيير لون السمة
  Future<void> setThemeColor(ThemeModel theme) async {
    if (_currentTheme.colorValue != theme.colorValue) {
      _currentTheme = theme;
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setInt('theme_color', theme.colorValue);

      // إعادة بناء السمة بالكامل لتطبيق التغييرات فوراً
      _rebuildTheme();

      notifyListeners();
    }
  }

  // تغيير حجم الخط
  Future<void> setFontSize(double size) async {
    // التأكد من أن حجم الخط ضمن النطاق المسموح به (0.8 إلى 1.5)
    double newSize = size.clamp(0.8, 1.5);

    if (_fontSize != newSize) {
      _fontSize = newSize;
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setDouble('font_size', newSize);

      // إعادة بناء السمة بالكامل لتطبيق التغييرات فوراً
      _rebuildTheme();

      notifyListeners();
    }
  }

  // تحويل النص إلى طريقة عرض القرآن الكريم
  QuranViewMode _getQuranViewModeFromString(String value) {
    switch (value) {
      case 'list':
        return QuranViewMode.list;
      case 'pages':
        return QuranViewMode.pages;
      case 'mushaf':
        return QuranViewMode.mushaf;
      case 'verticalScroll':
        return QuranViewMode.verticalScroll;
      default:
        return QuranViewMode.list;
    }
  }

  // تحويل النص إلى طريقة عرض الآيات
  AyahViewMode _getAyahViewModeFromString(String value) {
    switch (value) {
      case 'list':
        return AyahViewMode.list;
      case 'simple': // للتوافق مع الإصدارات السابقة
        return AyahViewMode.list;
      case 'tafsir':
        return AyahViewMode.tafsir;
      case 'continuousScroll':
        return AyahViewMode.continuousScroll;
      case 'surahMushaf':
        return AyahViewMode.surahMushaf;
      default:
        return AyahViewMode.list;
    }
  }

  // تغيير طريقة عرض القرآن الكريم
  Future<void> setQuranViewMode(QuranViewMode mode) async {
    if (_quranViewMode != mode) {
      _quranViewMode = mode;
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'quran_view_mode',
        _quranViewMode.toString().split('.').last,
      );

      // إخطار المستمعين بالتغيير
      notifyListeners();

      // طباعة معلومات طريقة العرض للتأكد من تغييرها
      debugPrint(
        'Quran view mode changed to: ${_quranViewMode.toString().split('.').last}',
      );
    }
  }

  // تغيير طريقة عرض الآيات
  Future<void> setAyahViewMode(AyahViewMode mode) async {
    if (_ayahViewMode != mode) {
      _ayahViewMode = mode;
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'ayah_view_mode',
        _ayahViewMode.toString().split('.').last,
      );

      // إخطار المستمعين بالتغيير
      notifyListeners();

      // طباعة معلومات طريقة العرض للتأكد من تغييرها
      debugPrint(
        'Ayah view mode changed to: ${_ayahViewMode.toString().split('.').last}',
      );
    }
  }

  // إعادة بناء السمة بالكامل
  void _rebuildTheme() {
    // إعادة إنشاء السمات مع حجم الخط الجديد
    final lightThemeData = AppTheme.lightTheme(
      primaryColor: _currentTheme.primaryColor,
      fontSize: _fontSize,
    );

    final dimThemeData = AppTheme.dimTheme(
      primaryColor: _currentTheme.primaryColor,
      fontSize: _fontSize,
    );

    final darkThemeData = AppTheme.darkTheme(
      primaryColor: _currentTheme.primaryColor,
      fontSize: _fontSize,
    );

    // تحديث السمات
    _lightThemeData = lightThemeData;
    _dimThemeData = dimThemeData;
    _darkThemeData = darkThemeData;

    // طباعة معلومات السمة للتأكد من إعادة بنائها
    debugPrint(
      'Theme rebuilt - Theme mode: ${_themeMode.toString().split('.').last}, Font size: $_fontSize',
    );
  }
}
