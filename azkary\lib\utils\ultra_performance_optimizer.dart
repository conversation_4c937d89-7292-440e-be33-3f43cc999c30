import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';

/// محسن الأداء الفائق - تحسينات متقدمة للسرعة والخفة
class UltraPerformanceOptimizer {
  static final UltraPerformanceOptimizer _instance =
      UltraPerformanceOptimizer._internal();
  factory UltraPerformanceOptimizer() => _instance;
  UltraPerformanceOptimizer._internal();

  bool _isInitialized = false;
  Timer? _memoryCleanupTimer;
  Timer? _performanceMonitorTimer;

  /// تهيئة التحسينات الفائقة
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // 1. تحسينات Flutter Engine
      await _optimizeFlutterEngine();

      // 2. تحسينات الذاكرة الفائقة
      _setupUltraMemoryOptimization();

      // 3. تحسينات الرسم والعرض
      _optimizeRenderingPipeline();

      // 4. تحسينات التخزين المؤقت
      _setupIntelligentCaching();

      // 5. مراقبة الأداء المستمرة
      _startPerformanceMonitoring();

      _isInitialized = true;
      debugPrint('🚀 تم تفعيل محسن الأداء الفائق');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة محسن الأداء: $e');
    }
  }

  /// تحسينات Flutter Engine
  Future<void> _optimizeFlutterEngine() async {
    // تحسين معدل الإطارات
    SchedulerBinding.instance.addPersistentFrameCallback((_) {
      // تنظيف دوري للذاكرة كل 60 إطار (~1 ثانية)
      if (SchedulerBinding.instance.transientCallbackCount == 0) {
        _performLightweightCleanup();
      }
    });

    // تحسين إعدادات النظام
    if (!kIsWeb) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    }
  }

  /// تحسينات الذاكرة الفائقة
  void _setupUltraMemoryOptimization() {
    // تقليل جذري في التخزين المؤقت للصور
    PaintingBinding.instance.imageCache.maximumSize = 25;
    PaintingBinding.instance.imageCache.maximumSizeBytes =
        12 * 1024 * 1024; // 12MB

    // تنظيف دوري كل 45 ثانية
    _memoryCleanupTimer = Timer.periodic(
      const Duration(seconds: 45),
      (_) => _performIntelligentCleanup(),
    );
  }

  /// تحسين pipeline الرسم
  void _optimizeRenderingPipeline() {
    // تحسين إعدادات الرسم
    debugRepaintRainbowEnabled = false;
    debugPaintSizeEnabled = false;

    // تحسين الشفافية
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // تقليل العمليات المكلفة
      RenderObject.debugCheckingIntrinsics = false;
    });
  }

  /// إعداد التخزين المؤقت الذكي
  void _setupIntelligentCaching() {
    // تنظيف التخزين المؤقت عند عدم الاستخدام
    WidgetsBinding.instance.addObserver(_AppLifecycleObserver());
  }

  /// مراقبة الأداء المستمرة
  void _startPerformanceMonitoring() {
    _performanceMonitorTimer = Timer.periodic(
      const Duration(minutes: 2),
      (_) => _checkAndOptimizePerformance(),
    );
  }

  /// تنظيف خفيف ومستمر
  void _performLightweightCleanup() {
    // تنظيف خفيف بدون تأثير على الأداء
    if (PaintingBinding.instance.imageCache.currentSize > 20) {
      PaintingBinding.instance.imageCache.clearLiveImages();
    }
  }

  /// تنظيف ذكي ومتقدم
  Future<void> _performIntelligentCleanup() async {
    try {
      // تنظيف التخزين المؤقت للصور
      PaintingBinding.instance.imageCache.clear();

      // إجبار جمع القمامة بطريقة ذكية
      await _smartGarbageCollection();

      debugPrint('🧹 تم تنظيف الذاكرة بذكاء');
    } catch (e) {
      debugPrint('❌ خطأ في التنظيف الذكي: $e');
    }
  }

  /// جمع القمامة الذكي
  Future<void> _smartGarbageCollection() async {
    // جمع قمامة خفيف
    await Future.delayed(const Duration(milliseconds: 16)); // مدة إطار واحد
  }

  /// فحص وتحسين الأداء
  void _checkAndOptimizePerformance() {
    final imageCache = PaintingBinding.instance.imageCache;

    // إذا كان التخزين المؤقت ممتلئ، قم بتنظيفه
    if (imageCache.currentSize > imageCache.maximumSize * 0.8) {
      imageCache.clear();
      debugPrint('🔄 تم تنظيف التخزين المؤقت للصور');
    }

    // تحسين إعدادات الأداء حسب الحاجة
    _adjustPerformanceSettings();
  }

  /// تعديل إعدادات الأداء ديناميكياً
  void _adjustPerformanceSettings() {
    // تقليل جودة الصور إذا كان الأداء بطيء
    // يمكن إضافة منطق أكثر تعقيداً هنا
  }

  /// بناء ListView محسن فائق السرعة
  static Widget buildUltraFastListView({
    required int itemCount,
    required IndexedWidgetBuilder itemBuilder,
    ScrollController? controller,
    EdgeInsetsGeometry? padding,
    bool shrinkWrap = false,
  }) {
    return ListView.builder(
      controller: controller,
      padding: padding,
      shrinkWrap: shrinkWrap,
      itemCount: itemCount,
      itemBuilder: (context, index) {
        // تغليف كل عنصر في RepaintBoundary لتحسين الأداء
        return RepaintBoundary(child: itemBuilder(context, index));
      },
      // إعدادات محسنة للأداء
      cacheExtent: 200, // تقليل المساحة المخزنة مؤقتاً
      physics: const ClampingScrollPhysics(), // فيزياء أسرع
      addAutomaticKeepAlives: false, // عدم الاحتفاظ بالعناصر
      addRepaintBoundaries: false, // نحن نضيفها يدوياً
      addSemanticIndexes: false, // تقليل العمليات الإضافية
    );
  }

  /// بناء صورة محسنة فائقة السرعة
  static Widget buildUltraFastImage({
    required String imagePath,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    bool isAsset = true,
  }) {
    return RepaintBoundary(
      child:
          isAsset
              ? Image.asset(
                imagePath,
                width: width,
                height: height,
                fit: fit,
                cacheWidth: width?.round(),
                cacheHeight: height?.round(),
                filterQuality: FilterQuality.low, // جودة منخفضة للسرعة
                gaplessPlayback: true,
                excludeFromSemantics: true,
              )
              : Image.network(
                imagePath,
                width: width,
                height: height,
                fit: fit,
                cacheWidth: width?.round(),
                cacheHeight: height?.round(),
                filterQuality: FilterQuality.low,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return SizedBox(
                    width: width,
                    height: height,
                    child: const Center(
                      child: SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    ),
                  );
                },
              ),
    );
  }

  /// بناء انتقال فائق السرعة
  static PageRouteBuilder buildUltraFastTransition({
    required Widget page,
    Duration duration = const Duration(milliseconds: 150),
  }) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // انتقال بسيط وسريع
        return FadeTransition(opacity: animation, child: child);
      },
    );
  }

  /// بناء بطاقة محسنة فائقة السرعة
  static Widget buildUltraFastCard({
    required Widget child,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    Color? color,
    double? elevation,
    BorderRadius? borderRadius,
  }) {
    return RepaintBoundary(
      child: Container(
        margin:
            margin ?? const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        padding: padding ?? const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color ?? Colors.white,
          borderRadius: borderRadius ?? BorderRadius.circular(8),
          boxShadow:
              elevation != null && elevation > 0
                  ? [
                    BoxShadow(
                      color: Colors.black.withAlpha(25),
                      blurRadius: elevation,
                      offset: Offset(0, elevation / 2),
                    ),
                  ]
                  : null,
        ),
        child: child,
      ),
    );
  }

  /// تنظيف الموارد
  void dispose() {
    _memoryCleanupTimer?.cancel();
    _performanceMonitorTimer?.cancel();
    _isInitialized = false;
    debugPrint('🔄 تم تنظيف محسن الأداء الفائق');
  }

  // Getters
  bool get isInitialized => _isInitialized;
}

/// مراقب دورة حياة التطبيق للتحسين
class _AppLifecycleObserver extends WidgetsBindingObserver {
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.detached:
        // تنظيف عند إيقاف التطبيق
        PaintingBinding.instance.imageCache.clear();
        break;
      case AppLifecycleState.resumed:
        // تحسين عند العودة للتطبيق
        UltraPerformanceOptimizer()._performLightweightCleanup();
        break;
      default:
        break;
    }
  }
}
