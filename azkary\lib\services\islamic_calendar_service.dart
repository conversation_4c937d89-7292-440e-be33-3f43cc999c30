// import 'package:hijri/hijri.dart'; // مؤقتاً معطل
// import 'package:adhan/adhan.dart'; // مؤقتاً معطل
// import 'package:geolocator/geolocator.dart'; // مؤقتاً معطل
import '../models/islamic_calendar_model.dart';
import '../utils/logger.dart';

/// خدمة التقويم الإسلامي (مبسطة)
class IslamicCalendarService {
  /// الحصول على بيانات التقويم لتاريخ معين
  static Future<IslamicCalendarModel> getCalendarData(DateTime date) async {
    try {
      // إنشاء تاريخ هجري مبسط
      final hijriDate = SimpleHijriDate(
        hYear: 1445, // سنة افتراضية
        hMonth: date.month,
        hDay: date.day,
      );

      // الحصول على الأحداث الإسلامية
      final events = IslamicEventsFactory.getEventsForDate(hijriDate);

      // حساب مرحلة القمر
      final moonPhase = MoonPhaseCalculator.calculateMoonPhase(date);

      // أوقات صلاة افتراضية (مؤقتة)
      final prayerTimes = _getDefaultPrayerTimes(date);

      return IslamicCalendarModel(
        hijriDate: hijriDate,
        gregorianDate: date,
        events: events,
        moonPhase: moonPhase,
        prayerTimes: prayerTimes,
      );
    } catch (e) {
      AppLogger.error('خطأ في الحصول على بيانات التقويم: $e');
      rethrow;
    }
  }

  /// الحصول على بيانات الشهر الكامل
  static Future<List<IslamicCalendarModel>> getMonthData(
    int year,
    int month,
  ) async {
    try {
      final monthData = <IslamicCalendarModel>[];
      final lastDay = DateTime(year, month + 1, 0);

      for (int day = 1; day <= lastDay.day; day++) {
        final date = DateTime(year, month, day);
        final calendarData = await getCalendarData(date);
        monthData.add(calendarData);
      }

      return monthData;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على بيانات الشهر: $e');
      rethrow;
    }
  }

  /// الحصول على أوقات صلاة افتراضية (مؤقتة)
  static PrayerTimes _getDefaultPrayerTimes(DateTime date) {
    // أوقات صلاة افتراضية (يمكن تحسينها لاحقاً)
    return PrayerTimes(
      fajr: DateTime(date.year, date.month, date.day, 5, 30),
      sunrise: DateTime(date.year, date.month, date.day, 6, 45),
      dhuhr: DateTime(date.year, date.month, date.day, 12, 15),
      asr: DateTime(date.year, date.month, date.day, 15, 30),
      maghrib: DateTime(date.year, date.month, date.day, 18, 0),
      isha: DateTime(date.year, date.month, date.day, 19, 30),
    );
  }

  /// الحصول على الأحداث القادمة (مبسط)
  static List<IslamicEvent> getUpcomingEvents({int daysAhead = 30}) {
    final upcomingEvents = <IslamicEvent>[];
    final allEvents = IslamicEventsFactory.getFixedEvents();
    final now = DateTime.now();

    for (int i = 0; i < daysAhead; i++) {
      final checkDate = now.add(Duration(days: i));
      final simpleHijriDate = SimpleHijriDate(
        hYear: 1445,
        hMonth: checkDate.month,
        hDay: checkDate.day,
      );

      final eventsForDay =
          allEvents.where((event) => event.isOnDate(simpleHijriDate)).toList();

      upcomingEvents.addAll(eventsForDay);
    }

    return upcomingEvents;
  }

  /// تحويل الأرقام إلى العربية
  static String convertToArabicNumbers(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      final digitInt = int.tryParse(digit);
      return digitInt != null ? arabicNumbers[digitInt] : digit;
    }).join();
  }

  /// الحصول على اسم الشهر الميلادي بالعربية
  static String getGregorianMonthName(int month) {
    const monthNames = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];
    return monthNames[month - 1];
  }

  /// الحصول على اسم اليوم بالعربية
  static String getDayName(int weekday) {
    const dayNames = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];
    return dayNames[weekday - 1];
  }

  /// التحقق من كون التاريخ اليوم
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  /// التحقق من كون التاريخ في المستقبل
  static bool isFuture(DateTime date) {
    return date.isAfter(DateTime.now());
  }

  /// التحقق من كون التاريخ في الماضي
  static bool isPast(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final checkDate = DateTime(date.year, date.month, date.day);
    return checkDate.isBefore(today);
  }

  /// الحصول على لون الحدث حسب نوعه
  static int getEventColor(IslamicEventType type) {
    switch (type) {
      case IslamicEventType.eid:
        return 0xFF4CAF50; // أخضر للأعياد
      case IslamicEventType.religious:
        return 0xFF2196F3; // أزرق للمناسبات الدينية
      case IslamicEventType.historical:
        return 0xFF9C27B0; // بنفسجي للأحداث التاريخية
      case IslamicEventType.special:
        return 0xFFFF9800; // برتقالي للمناسبات الخاصة
    }
  }

  /// تنسيق التاريخ الهجري
  static String formatHijriDate(dynamic hijri) {
    return '${convertToArabicNumbers(hijri.hDay)} ${_getHijriMonthName(hijri.hMonth)} ${convertToArabicNumbers(hijri.hYear)} هـ';
  }

  /// تنسيق التاريخ الميلادي
  static String formatGregorianDate(DateTime date) {
    return '${convertToArabicNumbers(date.day)} ${getGregorianMonthName(date.month)} ${convertToArabicNumbers(date.year)} م';
  }

  /// الحصول على اسم الشهر الهجري
  static String _getHijriMonthName(int month) {
    const monthNames = [
      'محرم',
      'صفر',
      'ربيع الأول',
      'ربيع الثاني',
      'جمادى الأولى',
      'جمادى الثانية',
      'رجب',
      'شعبان',
      'رمضان',
      'شوال',
      'ذو القعدة',
      'ذو الحجة',
    ];
    return monthNames[month - 1];
  }
}
