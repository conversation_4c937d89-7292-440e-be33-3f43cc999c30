import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/mushaf_page.dart';
import '../models/quran_model.dart';
import '../services/surah_mushaf_service.dart';
import '../utils/simple_mushaf_cache.dart';
import '../widgets/mushaf_page_viewer.dart';
import '../utils/logger.dart';
import '../utils/mushaf_debug_helper.dart';

/// ويدجت عرض المصحف الشريف للسورة المحددة
class SurahMushafViewer extends StatefulWidget {
  final Surah surah;
  final int? initialAyahNumber;
  final Function(int)? onAyahTap;

  const SurahMushafViewer({
    super.key,
    required this.surah,
    this.initialAyahNumber,
    this.onAyahTap,
  });

  @override
  State<SurahMushafViewer> createState() => _SurahMushafViewerState();
}

class _SurahMushafViewerState extends State<SurahMushafViewer> {
  final PageController _pageController = PageController();
  final SurahMushafService _surahMushafService = SurahMushafService();
  final SimpleMushafCache _imageCache = SimpleMushafCache();

  List<MushafPage> _surahPages = [];
  int _currentPageIndex = 0;
  bool _isLoading = true;
  String? _error;
  MushafViewSettings _settings = const MushafViewSettings();

  @override
  void initState() {
    super.initState();
    _initializeSurahMushaf();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// تهيئة المصحف للسورة
  Future<void> _initializeSurahMushaf() async {
    try {
      AppLogger.info('=== بدء تهيئة المصحف للسورة ${widget.surah.name} ===');

      setState(() {
        _isLoading = true;
        _error = null;
      });

      // تهيئة الخدمات
      AppLogger.info('تهيئة خدمة المصحف للسورة...');
      await _surahMushafService.initialize();

      AppLogger.info('تهيئة مدير التخزين المؤقت للصور...');
      await _imageCache.initialize();

      // تحميل صفحات السورة
      final surahPages = await _surahMushafService.getSurahPages(widget.surah);
      _settings = _surahMushafService.settings;

      if (surahPages.isEmpty) {
        throw Exception(
          'لا توجد صفحات متاحة للسورة ${widget.surah.name}. تأكد من صحة رقم السورة.',
        );
      }

      // تحديد الصفحة الأولى
      int initialPageIndex = 0;

      if (widget.initialAyahNumber != null) {
        // البحث عن الصفحة التي تحتوي على الآية المحددة
        final targetPage = await _surahMushafService.findPageByAyah(
          widget.surah,
          widget.initialAyahNumber!,
        );
        if (targetPage != null) {
          initialPageIndex = surahPages.indexWhere(
            (page) => page.pageNumber == targetPage.pageNumber,
          );
          if (initialPageIndex == -1) initialPageIndex = 0;
        }
      } else {
        // تحميل آخر صفحة تم عرضها
        final lastViewedPage = await _surahMushafService.getLastViewedPage(
          widget.surah.number,
        );
        initialPageIndex = surahPages.indexWhere(
          (page) => page.pageNumber == lastViewedPage,
        );
        if (initialPageIndex == -1) initialPageIndex = 0;
      }

      setState(() {
        _surahPages = surahPages;
        _currentPageIndex = initialPageIndex;
        _isLoading = false;
      });

      // الانتقال إلى الصفحة الأولى
      if (_pageController.hasClients) {
        _pageController.jumpToPage(initialPageIndex);
      } else {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && _pageController.hasClients) {
            _pageController.jumpToPage(initialPageIndex);
          }
        });
      }

      // تحميل مسبق للصفحات المجاورة
      _preloadAdjacentPages();

      AppLogger.info(
        'تم تحميل ${surahPages.length} صفحة للسورة ${widget.surah.name}',
      );
      AppLogger.info(
        '=== انتهاء تهيئة المصحف للسورة ${widget.surah.name} بنجاح ===',
      );
    } catch (e) {
      final errorMessage =
          'خطأ في تهيئة المصحف للسورة ${widget.surah.name}: $e';
      AppLogger.error(errorMessage);

      // تسجيل معلومات إضافية للتشخيص
      AppLogger.error('رقم السورة: ${widget.surah.number}');
      AppLogger.error('عدد آيات السورة: ${widget.surah.numberOfAyahs}');

      setState(() {
        _error =
            'فشل في تحميل صفحات السورة ${widget.surah.name}.\n\nالسبب: ${e.toString()}\n\nتأكد من الاتصال بالإنترنت وحاول مرة أخرى.';
        _isLoading = false;
      });

      // تشغيل التشخيص في حالة الخطأ
      _runDiagnostic();
    }
  }

  /// تشخيص مشاكل المصحف للسورة
  Future<void> _runDiagnostic() async {
    AppLogger.info('تشغيل تشخيص مشاكل المصحف للسورة ${widget.surah.name}...');

    try {
      // اختبار سريع للسورة
      final result = await MushafDebugHelper.quickTestSurah(widget.surah);

      if (result) {
        AppLogger.info('✅ التشخيص نجح - السورة تعمل بشكل صحيح');
      } else {
        AppLogger.error('❌ التشخيص فشل - توجد مشاكل في السورة');
      }
    } catch (e) {
      AppLogger.error('خطأ في تشغيل التشخيص: $e');
    }
  }

  /// تحميل مسبق للصفحات المجاورة
  Future<void> _preloadAdjacentPages() async {
    if (_surahPages.isEmpty) return;
    await _imageCache.preloadAdjacentPages(_currentPageIndex + 1, _surahPages);
  }

  /// تغيير الصفحة
  void _onPageChanged(int index) {
    if (index < 0 || index >= _surahPages.length) return;

    setState(() => _currentPageIndex = index);

    final page = _surahPages[index];

    // حفظ الموضع الحالي
    _surahMushafService.saveLastViewedPage(
      widget.surah.number,
      page.pageNumber,
    );

    // تحميل مسبق للصفحات المجاورة الجديدة
    Future.delayed(const Duration(milliseconds: 300), _preloadAdjacentPages);

    // تأثير اهتزاز خفيف
    HapticFeedback.selectionClick();

    AppLogger.info(
      'تم الانتقال إلى صفحة ${page.pageNumber} في السورة ${widget.surah.name}',
    );
  }

  /// الانتقال إلى الصفحة السابقة
  void _goToPreviousPage() {
    if (_currentPageIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// الانتقال إلى الصفحة التالية
  void _goToNextPage() {
    if (_currentPageIndex < _surahPages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode =
        _settings.nightMode || theme.brightness == Brightness.dark;

    if (_isLoading) {
      return _buildLoadingState(isDarkMode);
    }

    if (_error != null) {
      return _buildErrorState(isDarkMode);
    }

    if (_surahPages.isEmpty) {
      return _buildEmptyState(isDarkMode);
    }

    return RepaintBoundary(
      child: Column(
        children: [
          // معلومات السورة والصفحة
          _buildSurahInfo(theme, isDarkMode),

          // عارض الصفحات
          Expanded(
            child: RepaintBoundary(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: _onPageChanged,
                itemCount: _surahPages.length,
                itemBuilder: (context, index) {
                  final page = _surahPages[index];
                  return RepaintBoundary(
                    child: MushafPageViewer(
                      page: page,
                      settings: _settings,
                      enableZoom: _settings.enableZoom,
                      onTap: () {
                        // يمكن إضافة منطق للتفاعل مع النقر على الصفحة
                        if (widget.onAyahTap != null) {
                          // تقدير رقم الآية بناءً على الصفحة
                          // هذا تقدير بسيط، يمكن تحسينه لاحقاً
                          final estimatedAyah = (index + 1) * 10;
                          widget.onAyahTap!(estimatedAyah);
                        }
                      },
                    ),
                  );
                },
              ),
            ),
          ),

          // شريط التنقل
          _buildNavigationBar(theme, isDarkMode),
        ],
      ),
    );
  }

  /// بناء معلومات السورة
  Widget _buildSurahInfo(ThemeData theme, bool isDarkMode) {
    if (!_settings.showPageInfo || _surahPages.isEmpty) {
      return const SizedBox.shrink();
    }

    final currentPage = _surahPages[_currentPageIndex];

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: (isDarkMode ? Colors.black : Colors.white).withValues(
          alpha: 0.9,
        ),
        border: Border(
          bottom: BorderSide(
            color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.menu_book,
            size: 16,
            color: isDarkMode ? Colors.amber[400] : Colors.brown[600],
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '${widget.surah.name} - صفحة ${currentPage.pageNumberArabic}',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black87,
                fontFamily: 'Amiri',
              ),
            ),
          ),
          Text(
            '${_currentPageIndex + 1} من ${_surahPages.length}',
            style: TextStyle(
              fontSize: 12,
              color: isDarkMode ? Colors.grey[300] : Colors.grey[600],
              fontFamily: 'Amiri',
            ),
          ),
        ],
      ),
    );
  }

  /// بناء شريط التنقل
  Widget _buildNavigationBar(ThemeData theme, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: (isDarkMode ? Colors.black : Colors.white).withValues(
          alpha: 0.95,
        ),
        border: Border(
          top: BorderSide(
            color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            // زر الصفحة السابقة
            _buildNavigationButton(
              context,
              icon: Icons.chevron_right,
              label: 'السابقة',
              onPressed: _currentPageIndex > 0 ? _goToPreviousPage : null,
              isDarkMode: isDarkMode,
            ),

            // مؤشر التقدم
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  LinearProgressIndicator(
                    value: (_currentPageIndex + 1) / _surahPages.length,
                    backgroundColor:
                        isDarkMode ? Colors.grey[700] : Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      isDarkMode ? Colors.amber[400]! : Colors.brown[600]!,
                    ),
                    minHeight: 4,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'صفحة ${_currentPageIndex + 1} من ${_surahPages.length}',
                    style: TextStyle(
                      fontSize: 12,
                      color: isDarkMode ? Colors.grey[300] : Colors.grey[600],
                      fontFamily: 'Amiri',
                    ),
                  ),
                ],
              ),
            ),

            // زر الصفحة التالية
            _buildNavigationButton(
              context,
              icon: Icons.chevron_left,
              label: 'التالية',
              onPressed:
                  _currentPageIndex < _surahPages.length - 1
                      ? _goToNextPage
                      : null,
              isDarkMode: isDarkMode,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر التنقل
  Widget _buildNavigationButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
    required bool isDarkMode,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap:
            onPressed != null
                ? () {
                  HapticFeedback.lightImpact();
                  onPressed();
                }
                : null,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color:
                  onPressed != null
                      ? (isDarkMode ? Colors.grey[600]! : Colors.grey[400]!)
                      : (isDarkMode ? Colors.grey[800]! : Colors.grey[200]!),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 16,
                color:
                    onPressed != null
                        ? (isDarkMode ? Colors.amber[400] : Colors.brown[600])
                        : (isDarkMode ? Colors.grey[600] : Colors.grey[400]),
              ),
              const SizedBox(width: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color:
                      onPressed != null
                          ? (isDarkMode ? Colors.white : Colors.black87)
                          : (isDarkMode ? Colors.grey[600] : Colors.grey[400]),
                  fontFamily: 'Amiri',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState(bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              isDarkMode ? Colors.amber[400]! : Colors.brown[600]!,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل صفحات ${widget.surah.name}...',
            style: TextStyle(
              fontSize: 16,
              color: isDarkMode ? Colors.white70 : Colors.black87,
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState(bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: isDarkMode ? Colors.red[300] : Colors.red[600],
          ),
          const SizedBox(height: 16),
          Text(
            'خطأ في تحميل المصحف',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.black87,
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          if (_error != null)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _error!,
                style: TextStyle(
                  fontSize: 14,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  fontFamily: 'Amiri',
                ),
                textAlign: TextAlign.center,
              ),
            ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton.icon(
                onPressed: _initializeSurahMushaf,
                icon: const Icon(Icons.refresh),
                label: const Text(
                  'إعادة المحاولة',
                  style: TextStyle(fontFamily: 'Amiri'),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      isDarkMode ? Colors.amber[400] : Colors.brown[600],
                  foregroundColor: isDarkMode ? Colors.black : Colors.white,
                ),
              ),
              const SizedBox(width: 12),
              OutlinedButton.icon(
                onPressed: () async {
                  AppLogger.info('تشغيل التشخيص الشامل...');
                  await MushafDebugHelper.runFullDiagnostic();
                },
                icon: const Icon(Icons.bug_report),
                label: const Text(
                  'تشخيص',
                  style: TextStyle(fontFamily: 'Amiri'),
                ),
                style: OutlinedButton.styleFrom(
                  foregroundColor:
                      isDarkMode ? Colors.amber[400] : Colors.brown[600],
                  side: BorderSide(
                    color: isDarkMode ? Colors.amber[400]! : Colors.brown[600]!,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState(bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.menu_book_outlined,
            size: 64,
            color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد صفحات متاحة للسورة ${widget.surah.name}',
            style: TextStyle(
              fontSize: 16,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
