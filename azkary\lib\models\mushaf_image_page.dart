/// نموذج بيانات لصفحة المصحف التقليدي بالصور
class MushafImagePage {
  final int pageNumber;
  final String imageUrl;
  final List<int> surahNumbers; // أرقام السور في الصفحة
  final List<String> surahNames; // أسماء السور في الصفحة
  final int? juzNumber; // رقم الجزء
  final int? hizbNumber; // رقم الحزب
  final List<int> ayahNumbers; // أرقام الآيات في الصفحة
  final bool isDownloaded; // هل تم تحميل الصورة محلياً
  final String? localPath; // المسار المحلي للصورة

  const MushafImagePage({
    required this.pageNumber,
    required this.imageUrl,
    this.surahNumbers = const [],
    this.surahNames = const [],
    this.juzNumber,
    this.hizbNumber,
    this.ayahNumbers = const [],
    this.isDownloaded = false,
    this.localPath,
  });

  /// إنشاء نموذج من بيانات JSON
  factory MushafImagePage.fromJson(Map<String, dynamic> json) {
    return MushafImagePage(
      pageNumber: json['pageNumber'] ?? 0,
      imageUrl: json['imageUrl'] ?? '',
      surahNumbers: List<int>.from(json['surahNumbers'] ?? []),
      surahNames: List<String>.from(json['surahNames'] ?? []),
      juzNumber: json['juzNumber'],
      hizbNumber: json['hizbNumber'],
      ayahNumbers: List<int>.from(json['ayahNumbers'] ?? []),
      isDownloaded: json['isDownloaded'] ?? false,
      localPath: json['localPath'],
    );
  }

  /// تحويل النموذج إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'pageNumber': pageNumber,
      'imageUrl': imageUrl,
      'surahNumbers': surahNumbers,
      'surahNames': surahNames,
      'juzNumber': juzNumber,
      'hizbNumber': hizbNumber,
      'ayahNumbers': ayahNumbers,
      'isDownloaded': isDownloaded,
      'localPath': localPath,
    };
  }

  /// إنشاء نسخة محدثة من النموذج
  MushafImagePage copyWith({
    int? pageNumber,
    String? imageUrl,
    List<int>? surahNumbers,
    List<String>? surahNames,
    int? juzNumber,
    int? hizbNumber,
    List<int>? ayahNumbers,
    bool? isDownloaded,
    String? localPath,
  }) {
    return MushafImagePage(
      pageNumber: pageNumber ?? this.pageNumber,
      imageUrl: imageUrl ?? this.imageUrl,
      surahNumbers: surahNumbers ?? this.surahNumbers,
      surahNames: surahNames ?? this.surahNames,
      juzNumber: juzNumber ?? this.juzNumber,
      hizbNumber: hizbNumber ?? this.hizbNumber,
      ayahNumbers: ayahNumbers ?? this.ayahNumbers,
      isDownloaded: isDownloaded ?? this.isDownloaded,
      localPath: localPath ?? this.localPath,
    );
  }

  @override
  String toString() {
    return 'MushafImagePage(pageNumber: $pageNumber, imageUrl: $imageUrl, surahNumbers: $surahNumbers, isDownloaded: $isDownloaded)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MushafImagePage && other.pageNumber == pageNumber;
  }

  @override
  int get hashCode => pageNumber.hashCode;
}

/// نموذج لمعلومات السورة في المصحف التقليدي
class SurahInMushaf {
  final int surahNumber;
  final String surahName;
  final int startPage;
  final int endPage;
  final int numberOfAyahs;
  final String revelationType;

  const SurahInMushaf({
    required this.surahNumber,
    required this.surahName,
    required this.startPage,
    required this.endPage,
    required this.numberOfAyahs,
    required this.revelationType,
  });

  /// إنشاء نموذج من بيانات JSON
  factory SurahInMushaf.fromJson(Map<String, dynamic> json) {
    return SurahInMushaf(
      surahNumber: json['surahNumber'] ?? 0,
      surahName: json['surahName'] ?? '',
      startPage: json['startPage'] ?? 0,
      endPage: json['endPage'] ?? 0,
      numberOfAyahs: json['numberOfAyahs'] ?? 0,
      revelationType: json['revelationType'] ?? '',
    );
  }

  /// تحويل النموذج إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'surahNumber': surahNumber,
      'surahName': surahName,
      'startPage': startPage,
      'endPage': endPage,
      'numberOfAyahs': numberOfAyahs,
      'revelationType': revelationType,
    };
  }

  @override
  String toString() {
    return 'SurahInMushaf(surahNumber: $surahNumber, surahName: $surahName, startPage: $startPage, endPage: $endPage)';
  }
}

/// نموذج لحالة تحميل صفحة المصحف
enum MushafPageLoadingState {
  /// لم يتم التحميل بعد
  notLoaded,
  
  /// جاري التحميل
  loading,
  
  /// تم التحميل بنجاح
  loaded,
  
  /// فشل في التحميل
  error,
  
  /// متاح محلياً
  cached,
}

/// نموذج لحالة صفحة المصحف مع معلومات التحميل
class MushafPageState {
  final MushafImagePage page;
  final MushafPageLoadingState state;
  final String? errorMessage;
  final double? downloadProgress;

  const MushafPageState({
    required this.page,
    required this.state,
    this.errorMessage,
    this.downloadProgress,
  });

  /// إنشاء حالة تحميل
  factory MushafPageState.loading(MushafImagePage page, {double? progress}) {
    return MushafPageState(
      page: page,
      state: MushafPageLoadingState.loading,
      downloadProgress: progress,
    );
  }

  /// إنشاء حالة نجاح
  factory MushafPageState.loaded(MushafImagePage page) {
    return MushafPageState(
      page: page,
      state: MushafPageLoadingState.loaded,
    );
  }

  /// إنشاء حالة خطأ
  factory MushafPageState.error(MushafImagePage page, String error) {
    return MushafPageState(
      page: page,
      state: MushafPageLoadingState.error,
      errorMessage: error,
    );
  }

  /// إنشاء حالة مخزنة محلياً
  factory MushafPageState.cached(MushafImagePage page) {
    return MushafPageState(
      page: page,
      state: MushafPageLoadingState.cached,
    );
  }

  @override
  String toString() {
    return 'MushafPageState(page: ${page.pageNumber}, state: $state, error: $errorMessage)';
  }
}
