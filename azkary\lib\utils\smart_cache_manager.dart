import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';

/// مدير التخزين المؤقت الذكي - تحسين متقدم للذاكرة
class SmartCacheManager {
  static final SmartCacheManager _instance = SmartCacheManager._internal();
  factory SmartCacheManager() => _instance;
  SmartCacheManager._internal();

  // تخزين مؤقت ذكي للبيانات
  final Map<String, _CacheEntry> _cache = {};
  final Queue<String> _accessOrder = Queue<String>();
  
  // إعدادات التخزين المؤقت
  static const int _maxCacheSize = 50; // حد أقصى 50 عنصر
  static const Duration _defaultTTL = Duration(minutes: 10); // مدة انتهاء افتراضية
  static const Duration _cleanupInterval = Duration(minutes: 5); // تنظيف كل 5 دقائق

  Timer? _cleanupTimer;
  bool _isInitialized = false;

  /// تهيئة مدير التخزين المؤقت
  void initialize() {
    if (_isInitialized) return;

    // بدء تنظيف دوري
    _cleanupTimer = Timer.periodic(_cleanupInterval, (_) => _performCleanup());
    
    _isInitialized = true;
    debugPrint('🧠 تم تهيئة مدير التخزين المؤقت الذكي');
  }

  /// حفظ بيانات في التخزين المؤقت
  void put<T>(String key, T data, {Duration? ttl}) {
    // إزالة العنصر القديم إذا كان موجوداً
    if (_cache.containsKey(key)) {
      _accessOrder.remove(key);
    }

    // إضافة العنصر الجديد
    _cache[key] = _CacheEntry<T>(
      data: data,
      timestamp: DateTime.now(),
      ttl: ttl ?? _defaultTTL,
    );
    
    _accessOrder.addLast(key);

    // تنظيف إذا تجاوز الحد الأقصى
    if (_cache.length > _maxCacheSize) {
      _evictOldest();
    }
  }

  /// استرجاع بيانات من التخزين المؤقت
  T? get<T>(String key) {
    final entry = _cache[key];
    if (entry == null) return null;

    // فحص انتهاء الصلاحية
    if (_isExpired(entry)) {
      remove(key);
      return null;
    }

    // تحديث ترتيب الوصول (LRU)
    _accessOrder.remove(key);
    _accessOrder.addLast(key);

    return entry.data as T?;
  }

  /// إزالة عنصر من التخزين المؤقت
  void remove(String key) {
    _cache.remove(key);
    _accessOrder.remove(key);
  }

  /// مسح جميع البيانات
  void clear() {
    _cache.clear();
    _accessOrder.clear();
    debugPrint('🧹 تم مسح التخزين المؤقت الذكي');
  }

  /// فحص وجود مفتاح
  bool containsKey(String key) {
    final entry = _cache[key];
    if (entry == null) return false;
    
    if (_isExpired(entry)) {
      remove(key);
      return false;
    }
    
    return true;
  }

  /// الحصول على حجم التخزين المؤقت
  int get size => _cache.length;

  /// فحص انتهاء صلاحية العنصر
  bool _isExpired(_CacheEntry entry) {
    return DateTime.now().difference(entry.timestamp) > entry.ttl;
  }

  /// إزالة أقدم عنصر (LRU)
  void _evictOldest() {
    if (_accessOrder.isNotEmpty) {
      final oldestKey = _accessOrder.removeFirst();
      _cache.remove(oldestKey);
    }
  }

  /// تنظيف دوري للعناصر المنتهية الصلاحية
  void _performCleanup() {
    final expiredKeys = <String>[];
    
    _cache.forEach((key, entry) {
      if (_isExpired(entry)) {
        expiredKeys.add(key);
      }
    });

    for (final key in expiredKeys) {
      remove(key);
    }

    if (expiredKeys.isNotEmpty) {
      debugPrint('🧹 تم تنظيف ${expiredKeys.length} عنصر منتهي الصلاحية');
    }
  }

  /// طباعة إحصائيات التخزين المؤقت
  void printStats() {
    debugPrint('📊 إحصائيات التخزين المؤقت الذكي:');
    debugPrint('  - العناصر المحفوظة: ${_cache.length}');
    debugPrint('  - الحد الأقصى: $_maxCacheSize');
    debugPrint('  - معدل الاستخدام: ${(_cache.length / _maxCacheSize * 100).toStringAsFixed(1)}%');
  }

  /// تنظيف الموارد
  void dispose() {
    _cleanupTimer?.cancel();
    clear();
    _isInitialized = false;
    debugPrint('🔄 تم تنظيف مدير التخزين المؤقت الذكي');
  }

  // Getters
  bool get isInitialized => _isInitialized;
}

/// عنصر في التخزين المؤقت
class _CacheEntry<T> {
  final T data;
  final DateTime timestamp;
  final Duration ttl;

  _CacheEntry({
    required this.data,
    required this.timestamp,
    required this.ttl,
  });
}

/// مدير التخزين المؤقت للصور الذكي
class SmartImageCacheManager {
  static final SmartImageCacheManager _instance = SmartImageCacheManager._internal();
  factory SmartImageCacheManager() => _instance;
  SmartImageCacheManager._internal();

  final Map<String, DateTime> _imageAccessTimes = {};
  Timer? _cleanupTimer;

  /// تهيئة مدير الصور
  void initialize() {
    // تنظيف دوري للصور غير المستخدمة
    _cleanupTimer = Timer.periodic(
      const Duration(minutes: 3),
      (_) => _cleanupUnusedImages(),
    );
    
    debugPrint('🖼️ تم تهيئة مدير التخزين المؤقت للصور');
  }

  /// تسجيل استخدام صورة
  void recordImageAccess(String imagePath) {
    _imageAccessTimes[imagePath] = DateTime.now();
  }

  /// تنظيف الصور غير المستخدمة
  void _cleanupUnusedImages() {
    final now = DateTime.now();
    final unusedImages = <String>[];

    _imageAccessTimes.forEach((path, lastAccess) {
      if (now.difference(lastAccess).inMinutes > 5) {
        unusedImages.add(path);
      }
    });

    for (final path in unusedImages) {
      _imageAccessTimes.remove(path);
    }

    if (unusedImages.isNotEmpty) {
      debugPrint('🧹 تم تنظيف ${unusedImages.length} صورة غير مستخدمة');
    }
  }

  /// تنظيف الموارد
  void dispose() {
    _cleanupTimer?.cancel();
    _imageAccessTimes.clear();
  }
}

/// مدير التخزين المؤقت للنصوص
class SmartTextCacheManager {
  static final SmartTextCacheManager _instance = SmartTextCacheManager._internal();
  factory SmartTextCacheManager() => _instance;
  SmartTextCacheManager._internal();

  final Map<String, String> _textCache = {};
  static const int _maxTextCacheSize = 100;

  /// حفظ نص في التخزين المؤقت
  void cacheText(String key, String text) {
    if (_textCache.length >= _maxTextCacheSize) {
      // إزالة أقدم نص
      final firstKey = _textCache.keys.first;
      _textCache.remove(firstKey);
    }
    
    _textCache[key] = text;
  }

  /// استرجاع نص من التخزين المؤقت
  String? getCachedText(String key) {
    return _textCache[key];
  }

  /// مسح التخزين المؤقت للنصوص
  void clearTextCache() {
    _textCache.clear();
    debugPrint('🧹 تم مسح التخزين المؤقت للنصوص');
  }

  /// الحصول على حجم التخزين المؤقت
  int get textCacheSize => _textCache.length;
}
