import 'package:flutter/material.dart';
import 'package:azkary/utils/logger.dart';

/// مُحسن الصور الذكي للأجهزة ذات 6GB RAM
class SmartImageOptimizer {
  static final SmartImageOptimizer _instance = SmartImageOptimizer._internal();
  factory SmartImageOptimizer() => _instance;
  SmartImageOptimizer._internal();

  // إعدادات التحسين التكيفية
  bool _isLowMemoryMode = false;
  bool _isCriticalMemoryMode = false;

  // إعدادات الجودة حسب حالة الذاكرة
  static const Map<String, double> _qualitySettings = {
    'normal': 0.8,
    'low': 0.6,
    'critical': 0.4,
  };

  // إعدادات الحجم حسب حالة الذاكرة
  static const Map<String, double> _scaleSettings = {
    'normal': 1.0,
    'low': 0.8,
    'critical': 0.6,
  };

  /// تحديث حالة الذاكرة
  void updateMemoryState({bool isLowMemory = false, bool isCritical = false}) {
    _isLowMemoryMode = isLowMemory;
    _isCriticalMemoryMode = isCritical;

    if (isCritical) {
      AppLogger.warning('تم تفعيل الوضع الحرج للصور');
      _optimizeImageCache(critical: true);
    } else if (isLowMemory) {
      AppLogger.info('تم تفعيل وضع الذاكرة المنخفضة للصور');
      _optimizeImageCache(critical: false);
    } else {
      AppLogger.info('تم إلغاء وضع تحسين الصور');
      _resetImageCache();
    }
  }

  /// تحسين إعدادات التخزين المؤقت للصور
  void _optimizeImageCache({required bool critical}) {
    final imageCache = PaintingBinding.instance.imageCache;

    if (critical) {
      imageCache.maximumSize = 20;
      imageCache.maximumSizeBytes = 10 * 1024 * 1024; // 10MB
    } else {
      imageCache.maximumSize = 50;
      imageCache.maximumSizeBytes = 25 * 1024 * 1024; // 25MB
    }

    AppLogger.info('تم تحسين إعدادات التخزين المؤقت للصور');
  }

  /// إعادة تعيين إعدادات التخزين المؤقت للصور
  void _resetImageCache() {
    final imageCache = PaintingBinding.instance.imageCache;
    imageCache.maximumSize = 100;
    imageCache.maximumSizeBytes = 50 * 1024 * 1024; // 50MB
  }

  /// الحصول على جودة الصورة المحسنة
  double getOptimizedQuality() {
    if (_isCriticalMemoryMode) return _qualitySettings['critical']!;
    if (_isLowMemoryMode) return _qualitySettings['low']!;
    return _qualitySettings['normal']!;
  }

  /// الحصول على مقياس الصورة المحسن
  double getOptimizedScale() {
    if (_isCriticalMemoryMode) return _scaleSettings['critical']!;
    if (_isLowMemoryMode) return _scaleSettings['low']!;
    return _scaleSettings['normal']!;
  }

  /// إنشاء صورة محسنة تكيفية
  Widget buildOptimizedImage({
    required String imagePath,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    bool isAsset = true,
    String? semanticLabel,
    Widget? errorWidget,
    Widget? loadingWidget,
  }) {
    final scale = getOptimizedScale();

    // حساب الأبعاد المحسنة
    final optimizedWidth = width != null ? (width * scale) : null;
    final optimizedHeight = height != null ? (height * scale) : null;

    // حساب أبعاد التخزين المؤقت
    final cacheWidth = optimizedWidth?.round();
    final cacheHeight = optimizedHeight?.round();

    if (isAsset) {
      return Image.asset(
        imagePath,
        width: optimizedWidth,
        height: optimizedHeight,
        fit: fit,
        cacheWidth: cacheWidth,
        cacheHeight: cacheHeight,
        filterQuality: _getFilterQuality(),
        semanticLabel: semanticLabel,
        errorBuilder: errorWidget != null
            ? (context, error, stackTrace) => errorWidget
            : _buildDefaultErrorWidget,
        frameBuilder: loadingWidget != null
            ? (context, child, frame, wasSynchronouslyLoaded) {
                if (wasSynchronouslyLoaded) return child;
                return frame == null ? loadingWidget : child;
              }
            : null,
      );
    } else {
      return Image.network(
        imagePath,
        width: optimizedWidth,
        height: optimizedHeight,
        fit: fit,
        cacheWidth: cacheWidth,
        cacheHeight: cacheHeight,
        filterQuality: _getFilterQuality(),
        semanticLabel: semanticLabel,
        errorBuilder: errorWidget != null
            ? (context, error, stackTrace) => errorWidget
            : _buildDefaultErrorWidget,
        loadingBuilder: loadingWidget != null
            ? (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return loadingWidget;
              }
            : null,
      );
    }
  }

  /// الحصول على جودة الفلترة المحسنة
  FilterQuality _getFilterQuality() {
    if (_isCriticalMemoryMode) return FilterQuality.low;
    if (_isLowMemoryMode) return FilterQuality.medium;
    return FilterQuality.medium; // توازن بين الجودة والأداء
  }

  /// بناء ويدجت الخطأ الافتراضي
  Widget _buildDefaultErrorWidget(
      BuildContext context, Object error, StackTrace? stackTrace) {
    return Container(
      color: Colors.grey[300],
      child: const Icon(
        Icons.error_outline,
        color: Colors.grey,
        size: 24,
      ),
    );
  }

  /// إنشاء أيقونة محسنة
  Widget buildOptimizedIcon({
    required IconData icon,
    double? size,
    Color? color,
    String? semanticLabel,
  }) {
    final scale = getOptimizedScale();
    final optimizedSize = size != null ? (size * scale) : null;

    return Icon(
      icon,
      size: optimizedSize,
      color: color,
      semanticLabel: semanticLabel,
    );
  }

  /// إنشاء صورة SVG محسنة (إذا كان متاح)
  Widget buildOptimizedSvgImage({
    required String assetPath,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    Color? color,
    String? semanticLabel,
  }) {
    final scale = getOptimizedScale();
    final optimizedWidth = width != null ? (width * scale) : null;
    final optimizedHeight = height != null ? (height * scale) : null;

    // في حالة عدم توفر flutter_svg، نستخدم صورة عادية
    return buildOptimizedImage(
      imagePath: assetPath,
      width: optimizedWidth,
      height: optimizedHeight,
      fit: fit,
      semanticLabel: semanticLabel,
    );
  }

  /// تنظيف التخزين المؤقت للصور
  Future<void> clearImageCache() async {
    try {
      PaintingBinding.instance.imageCache.clear();
      AppLogger.info('تم تنظيف التخزين المؤقت للصور');
    } catch (e) {
      AppLogger.error('خطأ في تنظيف التخزين المؤقت للصور: $e');
    }
  }

  /// تنظيف الصور غير المستخدمة
  Future<void> cleanupUnusedImages() async {
    try {
      PaintingBinding.instance.imageCache.clearLiveImages();
      AppLogger.info('تم تنظيف الصور غير المستخدمة');
    } catch (e) {
      AppLogger.error('خطأ في تنظيف الصور غير المستخدمة: $e');
    }
  }

  /// الحصول على معلومات التخزين المؤقت
  Map<String, dynamic> getCacheInfo() {
    final imageCache = PaintingBinding.instance.imageCache;
    return {
      'currentSize': imageCache.currentSize,
      'maximumSize': imageCache.maximumSize,
      'currentSizeBytes': imageCache.currentSizeBytes,
      'maximumSizeBytes': imageCache.maximumSizeBytes,
      'liveImageCount': imageCache.liveImageCount,
      'pendingImageCount': imageCache.pendingImageCount,
    };
  }

  /// طباعة إحصائيات التخزين المؤقت
  void printCacheStats() {
    final info = getCacheInfo();
    AppLogger.info('إحصائيات التخزين المؤقت للصور:');
    AppLogger.info(
        '  - الحجم الحالي: ${info['currentSize']}/${info['maximumSize']}');
    AppLogger.info(
        '  - البايتات: ${(info['currentSizeBytes'] / 1024 / 1024).toStringAsFixed(1)}MB/${(info['maximumSizeBytes'] / 1024 / 1024).toStringAsFixed(1)}MB');
    AppLogger.info('  - الصور المباشرة: ${info['liveImageCount']}');
    AppLogger.info('  - الصور المعلقة: ${info['pendingImageCount']}');
  }

  /// إعادة تعيين جميع الإعدادات
  void reset() {
    _isLowMemoryMode = false;
    _isCriticalMemoryMode = false;
    _resetImageCache();
    AppLogger.info('تم إعادة تعيين مُحسن الصور');
  }
}
