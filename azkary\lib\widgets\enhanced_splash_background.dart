import 'package:flutter/material.dart';
import 'dart:math' as math;

/// خلفية محسنة لشاشة البداية مع تأثيرات بصرية جميلة
class EnhancedSplashBackground extends StatefulWidget {
  final Widget child;
  final bool isDarkMode;

  const EnhancedSplashBackground({
    super.key,
    required this.child,
    required this.isDarkMode,
  });

  @override
  State<EnhancedSplashBackground> createState() =>
      _EnhancedSplashBackgroundState();
}

class _EnhancedSplashBackgroundState extends State<EnhancedSplashBackground>
    with TickerProviderStateMixin {
  late AnimationController _particleController;
  late AnimationController _gradientController;
  late Animation<double> _particleAnimation;
  late Animation<double> _gradientAnimation;

  @override
  void initState() {
    super.initState();

    // تحكم محسن في حركة الجسيمات - أبطأ وأقل استهلاكاً
    _particleController = AnimationController(
      duration: const Duration(seconds: 15), // أبطأ للأداء
      vsync: this,
    )..repeat();

    // تحكم محسن في تدرج الألوان - أبطأ وأقل تعقيداً
    _gradientController = AnimationController(
      duration: const Duration(seconds: 12), // أبطأ للأداء
      vsync: this,
    )..repeat(reverse: true);

    _particleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _particleController, curve: Curves.linear),
    );

    _gradientAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _gradientController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _particleController.dispose();
    _gradientController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // التدرج اللوني المتحرك
        AnimatedBuilder(
          animation: _gradientAnimation,
          builder: (context, child) {
            return Container(
              decoration: BoxDecoration(gradient: _buildAnimatedGradient()),
            );
          },
        ),

        // الجسيمات المتحركة
        AnimatedBuilder(
          animation: _particleAnimation,
          builder: (context, child) {
            return CustomPaint(
              painter: ParticlesPainter(
                animation: _particleAnimation.value,
                isDarkMode: widget.isDarkMode,
              ),
              size: Size.infinite,
            );
          },
        ),

        // الزخارف الإسلامية المحسنة
        Positioned.fill(
          child: CustomPaint(
            painter: EnhancedIslamicPatternPainter(
              isDarkMode: widget.isDarkMode,
            ),
          ),
        ),

        // المحتوى الرئيسي
        widget.child,
      ],
    );
  }

  LinearGradient _buildAnimatedGradient() {
    final t = _gradientAnimation.value;

    if (widget.isDarkMode) {
      return LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color.lerp(const Color(0xFF0D1B2A), const Color(0xFF1B263B), t)!,
          Color.lerp(const Color(0xFF1B263B), const Color(0xFF415A77), t)!,
          Color.lerp(const Color(0xFF415A77), const Color(0xFF0D1B2A), t)!,
        ],
        stops: [0.0, 0.5, 1.0],
      );
    } else {
      return LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color.lerp(const Color(0xFFF8F9FA), const Color(0xFFE9ECEF), t)!,
          Color.lerp(const Color(0xFFE9ECEF), const Color(0xFFDEE2E6), t)!,
          Color.lerp(const Color(0xFFDEE2E6), const Color(0xFFF8F9FA), t)!,
        ],
        stops: [0.0, 0.5, 1.0],
      );
    }
  }
}

/// رسام الجسيمات المتحركة
class ParticlesPainter extends CustomPainter {
  final double animation;
  final bool isDarkMode;
  final List<Particle> particles = [];

  ParticlesPainter({required this.animation, required this.isDarkMode}) {
    // إنشاء جسيمات أقل للأداء المحسن
    for (int i = 0; i < 8; i++) {
      // تقليل من 20 إلى 8 جسيمات
      particles.add(
        Particle(
          x: math.Random().nextDouble(),
          y: math.Random().nextDouble(),
          size: math.Random().nextDouble() * 2 + 0.5, // أصغر حجماً
          speed: math.Random().nextDouble() * 0.3 + 0.1, // أبطأ حركة
          opacity: math.Random().nextDouble() * 0.4 + 0.1, // شفافية أكثر
        ),
      );
    }
  }

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    for (final particle in particles) {
      final x = (particle.x + animation * particle.speed) % 1.0 * size.width;
      final y = particle.y * size.height;

      paint.color =
          (isDarkMode
              ? Colors.white.withValues(alpha: particle.opacity * 0.3)
              : const Color(
                0xFF2E7D32,
              ).withValues(alpha: particle.opacity * 0.4));

      canvas.drawCircle(Offset(x, y), particle.size, paint);
    }
  }

  @override
  bool shouldRepaint(ParticlesPainter oldDelegate) {
    return oldDelegate.animation != animation;
  }
}

/// فئة الجسيمة
class Particle {
  final double x;
  final double y;
  final double size;
  final double speed;
  final double opacity;

  Particle({
    required this.x,
    required this.y,
    required this.size,
    required this.speed,
    required this.opacity,
  });
}

/// رسام الزخارف الإسلامية المحسنة
class EnhancedIslamicPatternPainter extends CustomPainter {
  final bool isDarkMode;

  EnhancedIslamicPatternPainter({required this.isDarkMode});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..style = PaintingStyle.stroke
          ..strokeWidth = 0.5;

    paint.color =
        isDarkMode
            ? Colors.white.withValues(alpha: 0.08)
            : const Color(0xFF2E7D32).withValues(alpha: 0.12);

    // رسم شبكة مبسطة من الأشكال الهندسية الإسلامية - محسن للأداء
    final patternSize = 180.0; // أكبر لتقليل العدد
    final cols = (size.width / patternSize).ceil();
    final rows = (size.height / patternSize).ceil();

    // تقليل عدد الأشكال المرسومة
    for (int i = 0; i < cols; i += 2) {
      // رسم كل شكل ثاني
      for (int j = 0; j < rows; j += 2) {
        // رسم كل شكل ثاني
        final centerX = i * patternSize;
        final centerY = j * patternSize;

        _drawSimplifiedGeometricPattern(
          canvas,
          paint,
          Offset(centerX, centerY),
          patternSize * 0.3, // أصغر حجماً
        );
      }
    }
  }

  void _drawSimplifiedGeometricPattern(
    Canvas canvas,
    Paint paint,
    Offset center,
    double radius,
  ) {
    // رسم نجمة مبسطة - أقل تعقيداً للأداء
    final path = Path();
    const points = 6; // تقليل من 8 إلى 6 نقاط

    for (int i = 0; i < points * 2; i++) {
      final angle = i * math.pi / points;
      final r = i.isEven ? radius : radius * 0.6; // تبسيط النسبة
      final x = center.dx + r * math.cos(angle);
      final y = center.dy + r * math.sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    path.close();
    canvas.drawPath(path, paint);

    // دائرة داخلية مبسطة
    canvas.drawCircle(center, radius * 0.4, paint);
  }

  @override
  bool shouldRepaint(EnhancedIslamicPatternPainter oldDelegate) {
    return oldDelegate.isDarkMode != isDarkMode;
  }
}
