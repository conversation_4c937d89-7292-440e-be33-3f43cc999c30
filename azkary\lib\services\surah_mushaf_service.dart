import 'package:shared_preferences/shared_preferences.dart';
import '../models/mushaf_page.dart';
import '../models/quran_model.dart';
import '../services/mushaf_service.dart';
import '../utils/logger.dart';

/// خدمة إدارة المصحف الشريف للسورة المحددة
class SurahMushafService {
  static final SurahMushafService _instance = SurahMushafService._internal();
  factory SurahMushafService() => _instance;
  SurahMushafService._internal();

  final MushafService _mushafService = MushafService();

  // مفاتيح التخزين المحلي
  static const String _lastPagePrefix = 'surah_mushaf_last_page_';

  /// تهيئة الخدمة
  Future<void> initialize() async {
    await _mushafService.initialize();
    AppLogger.info('تم تهيئة خدمة المصحف للسورة');
  }

  /// الحصول على صفحات السورة المحددة
  Future<List<MushafPage>> getSurahPages(Surah surah) async {
    try {
      AppLogger.info('جاري تحميل صفحات السورة: ${surah.name}');

      // الحصول على جميع الصفحات
      final allPages = await _mushafService.getAllPages();

      // تصفية الصفحات التي تحتوي على السورة المحددة
      final surahPages =
          allPages.where((page) {
            return page.surahNumbers.contains(surah.number);
          }).toList();

      if (surahPages.isEmpty) {
        // إذا لم نجد صفحات، نحاول إنشاءها بناءً على رقم السورة
        final estimatedPages = _estimateSurahPages(surah);
        AppLogger.warning(
          'لم يتم العثور على صفحات للسورة ${surah.name}، تم إنشاء ${estimatedPages.length} صفحة تقديرية',
        );
        return estimatedPages;
      }

      AppLogger.info(
        'تم العثور على ${surahPages.length} صفحة للسورة ${surah.name}',
      );
      return surahPages;
    } catch (e) {
      AppLogger.error('خطأ في تحميل صفحات السورة ${surah.name}: $e');
      return [];
    }
  }

  /// تقدير صفحات السورة بناءً على معلومات السورة
  List<MushafPage> _estimateSurahPages(Surah surah) {
    // خريطة تقديرية لصفحات بداية السور الرئيسية
    final Map<int, int> surahStartPages = {
      1: 1, // الفاتحة
      2: 2, // البقرة
      3: 50, // آل عمران
      4: 77, // النساء
      5: 106, // المائدة
      6: 128, // الأنعام
      7: 151, // الأعراف
      8: 177, // الأنفال
      9: 187, // التوبة
      10: 208, // يونس
      11: 221, // هود
      12: 235, // يوسف
      13: 249, // الرعد
      14: 255, // إبراهيم
      15: 262, // الحجر
      16: 267, // النحل
      17: 282, // الإسراء
      18: 293, // الكهف
      19: 305, // مريم
      20: 312, // طه
      // يمكن إضافة المزيد حسب الحاجة
    };

    final startPage = surahStartPages[surah.number] ?? 1;
    final nextSurahNumber = surah.number + 1;
    final endPage =
        surahStartPages[nextSurahNumber] ??
        (startPage + _estimatePageCount(surah));

    final pages = <MushafPage>[];
    for (int pageNum = startPage; pageNum < endPage; pageNum++) {
      if (pageNum <= 604) {
        // التأكد من عدم تجاوز عدد صفحات المصحف
        pages.add(
          MushafPage(
            pageNumber: pageNum,
            imageUrl:
                'https://everyayah.com/data/QuranPages/${pageNum.toString().padLeft(3, '0')}.png',
            surahNumbers: [surah.number],
            surahNames: [surah.name],
            juzNumber: _estimateJuzNumber(pageNum),
            hizbNumber: _estimateHizbNumber(pageNum),
          ),
        );
      }
    }

    return pages;
  }

  /// تقدير عدد الصفحات للسورة
  int _estimatePageCount(Surah surah) {
    // تقدير بناءً على عدد الآيات
    if (surah.numberOfAyahs <= 10) return 1;
    if (surah.numberOfAyahs <= 50) return 2;
    if (surah.numberOfAyahs <= 100) return 3;
    if (surah.numberOfAyahs <= 200) return 5;
    return (surah.numberOfAyahs / 40).ceil(); // تقدير تقريبي
  }

  /// تقدير رقم الجزء
  int _estimateJuzNumber(int pageNumber) {
    return ((pageNumber - 1) ~/ 20) + 1;
  }

  /// تقدير رقم الحزب
  int _estimateHizbNumber(int pageNumber) {
    return ((pageNumber - 1) ~/ 10) + 1;
  }

  /// حفظ آخر صفحة تم عرضها للسورة
  Future<void> saveLastViewedPage(int surahNumber, int pageNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('$_lastPagePrefix$surahNumber', pageNumber);
      AppLogger.info('تم حفظ آخر صفحة للسورة $surahNumber: $pageNumber');
    } catch (e) {
      AppLogger.error('خطأ في حفظ آخر صفحة للسورة $surahNumber: $e');
    }
  }

  /// تحميل آخر صفحة تم عرضها للسورة
  Future<int> getLastViewedPage(int surahNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastPage = prefs.getInt('$_lastPagePrefix$surahNumber') ?? 1;
      AppLogger.info('آخر صفحة محفوظة للسورة $surahNumber: $lastPage');
      return lastPage;
    } catch (e) {
      AppLogger.error('خطأ في تحميل آخر صفحة للسورة $surahNumber: $e');
      return 1;
    }
  }

  /// الحصول على إعدادات العرض
  MushafViewSettings get settings => _mushafService.settings;

  /// حفظ إعدادات العرض
  Future<void> saveSettings(MushafViewSettings settings) async {
    await _mushafService.saveSettings(settings);
  }

  /// الحصول على معلومات الصفحة
  String getPageInfo(MushafPage page, Surah surah) {
    final pageInSurah = _getPageNumberInSurah(page, surah);
    return 'صفحة $pageInSurah من ${surah.name}';
  }

  /// الحصول على رقم الصفحة داخل السورة
  int _getPageNumberInSurah(MushafPage page, Surah surah) {
    // هذا تقدير بسيط، يمكن تحسينه لاحقاً
    return page.pageNumber;
  }

  /// البحث عن صفحة تحتوي على آية معينة
  Future<MushafPage?> findPageByAyah(Surah surah, int ayahNumber) async {
    try {
      final surahPages = await getSurahPages(surah);

      // للآن نعيد الصفحة الأولى، يمكن تحسين هذا لاحقاً
      // بناءً على خريطة دقيقة للآيات والصفحات
      if (surahPages.isNotEmpty) {
        // تقدير بسيط: كل صفحة تحتوي على حوالي 15 آية
        final estimatedPageIndex = (ayahNumber - 1) ~/ 15;
        final pageIndex = estimatedPageIndex.clamp(0, surahPages.length - 1);
        return surahPages[pageIndex];
      }

      return null;
    } catch (e) {
      AppLogger.error(
        'خطأ في البحث عن صفحة الآية $ayahNumber في السورة ${surah.name}: $e',
      );
      return null;
    }
  }

  /// مسح البيانات المحفوظة للسورة
  Future<void> clearSurahData(int surahNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('$_lastPagePrefix$surahNumber');
      AppLogger.info('تم مسح البيانات المحفوظة للسورة $surahNumber');
    } catch (e) {
      AppLogger.error('خطأ في مسح البيانات للسورة $surahNumber: $e');
    }
  }

  /// الحصول على الصفحة التالية في السورة
  MushafPage? getNextPage(List<MushafPage> surahPages, int currentPageNumber) {
    final currentIndex = surahPages.indexWhere(
      (page) => page.pageNumber == currentPageNumber,
    );
    if (currentIndex != -1 && currentIndex < surahPages.length - 1) {
      return surahPages[currentIndex + 1];
    }
    return null;
  }

  /// الحصول على الصفحة السابقة في السورة
  MushafPage? getPreviousPage(
    List<MushafPage> surahPages,
    int currentPageNumber,
  ) {
    final currentIndex = surahPages.indexWhere(
      (page) => page.pageNumber == currentPageNumber,
    );
    if (currentIndex > 0) {
      return surahPages[currentIndex - 1];
    }
    return null;
  }

  /// التحقق من وجود صفحة تالية
  bool hasNextPage(List<MushafPage> surahPages, int currentPageNumber) {
    return getNextPage(surahPages, currentPageNumber) != null;
  }

  /// التحقق من وجود صفحة سابقة
  bool hasPreviousPage(List<MushafPage> surahPages, int currentPageNumber) {
    return getPreviousPage(surahPages, currentPageNumber) != null;
  }
}
