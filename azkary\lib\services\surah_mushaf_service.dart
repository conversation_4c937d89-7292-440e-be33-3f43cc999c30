import 'package:shared_preferences/shared_preferences.dart';
import '../models/mushaf_page.dart';
import '../models/quran_model.dart';
import '../utils/logger.dart';

/// خدمة إدارة المصحف الشريف للسورة المحددة
class SurahMushafService {
  static final SurahMushafService _instance = SurahMushafService._internal();
  factory SurahMushafService() => _instance;
  SurahMushafService._internal();

  // مفاتيح التخزين المحلي
  static const String _lastPagePrefix = 'surah_mushaf_last_page_';

  /// تهيئة الخدمة
  Future<void> initialize() async {
    try {
      // تهيئة بسيطة بدون الاعتماد على MushafService
      AppLogger.info('تم تهيئة خدمة المصحف للسورة');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة خدمة المصحف للسورة: $e');
      rethrow;
    }
  }

  /// الحصول على صفحات السورة المحددة
  Future<List<MushafPage>> getSurahPages(Surah surah) async {
    try {
      AppLogger.info('=== بدء تحميل صفحات السورة ===');
      AppLogger.info('اسم السورة: ${surah.name}');
      AppLogger.info('رقم السورة: ${surah.number}');
      AppLogger.info('عدد الآيات: ${surah.numberOfAyahs}');

      // استخدام البيانات التقديرية مباشرة لضمان الحصول على صفحات
      final estimatedPages = _estimateSurahPages(surah);

      if (estimatedPages.isEmpty) {
        final errorMsg = 'فشل في إنشاء صفحات تقديرية للسورة ${surah.name}';
        AppLogger.error(errorMsg);
        throw Exception(errorMsg);
      }

      AppLogger.info(
        'تم إنشاء ${estimatedPages.length} صفحة للسورة ${surah.name}',
      );

      // طباعة تفاصيل الصفحات للتشخيص
      for (int i = 0; i < estimatedPages.length && i < 3; i++) {
        final page = estimatedPages[i];
        AppLogger.info(
          'صفحة ${i + 1}: رقم ${page.pageNumber}, رابط: ${page.imageUrl}',
        );
      }

      AppLogger.info('=== انتهاء تحميل صفحات السورة ===');
      return estimatedPages;
    } catch (e) {
      AppLogger.error('خطأ في تحميل صفحات السورة ${surah.name}: $e');
      rethrow; // إعادة رمي الخطأ للتعامل معه في الطبقة العليا
    }
  }

  /// تقدير صفحات السورة بناءً على معلومات السورة
  List<MushafPage> _estimateSurahPages(Surah surah) {
    // خريطة دقيقة لصفحات بداية السور في المصحف الشريف
    final Map<int, int> surahStartPages = {
      1: 1, // الفاتحة
      2: 2, // البقرة
      3: 50, // آل عمران
      4: 77, // النساء
      5: 106, // المائدة
      6: 128, // الأنعام
      7: 151, // الأعراف
      8: 177, // الأنفال
      9: 187, // التوبة
      10: 208, // يونس
      11: 221, // هود
      12: 235, // يوسف
      13: 249, // الرعد
      14: 255, // إبراهيم
      15: 262, // الحجر
      16: 267, // النحل
      17: 282, // الإسراء
      18: 293, // الكهف
      19: 305, // مريم
      20: 312, // طه
      21: 322, // الأنبياء
      22: 332, // الحج
      23: 342, // المؤمنون
      24: 350, // النور
      25: 359, // الفرقان
      26: 367, // الشعراء
      27: 377, // النمل
      28: 385, // القصص
      29: 396, // العنكبوت
      30: 404, // الروم
      31: 411, // لقمان
      32: 415, // السجدة
      33: 418, // الأحزاب
      34: 428, // سبأ
      35: 434, // فاطر
      36: 440, // يس
      37: 446, // الصافات
      38: 453, // ص
      39: 458, // الزمر
      40: 467, // غافر
      41: 477, // فصلت
      42: 483, // الشورى
      43: 489, // الزخرف
      44: 496, // الدخان
      45: 499, // الجاثية
      46: 502, // الأحقاف
      47: 507, // محمد
      48: 511, // الفتح
      49: 515, // الحجرات
      50: 518, // ق
      51: 520, // الذاريات
      52: 523, // الطور
      53: 526, // النجم
      54: 528, // القمر
      55: 531, // الرحمن
      56: 534, // الواقعة
      57: 537, // الحديد
      58: 542, // المجادلة
      59: 545, // الحشر
      60: 549, // الممتحنة
      61: 551, // الصف
      62: 553, // الجمعة
      63: 554, // المنافقون
      64: 556, // التغابن
      65: 558, // الطلاق
      66: 560, // التحريم
      67: 562, // الملك
      68: 564, // القلم
      69: 566, // الحاقة
      70: 568, // المعارج
      71: 570, // نوح
      72: 572, // الجن
      73: 574, // المزمل
      74: 575, // المدثر
      75: 577, // القيامة
      76: 578, // الإنسان
      77: 580, // المرسلات
      78: 582, // النبأ
      79: 583, // النازعات
      80: 585, // عبس
      81: 586, // التكوير
      82: 587, // الانفطار
      83: 587, // المطففين
      84: 589, // الانشقاق
      85: 590, // البروج
      86: 591, // الطارق
      87: 591, // الأعلى
      88: 592, // الغاشية
      89: 593, // الفجر
      90: 594, // البلد
      91: 595, // الشمس
      92: 595, // الليل
      93: 596, // الضحى
      94: 596, // الشرح
      95: 597, // التين
      96: 597, // العلق
      97: 598, // القدر
      98: 598, // البينة
      99: 599, // الزلزلة
      100: 599, // العاديات
      101: 600, // القارعة
      102: 600, // التكاثر
      103: 601, // العصر
      104: 601, // الهمزة
      105: 601, // الفيل
      106: 602, // قريش
      107: 602, // الماعون
      108: 602, // الكوثر
      109: 603, // الكافرون
      110: 603, // النصر
      111: 603, // المسد
      112: 604, // الإخلاص
      113: 604, // الفلق
      114: 604, // الناس
    };

    final startPage = surahStartPages[surah.number];
    if (startPage == null) {
      AppLogger.error('لا توجد بيانات صفحة للسورة رقم ${surah.number}');
      return [];
    }

    // تحديد صفحة النهاية
    int endPage;
    if (surah.number == 114) {
      // السورة الأخيرة
      endPage = 605; // لتشمل الصفحة 604
    } else {
      final nextSurahNumber = surah.number + 1;
      endPage =
          surahStartPages[nextSurahNumber] ??
          (startPage + _estimatePageCount(surah));
    }

    final pages = <MushafPage>[];
    for (
      int pageNum = startPage;
      pageNum < endPage && pageNum <= 604;
      pageNum++
    ) {
      // استخدام رابط صور محسن مع fallback
      final imageUrl = _getImageUrl(pageNum);

      pages.add(
        MushafPage(
          pageNumber: pageNum,
          imageUrl: imageUrl,
          surahNumbers: [surah.number],
          surahNames: [surah.name],
          juzNumber: _estimateJuzNumber(pageNum),
          hizbNumber: _estimateHizbNumber(pageNum),
        ),
      );
    }

    AppLogger.info(
      'تم إنشاء ${pages.length} صفحة للسورة ${surah.name} (من $startPage إلى ${endPage - 1})',
    );
    return pages;
  }

  /// الحصول على رابط صورة الصفحة مع fallback
  String _getImageUrl(int pageNumber) {
    final pageStr = pageNumber.toString().padLeft(3, '0');

    // استخدام رابط موثوق ومختبر
    final primaryUrl = 'https://everyayah.com/data/QuranPages/$pageStr.png';

    AppLogger.info('رابط صورة الصفحة $pageNumber: $primaryUrl');
    return primaryUrl;
  }

  /// تقدير عدد الصفحات للسورة
  int _estimatePageCount(Surah surah) {
    // تقدير بناءً على عدد الآيات
    if (surah.numberOfAyahs <= 10) return 1;
    if (surah.numberOfAyahs <= 50) return 2;
    if (surah.numberOfAyahs <= 100) return 3;
    if (surah.numberOfAyahs <= 200) return 5;
    return (surah.numberOfAyahs / 40).ceil(); // تقدير تقريبي
  }

  /// تقدير رقم الجزء
  int _estimateJuzNumber(int pageNumber) {
    return ((pageNumber - 1) ~/ 20) + 1;
  }

  /// تقدير رقم الحزب
  int _estimateHizbNumber(int pageNumber) {
    return ((pageNumber - 1) ~/ 10) + 1;
  }

  /// حفظ آخر صفحة تم عرضها للسورة
  Future<void> saveLastViewedPage(int surahNumber, int pageNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('$_lastPagePrefix$surahNumber', pageNumber);
      AppLogger.info('تم حفظ آخر صفحة للسورة $surahNumber: $pageNumber');
    } catch (e) {
      AppLogger.error('خطأ في حفظ آخر صفحة للسورة $surahNumber: $e');
    }
  }

  /// تحميل آخر صفحة تم عرضها للسورة
  Future<int> getLastViewedPage(int surahNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastPage = prefs.getInt('$_lastPagePrefix$surahNumber') ?? 1;
      AppLogger.info('آخر صفحة محفوظة للسورة $surahNumber: $lastPage');
      return lastPage;
    } catch (e) {
      AppLogger.error('خطأ في تحميل آخر صفحة للسورة $surahNumber: $e');
      return 1;
    }
  }

  /// الحصول على إعدادات العرض
  MushafViewSettings get settings => const MushafViewSettings();

  /// حفظ إعدادات العرض
  Future<void> saveSettings(MushafViewSettings settings) async {
    // يمكن إضافة حفظ الإعدادات لاحقاً إذا لزم الأمر
    AppLogger.info('تم حفظ إعدادات المصحف للسورة');
  }

  /// الحصول على معلومات الصفحة
  String getPageInfo(MushafPage page, Surah surah) {
    final pageInSurah = _getPageNumberInSurah(page, surah);
    return 'صفحة $pageInSurah من ${surah.name}';
  }

  /// الحصول على رقم الصفحة داخل السورة
  int _getPageNumberInSurah(MushafPage page, Surah surah) {
    // هذا تقدير بسيط، يمكن تحسينه لاحقاً
    return page.pageNumber;
  }

  /// البحث عن صفحة تحتوي على آية معينة
  Future<MushafPage?> findPageByAyah(Surah surah, int ayahNumber) async {
    try {
      final surahPages = await getSurahPages(surah);

      // للآن نعيد الصفحة الأولى، يمكن تحسين هذا لاحقاً
      // بناءً على خريطة دقيقة للآيات والصفحات
      if (surahPages.isNotEmpty) {
        // تقدير بسيط: كل صفحة تحتوي على حوالي 15 آية
        final estimatedPageIndex = (ayahNumber - 1) ~/ 15;
        final pageIndex = estimatedPageIndex.clamp(0, surahPages.length - 1);
        return surahPages[pageIndex];
      }

      return null;
    } catch (e) {
      AppLogger.error(
        'خطأ في البحث عن صفحة الآية $ayahNumber في السورة ${surah.name}: $e',
      );
      return null;
    }
  }

  /// مسح البيانات المحفوظة للسورة
  Future<void> clearSurahData(int surahNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('$_lastPagePrefix$surahNumber');
      AppLogger.info('تم مسح البيانات المحفوظة للسورة $surahNumber');
    } catch (e) {
      AppLogger.error('خطأ في مسح البيانات للسورة $surahNumber: $e');
    }
  }

  /// الحصول على الصفحة التالية في السورة
  MushafPage? getNextPage(List<MushafPage> surahPages, int currentPageNumber) {
    final currentIndex = surahPages.indexWhere(
      (page) => page.pageNumber == currentPageNumber,
    );
    if (currentIndex != -1 && currentIndex < surahPages.length - 1) {
      return surahPages[currentIndex + 1];
    }
    return null;
  }

  /// الحصول على الصفحة السابقة في السورة
  MushafPage? getPreviousPage(
    List<MushafPage> surahPages,
    int currentPageNumber,
  ) {
    final currentIndex = surahPages.indexWhere(
      (page) => page.pageNumber == currentPageNumber,
    );
    if (currentIndex > 0) {
      return surahPages[currentIndex - 1];
    }
    return null;
  }

  /// التحقق من وجود صفحة تالية
  bool hasNextPage(List<MushafPage> surahPages, int currentPageNumber) {
    return getNextPage(surahPages, currentPageNumber) != null;
  }

  /// التحقق من وجود صفحة سابقة
  bool hasPreviousPage(List<MushafPage> surahPages, int currentPageNumber) {
    return getPreviousPage(surahPages, currentPageNumber) != null;
  }
}
