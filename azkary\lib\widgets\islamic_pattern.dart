import 'package:flutter/material.dart';
import 'dart:math' as math;

/// مكون لرسم زخارف إسلامية
class IslamicPattern extends StatelessWidget {
  final Color? color;
  final double opacity;

  const IslamicPattern({super.key, this.color, this.opacity = 0.1});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // تحديد اللون والشفافية بناءً على الوضع
    Color patternColor = color ?? theme.colorScheme.primary;
    double finalOpacity = opacity;

    // تعديل الشفافية حسب الوضع
    if (isDarkMode) {
      // في الأوضاع الداكنة، نقلل الشفافية لتجنب التداخل
      finalOpacity = opacity * 0.7;
      // في الوضع المعتم (تويتر)، نستخدم لون أفتح قليلاً
      if (theme.scaffoldBackgroundColor == const Color(0xFF15202B)) {
        patternColor = patternColor.withAlpha(100);
        finalOpacity = opacity * 0.6; // شفافية أقل للوضع المعتم
      }
    }

    return CustomPaint(
      painter: IslamicPatternPainter(
        color: patternColor,
        opacity: finalOpacity,
      ),
      size: Size.infinite,
    );
  }
}

/// رسام الزخارف الإسلامية
class IslamicPatternPainter extends CustomPainter {
  final Color color;
  final double opacity;

  IslamicPatternPainter({required this.color, required this.opacity});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color.withAlpha((opacity * 255).round())
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0;

    final patternSize = 120.0; // أكبر لتقليل العدد
    final horizontalCount = (size.width / patternSize).ceil();
    final verticalCount = (size.height / patternSize).ceil();

    // رسم كل نمط ثاني للأداء المحسن
    for (int i = 0; i < horizontalCount; i += 2) {
      for (int j = 0; j < verticalCount; j += 2) {
        final centerX = i * patternSize;
        final centerY = j * patternSize;

        // رسم النمط الإسلامي المبسط
        _drawSimplifiedIslamicPattern(
          canvas,
          paint,
          Offset(centerX, centerY),
          patternSize,
        );
      }
    }
  }

  void _drawSimplifiedIslamicPattern(
    Canvas canvas,
    Paint paint,
    Offset center,
    double size,
  ) {
    // رسم نمط إسلامي مبسط للأداء المحسن

    // النمط الأساسي فقط - نجمة مبسطة
    _drawSimplifiedStar(canvas, paint, center, size * 0.3);

    // دائرة خارجية واحدة فقط
    canvas.drawCircle(center, size * 0.35, paint);

    // إزالة العناصر المعقدة للأداء:
    // - المثمن الخارجي
    // - المربع المدور
    // - الأشعة الزخرفية
    // - النقاط الزخرفية
  }

  void _drawSimplifiedStar(
    Canvas canvas,
    Paint paint,
    Offset center,
    double radius,
  ) {
    final path = Path();

    // نجمة مبسطة بـ 6 نقاط بدلاً من 8
    for (int i = 0; i < 6; i++) {
      final angle = i * math.pi / 3;
      final outerX = center.dx + radius * math.cos(angle);
      final outerY = center.dy + radius * math.sin(angle);

      final innerAngle = angle + math.pi / 6;
      final innerRadius = radius * 0.5; // نسبة مبسطة
      final innerX = center.dx + innerRadius * math.cos(innerAngle);
      final innerY = center.dy + innerRadius * math.sin(innerAngle);

      if (i == 0) {
        path.moveTo(outerX, outerY);
      } else {
        path.lineTo(outerX, outerY);
      }

      path.lineTo(innerX, innerY);
    }

    path.close();
    canvas.drawPath(path, paint);
  }

  // تم حذف الدوال المعقدة لتحسين الأداء:
  // - _drawOctagon
  // - _drawRotatedSquare
  // - _drawDecorativeRays
  // - _drawCornerDots

  @override
  bool shouldRepaint(IslamicPatternPainter oldDelegate) {
    return oldDelegate.color != color || oldDelegate.opacity != opacity;
  }
}
