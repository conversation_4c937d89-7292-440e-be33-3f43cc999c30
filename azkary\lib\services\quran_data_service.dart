import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/quran_data_models.dart';
import '../utils/logger.dart';

/// خدمة القرآن الكريم الجديدة باستخدام مصدر Quran-Data
/// المصدر: https://github.com/rn0x/Quran-Data
/// API: https://quran.i8x.net/api/
class QuranDataService {
  static const String _baseUrl = 'https://quran.i8x.net/api';
  static const Duration _timeout = Duration(seconds: 30);
  
  // Singleton pattern
  static final QuranDataService _instance = QuranDataService._internal();
  factory QuranDataService() => _instance;
  QuranDataService._internal();

  /// Headers للطلبات
  static const Map<String, String> _headers = {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
    'User-Agent': 'AzkaryApp/1.0 (Islamic App)',
  };

  /// الحصول على جميع السور
  Future<List<QuranSurah>> getAllSurahs() async {
    try {
      AppLogger.info('🕌 جاري تحميل جميع السور من API الجديد...');
      
      final response = await http.get(
        Uri.parse('$_baseUrl/surahs'),
        headers: _headers,
      ).timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['success'] == true && data['result'] != null) {
          final List<dynamic> surahsData = data['result'];
          
          final surahs = surahsData.map((surahData) {
            return QuranSurah.fromJson(surahData);
          }).toList();
          
          AppLogger.info('✅ تم تحميل ${surahs.length} سورة بنجاح');
          return surahs;
        } else {
          throw Exception('استجابة API غير صحيحة: ${data['message'] ?? 'خطأ غير معروف'}');
        }
      } else {
        throw Exception('فشل في الاتصال بـ API: ${response.statusCode}');
      }
    } catch (e) {
      AppLogger.error('❌ خطأ في تحميل السور: $e');
      rethrow;
    }
  }

  /// الحصول على سورة محددة مع تفاصيلها
  Future<QuranSurahDetail> getSurahDetail(int surahNumber) async {
    try {
      AppLogger.info('📖 جاري تحميل تفاصيل السورة رقم $surahNumber...');
      
      final response = await http.get(
        Uri.parse('$_baseUrl/surah/$surahNumber'),
        headers: _headers,
      ).timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['success'] == true && data['result'] != null) {
          final surahDetail = QuranSurahDetail.fromJson(data['result']);
          
          AppLogger.info('✅ تم تحميل تفاصيل السورة ${surahDetail.name.ar} بنجاح');
          return surahDetail;
        } else {
          throw Exception('استجابة API غير صحيحة: ${data['message'] ?? 'خطأ غير معروف'}');
        }
      } else {
        throw Exception('فشل في الاتصال بـ API: ${response.statusCode}');
      }
    } catch (e) {
      AppLogger.error('❌ خطأ في تحميل تفاصيل السورة $surahNumber: $e');
      rethrow;
    }
  }

  /// الحصول على آيات سورة محددة
  Future<List<QuranVerse>> getSurahVerses(int surahNumber) async {
    try {
      AppLogger.info('📜 جاري تحميل آيات السورة رقم $surahNumber...');
      
      final response = await http.get(
        Uri.parse('$_baseUrl/verses/$surahNumber'),
        headers: _headers,
      ).timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['success'] == true && data['result'] != null) {
          final List<dynamic> versesData = data['result'];
          
          final verses = versesData.map((verseData) {
            return QuranVerse.fromJson(verseData);
          }).toList();
          
          AppLogger.info('✅ تم تحميل ${verses.length} آية للسورة $surahNumber');
          return verses;
        } else {
          throw Exception('استجابة API غير صحيحة: ${data['message'] ?? 'خطأ غير معروف'}');
        }
      } else {
        throw Exception('فشل في الاتصال بـ API: ${response.statusCode}');
      }
    } catch (e) {
      AppLogger.error('❌ خطأ في تحميل آيات السورة $surahNumber: $e');
      rethrow;
    }
  }

  /// الحصول على التسجيلات الصوتية لسورة محددة
  Future<List<QuranAudio>> getSurahAudio(int surahNumber) async {
    try {
      AppLogger.info('🎧 جاري تحميل التسجيلات الصوتية للسورة رقم $surahNumber...');
      
      final response = await http.get(
        Uri.parse('$_baseUrl/audio/$surahNumber'),
        headers: _headers,
      ).timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['success'] == true && data['result'] != null) {
          final List<dynamic> audioData = data['result'];
          
          final audios = audioData.map((audio) {
            return QuranAudio.fromJson(audio);
          }).toList();
          
          AppLogger.info('✅ تم تحميل ${audios.length} تسجيل صوتي للسورة $surahNumber');
          return audios;
        } else {
          throw Exception('استجابة API غير صحيحة: ${data['message'] ?? 'خطأ غير معروف'}');
        }
      } else {
        throw Exception('فشل في الاتصال بـ API: ${response.statusCode}');
      }
    } catch (e) {
      AppLogger.error('❌ خطأ في تحميل التسجيلات الصوتية للسورة $surahNumber: $e');
      rethrow;
    }
  }

  /// الحصول على آيات السجدة
  Future<List<QuranVerse>> getSajdaVerses() async {
    try {
      AppLogger.info('🕋 جاري تحميل آيات السجدة...');
      
      final response = await http.get(
        Uri.parse('$_baseUrl/sajda'),
        headers: _headers,
      ).timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['success'] == true && data['result'] != null) {
          final List<dynamic> versesData = data['result'];
          
          final verses = versesData.map((verseData) {
            return QuranVerse.fromJson(verseData);
          }).toList();
          
          AppLogger.info('✅ تم تحميل ${verses.length} آية سجدة');
          return verses;
        } else {
          throw Exception('استجابة API غير صحيحة: ${data['message'] ?? 'خطأ غير معروف'}');
        }
      } else {
        throw Exception('فشل في الاتصال بـ API: ${response.statusCode}');
      }
    } catch (e) {
      AppLogger.error('❌ خطأ في تحميل آيات السجدة: $e');
      rethrow;
    }
  }

  /// الحصول على معلومات الصفحات
  Future<QuranPageInfo> getPageInfo({
    int? surahNumber,
    int? verseNumber,
    int? pageNumber,
  }) async {
    try {
      String endpoint;
      
      if (pageNumber != null) {
        endpoint = '$_baseUrl/pages?page=$pageNumber';
        AppLogger.info('📄 جاري تحميل معلومات الصفحة رقم $pageNumber...');
      } else if (surahNumber != null && verseNumber != null) {
        endpoint = '$_baseUrl/pages/$surahNumber/$verseNumber';
        AppLogger.info('📄 جاري تحميل معلومات الصفحة للسورة $surahNumber الآية $verseNumber...');
      } else if (surahNumber != null) {
        endpoint = '$_baseUrl/pages/$surahNumber';
        AppLogger.info('📄 جاري تحميل معلومات صفحات السورة $surahNumber...');
      } else {
        throw ArgumentError('يجب تحديد رقم الصفحة أو رقم السورة');
      }
      
      final response = await http.get(
        Uri.parse(endpoint),
        headers: _headers,
      ).timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['success'] == true && data['result'] != null) {
          final pageInfo = QuranPageInfo.fromJson(data['result']);
          
          AppLogger.info('✅ تم تحميل معلومات الصفحة ${pageInfo.page}');
          return pageInfo;
        } else {
          throw Exception('استجابة API غير صحيحة: ${data['message'] ?? 'خطأ غير معروف'}');
        }
      } else {
        throw Exception('فشل في الاتصال بـ API: ${response.statusCode}');
      }
    } catch (e) {
      AppLogger.error('❌ خطأ في تحميل معلومات الصفحة: $e');
      rethrow;
    }
  }

  /// اختبار الاتصال بـ API
  Future<bool> testConnection() async {
    try {
      AppLogger.info('🔍 اختبار الاتصال بـ API الجديد...');
      
      final response = await http.get(
        Uri.parse('$_baseUrl/surahs'),
        headers: _headers,
      ).timeout(const Duration(seconds: 10));

      final success = response.statusCode == 200;
      AppLogger.info(success ? '✅ API يعمل بشكل طبيعي' : '❌ API لا يعمل');
      
      return success;
    } catch (e) {
      AppLogger.error('❌ خطأ في اختبار API: $e');
      return false;
    }
  }

  /// الحصول على معلومات API
  Map<String, String> getApiInfo() {
    return {
      'name': 'Quran Data API',
      'base_url': _baseUrl,
      'source': 'https://github.com/rn0x/Quran-Data',
      'documentation': 'https://quran.i8x.net/docs',
      'version': '2.0',
      'features': 'Surahs, Verses, Audio, Pages, Sajda',
      'language_support': 'Arabic, English',
      'license': 'MIT',
    };
  }

  /// طباعة معلومات API
  void printApiInfo() {
    final info = getApiInfo();
    AppLogger.info('=== معلومات API القرآن الكريم الجديد ===');
    info.forEach((key, value) {
      AppLogger.info('$key: $value');
    });
    AppLogger.info('==========================================');
  }
}
