import 'package:flutter/material.dart';
import '../models/quran_model.dart';
import '../screens/surah_detail_screen.dart';

/// بطاقة سورة محسنة مع المعلومات الجديدة
class EnhancedSurahCard extends StatelessWidget {
  final Surah surah;
  final bool showDetailedInfo;
  final bool showAudioButton;
  final VoidCallback? onAudioTap;

  const EnhancedSurahCard({
    super.key,
    required this.surah,
    this.showDetailedInfo = true,
    this.showAudioButton = true,
    this.onAudioTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return RepaintBoundary(
      child: Card(
        elevation: 2,
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        color: isDarkMode ? Colors.grey[800] : Colors.white,
        child: InkWell(
          onTap: () => _navigateToSurahDetail(context),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // رقم السورة
                _buildSurahNumber(isDarkMode),
                const SizedBox(width: 16),

                // معلومات السورة
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // اسم السورة
                      _buildSurahName(isDarkMode),
                      const SizedBox(height: 4),

                      // الاسم الإنجليزي
                      _buildEnglishName(isDarkMode),

                      if (showDetailedInfo) ...[
                        const SizedBox(height: 8),
                        // المعلومات المفصلة
                        _buildDetailedInfo(isDarkMode),
                      ],
                    ],
                  ),
                ),

                // أزرار الإجراءات
                _buildActionButtons(context, isDarkMode),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء رقم السورة
  Widget _buildSurahNumber(bool isDarkMode) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.amber[600] : Colors.brown[600],
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: (isDarkMode ? Colors.amber[600] : Colors.brown[600])!
                .withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Text(
          _convertToArabicNumbers(surah.number),
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
            fontFamily: 'Amiri',
          ),
        ),
      ),
    );
  }

  /// بناء اسم السورة
  Widget _buildSurahName(bool isDarkMode) {
    return Text(
      surah.name,
      style: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: isDarkMode ? Colors.white : Colors.black87,
        fontFamily: 'Amiri',
      ),
    );
  }

  /// بناء الاسم الإنجليزي
  Widget _buildEnglishName(bool isDarkMode) {
    return Text(
      surah.englishName,
      style: TextStyle(
        fontSize: 14,
        color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
        fontFamily: 'Roboto',
      ),
    );
  }

  /// بناء المعلومات المفصلة
  Widget _buildDetailedInfo(bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // معلومات أساسية
        _buildInfoRow(
          Icons.format_list_numbered,
          '${_convertToArabicNumbers(surah.numberOfAyahs)} آية',
          isDarkMode,
        ),
        const SizedBox(height: 4),

        // مكان النزول
        _buildInfoRow(
          surah.isMeccan ? Icons.location_on : Icons.location_city,
          surah.isMeccan ? 'مكية' : 'مدنية',
          isDarkMode,
        ),

        // معلومات إضافية إذا كانت متوفرة
        if (surah.wordsCount != null || surah.lettersCount != null) ...[
          const SizedBox(height: 4),
          _buildAdditionalInfo(isDarkMode),
        ],
      ],
    );
  }

  /// بناء صف معلومات
  Widget _buildInfoRow(IconData icon, String text, bool isDarkMode) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
        ),
        const SizedBox(width: 6),
        Text(
          text,
          style: TextStyle(
            fontSize: 12,
            color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
            fontFamily: 'Amiri',
          ),
        ),
      ],
    );
  }

  /// بناء المعلومات الإضافية
  Widget _buildAdditionalInfo(bool isDarkMode) {
    final parts = <String>[];
    if (surah.wordsCount != null) {
      parts.add('${_convertToArabicNumbers(surah.wordsCount!)} كلمة');
    }
    if (surah.lettersCount != null) {
      parts.add('${_convertToArabicNumbers(surah.lettersCount!)} حرف');
    }

    if (parts.isEmpty) return const SizedBox.shrink();

    return Row(
      children: [
        Icon(
          Icons.text_fields,
          size: 16,
          color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
        ),
        const SizedBox(width: 6),
        Expanded(
          child: Text(
            parts.join(' • '),
            style: TextStyle(
              fontSize: 12,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              fontFamily: 'Amiri',
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons(BuildContext context, bool isDarkMode) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // زر الصوتيات
        if (showAudioButton)
          IconButton(
            onPressed: onAudioTap ?? () => _showAudioOptions(context),
            icon: Icon(
              Icons.play_circle_outline,
              color: isDarkMode ? Colors.amber[600] : Colors.brown[600],
              size: 28,
            ),
            tooltip: 'تشغيل التلاوة',
          ),

        // زر المزيد
        IconButton(
          onPressed: () => _navigateToSurahDetail(context),
          icon: Icon(
            Icons.arrow_forward_ios,
            color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
            size: 20,
          ),
          tooltip: 'عرض السورة',
        ),
      ],
    );
  }

  /// الانتقال إلى تفاصيل السورة
  void _navigateToSurahDetail(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SurahDetailScreen(surah: surah),
      ),
    );
  }

  /// عرض خيارات الصوتيات
  void _showAudioOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildAudioOptionsSheet(context),
    );
  }

  /// بناء ورقة خيارات الصوتيات
  Widget _buildAudioOptionsSheet(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[800] : Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // مقبض الورقة
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: isDarkMode ? Colors.grey[600] : Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),

          // العنوان
          Text(
            'تلاوة سورة ${surah.name}',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.black87,
              fontFamily: 'Amiri',
            ),
          ),
          const SizedBox(height: 20),

          // رسالة مؤقتة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isDarkMode ? Colors.grey[700] : Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: isDarkMode ? Colors.blue[300] : Colors.blue[600],
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'مشغل الصوتيات قيد التطوير...\nسيتم إضافة قائمة القراء قريباً',
                    style: TextStyle(
                      fontSize: 14,
                      color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
                      fontFamily: 'Amiri',
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),

          // زر الإغلاق
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => Navigator.pop(context),
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    isDarkMode ? Colors.amber[600] : Colors.brown[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'إغلاق',
                style: TextStyle(
                  fontSize: 16,
                  fontFamily: 'Amiri',
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// تحويل الأرقام إلى العربية
  String _convertToArabicNumbers(int number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      final index = int.tryParse(digit);
      return index != null ? arabicNumbers[index] : digit;
    }).join();
  }
}
