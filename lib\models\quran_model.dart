/// نموذج بيانات للسورة القرآنية
class Surah {
  final int number;
  final String name;
  final String englishName;
  final String englishNameTranslation;
  final int numberOfAyahs;
  final String revelationType;
  final int? wordsCount;
  final int? lettersCount;

  Surah({
    required this.number,
    required this.name,
    required this.englishName,
    required this.englishNameTranslation,
    required this.numberOfAyahs,
    required this.revelationType,
    this.wordsCount,
    this.lettersCount,
  });

  /// هل السورة مكية
  bool get isMeccan => revelationType == 'Meccan';

  /// هل السورة مدنية
  bool get isMedinan => revelationType == 'Medinan';

  /// إنشاء نموذج من بيانات JSON
  factory Surah.fromJson(Map<String, dynamic> json) {
    return Surah(
      number: json['number'] ?? 0,
      name: json['name'] ?? '',
      englishName: json['englishName'] ?? '',
      englishNameTranslation: json['englishNameTranslation'] ?? '',
      numberOfAyahs: json['numberOfAyahs'] ?? 0,
      revelationType: json['revelationType'] ?? 'Meccan',
      wordsCount: json['wordsCount'],
      lettersCount: json['lettersCount'],
    );
  }

  /// تحويل النموذج إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'name': name,
      'englishName': englishName,
      'englishNameTranslation': englishNameTranslation,
      'numberOfAyahs': numberOfAyahs,
      'revelationType': revelationType,
      'wordsCount': wordsCount,
      'lettersCount': lettersCount,
    };
  }
}

/// نموذج بيانات للآية القرآنية المحسن
class Ayah {
  final int number;
  final String text;
  final int numberInSurah;
  final int juz;
  final int page;
  final int hizbQuarter;
  final bool sajda;
  final String? englishText;
  final String? surahName;
  final int? surahNumber;

  Ayah({
    required this.number,
    required this.text,
    required this.numberInSurah,
    required this.juz,
    required this.page,
    required this.hizbQuarter,
    required this.sajda,
    this.englishText,
    this.surahName,
    this.surahNumber,
  });

  /// إنشاء نموذج من بيانات JSON
  factory Ayah.fromJson(Map<String, dynamic> json) {
    return Ayah(
      number: json['number'] ?? 0,
      text: json['text'] ?? '',
      numberInSurah: json['numberInSurah'] ?? 0,
      juz: json['juz'] ?? 0,
      page: json['page'] ?? 0,
      hizbQuarter: json['hizbQuarter'] ?? 0,
      sajda: json['sajda'] is bool ? json['sajda'] : false,
      englishText: json['englishText'],
      surahName: json['surahName'],
      surahNumber: json['surahNumber'],
    );
  }

  /// تحويل النموذج إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'text': text,
      'numberInSurah': numberInSurah,
      'juz': juz,
      'page': page,
      'hizbQuarter': hizbQuarter,
      'sajda': sajda,
      'englishText': englishText,
      'surahName': surahName,
      'surahNumber': surahNumber,
    };
  }
}

/// نموذج بيانات للقرآن الكريم كاملاً
class QuranData {
  final List<Surah> surahs;

  QuranData({required this.surahs});

  /// إنشاء نموذج من بيانات JSON
  factory QuranData.fromJson(Map<String, dynamic> json) {
    try {
      final data = json['data'];
      if (data == null) {
        return QuranData(surahs: []);
      }

      final List<dynamic> surahsJson = data['surahs'] ?? [];
      final List<Surah> surahs =
          surahsJson.map((surahJson) => Surah.fromJson(surahJson)).toList();
      return QuranData(surahs: surahs);
    } catch (e) {
      // في حالة حدوث خطأ، نعيد قائمة فارغة
      return QuranData(surahs: []);
    }
  }

  /// تحويل النموذج إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'data': {
        'surahs': surahs.map((surah) => surah.toJson()).toList(),
      },
    };
  }
}

/// نموذج القارئ للصوتيات
class QuranReciter {
  final int id;
  final String arabicName;
  final String englishName;
  final String rewaya;
  final String server;

  QuranReciter({
    required this.id,
    required this.arabicName,
    required this.englishName,
    required this.rewaya,
    required this.server,
  });

  /// الاسم الكامل للقارئ
  String get fullName => arabicName;

  /// إنشاء من JSON
  factory QuranReciter.fromJson(Map<String, dynamic> json) {
    return QuranReciter(
      id: json['id'] ?? 0,
      arabicName: json['arabicName'] ?? '',
      englishName: json['englishName'] ?? '',
      rewaya: json['rewaya'] ?? '',
      server: json['server'] ?? '',
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'arabicName': arabicName,
      'englishName': englishName,
      'rewaya': rewaya,
      'server': server,
    };
  }
}

/// نموذج التسجيل الصوتي
class QuranAudioTrack {
  final int id;
  final int surahNumber;
  final String url;
  final QuranReciter reciter;

  QuranAudioTrack({
    required this.id,
    required this.surahNumber,
    required this.url,
    required this.reciter,
  });

  /// فحص صحة الرابط
  bool get isValidUrl => url.isNotEmpty && Uri.tryParse(url) != null;

  /// إنشاء من JSON
  factory QuranAudioTrack.fromJson(Map<String, dynamic> json) {
    return QuranAudioTrack(
      id: json['id'] ?? 0,
      surahNumber: json['surahNumber'] ?? 0,
      url: json['url'] ?? '',
      reciter: QuranReciter.fromJson(json['reciter'] ?? {}),
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'surahNumber': surahNumber,
      'url': url,
      'reciter': reciter.toJson(),
    };
  }
}

/// نموذج نتيجة البحث في الآيات
class AyahSearchResult {
  final Ayah ayah;
  final String highlightedText;
  final double relevanceScore;

  AyahSearchResult({
    required this.ayah,
    required this.highlightedText,
    required this.relevanceScore,
  });

  /// معلومات مختصرة عن النتيجة
  String get shortInfo =>
      'سورة ${ayah.surahName ?? ayah.surahNumber} - آية ${ayah.number}';
}
