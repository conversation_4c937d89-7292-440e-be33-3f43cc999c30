import 'package:flutter/material.dart';
import '../models/quran_model.dart';
import 'new_quran_api_service.dart';
import 'integrated_audio_player.dart';
import '../utils/logger.dart';

/// مزود بيانات القرآن الكريم - الترحيل الكامل للـ API الجديد
class QuranProvider extends ChangeNotifier {
  final NewQuranApiService _apiService = NewQuranApiService();
  final IntegratedAudioPlayer _audioPlayer = IntegratedAudioPlayer();

  List<Surah> _surahs = [];
  List<Ayah> _currentAyahs = [];
  bool _isLoading = false;
  String _error = '';
  bool _isInitialized = false;

  /// قائمة السور
  List<Surah> get surahs => _surahs;

  /// الآيات الحالية
  List<Ayah> get currentAyahs => _currentAyahs;

  /// حالة التحميل
  bool get isLoading => _isLoading;

  /// رسالة الخطأ
  String get error => _error;

  /// حالة التهيئة
  bool get isInitialized => _isInitialized;

  /// مشغل الصوتيات
  IntegratedAudioPlayer get audioPlayer => _audioPlayer;

  /// تهيئة المزود
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      AppLogger.info('تهيئة مزود القرآن الكريم...');
      
      // تهيئة مشغل الصوتيات
      await _audioPlayer.initialize();
      
      // تحميل السور
      await loadSurahs();
      
      _isInitialized = true;
      AppLogger.info('تم تهيئة مزود القرآن الكريم بنجاح');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة مزود القرآن الكريم: $e');
      _error = 'فشل في التهيئة: $e';
    }
    notifyListeners();
  }

  /// تحميل السور
  Future<void> loadSurahs() async {
    if (_isLoading) return;

    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      AppLogger.info('تحميل السور من API الجديد...');
      _surahs = await _apiService.getAllSurahs();
      AppLogger.info('تم تحميل ${_surahs.length} سورة من API الجديد');
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'فشل في تحميل السور: $e';
      _isLoading = false;
      AppLogger.error('خطأ في تحميل السور: $e');
      notifyListeners();
    }
  }

  /// تحميل آيات سورة محددة
  Future<void> loadAyahs(int surahNumber) async {
    try {
      AppLogger.info('تحميل آيات السورة $surahNumber من API الجديد...');
      _currentAyahs = await _apiService.getSurahAyahs(surahNumber);
      AppLogger.info('تم تحميل ${_currentAyahs.length} آية من API الجديد');
      notifyListeners();
    } catch (e) {
      AppLogger.error('خطأ في تحميل آيات السورة $surahNumber: $e');
      rethrow;
    }
  }

  /// الحصول على القراء المتاحين لسورة
  Future<List<QuranReciter>> getAvailableReciters(int surahNumber) async {
    try {
      AppLogger.info('تحميل القراء المتاحين للسورة $surahNumber...');
      final reciters = await _audioPlayer.getAvailableReciters(surahNumber);
      AppLogger.info('تم تحميل ${reciters.length} قارئ للسورة $surahNumber');
      return reciters;
    } catch (e) {
      AppLogger.error('خطأ في تحميل القراء للسورة $surahNumber: $e');
      return [];
    }
  }

  /// تشغيل سورة
  Future<void> playSurah(int surahNumber, {int? reciterId}) async {
    try {
      AppLogger.info('بدء تشغيل السورة $surahNumber...');
      await _audioPlayer.playSurah(surahNumber, reciterId: reciterId);
      AppLogger.info('تم بدء تشغيل السورة $surahNumber');
    } catch (e) {
      AppLogger.error('خطأ في تشغيل السورة $surahNumber: $e');
      _error = 'فشل في تشغيل السورة: $e';
      notifyListeners();
    }
  }

  /// إيقاف التشغيل
  Future<void> stopPlayback() async {
    try {
      await _audioPlayer.stop();
      AppLogger.info('تم إيقاف التشغيل');
    } catch (e) {
      AppLogger.error('خطأ في إيقاف التشغيل: $e');
    }
  }

  /// البحث في الآيات
  Future<List<AyahSearchResult>> searchAyahs(String query, {int? surahNumber}) async {
    try {
      AppLogger.info('البحث عن: "$query"');
      return await _apiService.searchAyahs(query, surahNumber: surahNumber);
    } catch (e) {
      AppLogger.error('خطأ في البحث: $e');
      return [];
    }
  }

  /// الحصول على آيات السجدة
  Future<List<Ayah>> getSajdaAyahs() async {
    try {
      AppLogger.info('تحميل آيات السجدة...');
      return await _apiService.getSajdaAyahs();
    } catch (e) {
      AppLogger.error('خطأ في تحميل آيات السجدة: $e');
      return [];
    }
  }

  /// الحصول على التسجيلات الصوتية لسورة
  Future<List<QuranAudioTrack>> getSurahAudio(int surahNumber) async {
    try {
      AppLogger.info('تحميل تسجيلات السورة $surahNumber...');
      return await _apiService.getSurahAudio(surahNumber);
    } catch (e) {
      AppLogger.error('خطأ في تحميل التسجيلات: $e');
      return [];
    }
  }

  /// تنظيف الموارد
  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  /// معلومات الخدمة
  Map<String, dynamic> getServiceInfo() {
    return {
      'name': 'Enhanced Quran Provider',
      'version': '2.0',
      'api_service': _apiService.getServiceInfo(),
      'audio_player': _audioPlayer.getPlayerInfo(),
      'surahs_loaded': _surahs.length,
      'current_ayahs': _currentAyahs.length,
      'is_loading': _isLoading,
      'features': 'Complete API Migration, Integrated Audio, Search, Sajda',
    };
  }

  // Dummy methods for compatibility with old code
  bool get isSearching => false;
  String get searchQuery => '';
  List<AyahSearchResult> get searchResults => [];
  bool get isAyahSearch => false;
  dynamic get searchType => null;
  bool get downloadProgress => false;
  int get downloadedSurahsCount => 0;

  void startSearch(dynamic type) {}
  void cancelSearch() {}
  void updateSearchQuery(String query) {}
  Future<List<int>> getDownloadedSurahs() async => [];
  Future<bool> downloadAllSurahs() async => false;
  Future<bool> downloadAllTafsir() async => false;
  Future<void> clearAllDownloads() async {}
  Future<bool> isSurahDownloaded(int surahNumber) async => false;
  Future<bool> isTafsirDownloaded(int surahNumber) async => false;
  Future<List<Ayah>> getOfflineAyahs(int surahNumber) async => [];
  Future<List<Ayah>> getOfflineTafsir(int surahNumber) async => [];
  Future<List<Ayah>> getAyahsWithTafsir(int surahNumber) async => [];
  Future<List<Ayah>> getAyahs(int surahNumber) async => await _apiService.getSurahAyahs(surahNumber);
}
