import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/quran_model.dart';
import '../models/search_type.dart';
import 'quran_service.dart';
import 'enhanced_quran_service.dart';
import 'offline_quran_service.dart';
import '../utils/logger.dart';

/// مزود بيانات القرآن الكريم المحسن
class QuranProvider with ChangeNotifier {
  final QuranService _quranService = QuranService();
  final EnhancedQuranService _enhancedQuranService = EnhancedQuranService();
  final OfflineQuranService _offlineService = OfflineQuranService();

  List<Surah> _surahs = [];
  List<Surah> _filteredSurahs = []; // قائمة السور المفلترة للبحث
  List<AyahSearchResult> _searchResults = []; // نتائج البحث في الآيات
  bool _isLoading = false;
  String _error = '';
  bool _isSearching = false; // حالة البحث
  String _searchQuery = ''; // نص البحث
  SearchType _searchType = SearchType.surahName; // نوع البحث

  // متغيرات للبحث في سورة معينة
  bool _isSearchInSpecificSurah = false; // هل البحث في سورة معينة
  Surah? _selectedSearchSurah; // السورة المختارة للبحث

  // متغيرات لتتبع حالة تحميل السور والتفسير
  bool _isDownloadingSurahs = false;
  bool _isDownloadingTafsir = false;
  double _downloadProgress = 0.0;
  int _downloadedSurahsCount = 0;
  int _totalSurahsToDownload = 0;
  List<int> _downloadedSurahs = [];

  /// قائمة السور
  List<Surah> get surahs =>
      _isSearching && _searchType == SearchType.surahName
          ? _filteredSurahs
          : _surahs;

  /// قائمة السور المفلترة للبحث
  List<Surah> get filteredSurahs => _filteredSurahs;

  /// نتائج البحث في الآيات
  List<AyahSearchResult> get searchResults => _searchResults;

  /// حالة التحميل
  bool get isLoading => _isLoading;

  /// رسالة الخطأ
  String get error => _error;

  /// حالة البحث
  bool get isSearching => _isSearching;

  /// نص البحث
  String get searchQuery => _searchQuery;

  /// نوع البحث
  SearchType get searchType => _searchType;

  /// هل البحث عن آيات
  bool get isAyahSearch => _isSearching && _searchType == SearchType.ayahText;

  /// حالة تحميل السور
  bool get isDownloadingSurahs => _isDownloadingSurahs;

  /// حالة تحميل التفسير
  bool get isDownloadingTafsir => _isDownloadingTafsir;

  /// نسبة التقدم في التحميل
  double get downloadProgress => _downloadProgress;

  /// قائمة السور المحملة
  List<int> get downloadedSurahs => _downloadedSurahs;

  /// عدد السور المحملة
  int get downloadedSurahsCount => _downloadedSurahsCount;

  /// هل البحث في سورة معينة
  bool get isSearchInSpecificSurah => _isSearchInSpecificSurah;

  /// السورة المختارة للبحث
  Surah? get selectedSearchSurah => _selectedSearchSurah;

  /// تعيين حالة البحث في سورة معينة
  void setSearchInSpecificSurah(bool value) {
    _isSearchInSpecificSurah = value;
    notifyListeners();
  }

  /// تعيين السورة المختارة للبحث
  void setSelectedSearchSurah(Surah surah) {
    _selectedSearchSurah = surah;
    notifyListeners();
  }

  /// تصفية السور بالاسم (للبحث الفوري أثناء الكتابة)
  void filterSurahsByName(String query) {
    if (query.isEmpty) {
      _filteredSurahs = [];
    } else {
      _searchBySurahName(query);
    }
    notifyListeners();
  }

  /// تهيئة مزود البيانات
  Future<void> initialize() async {
    await loadSurahs();
    await _loadDownloadedSurahsList();
  }

  /// تحميل قائمة السور المحملة
  Future<void> _loadDownloadedSurahsList() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final downloadedSurahsStrings =
          prefs.getStringList('downloaded_surahs') ?? [];
      _downloadedSurahs =
          downloadedSurahsStrings
              .map((str) => int.tryParse(str) ?? 0)
              .where((surahNum) => surahNum > 0)
              .toList();
      AppLogger.info('تم تحميل قائمة السور المحملة: $_downloadedSurahs');
    } catch (e) {
      AppLogger.error('خطأ في تحميل قائمة السور المحملة: $e');
      _downloadedSurahs = [];
    }
  }

  /// حفظ قائمة السور المحملة
  Future<void> _saveDownloadedSurahsList() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(
        'downloaded_surahs',
        _downloadedSurahs.map((surahNum) => surahNum.toString()).toList(),
      );
      AppLogger.info('تم حفظ قائمة السور المحملة: $_downloadedSurahs');
    } catch (e) {
      AppLogger.error('خطأ في حفظ قائمة السور المحملة: $e');
    }
  }

  /// تحميل قائمة السور المحسنة
  Future<void> loadSurahs() async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      AppLogger.info('🔄 تحميل السور باستخدام الخدمة المحسنة...');

      // محاولة استخدام الخدمة المحسنة أولاً
      try {
        _surahs = await _enhancedQuranService.getAllSurahs();
        AppLogger.info('✅ تم تحميل ${_surahs.length} سورة من الخدمة المحسنة');
      } catch (enhancedError) {
        AppLogger.warning(
          '⚠️ فشل في استخدام الخدمة المحسنة، التبديل للخدمة القديمة: $enhancedError',
        );
        // التبديل للخدمة القديمة كخطة بديلة
        _surahs = await _quranService.getSurahs();
        AppLogger.info('✅ تم تحميل ${_surahs.length} سورة من الخدمة القديمة');
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      AppLogger.error('❌ خطأ في تحميل السور: $e');
      _isLoading = false;
      _error = 'حدث خطأ أثناء تحميل السور: $e';
      notifyListeners();
    }
  }

  /// الحصول على آيات سورة محددة مع الخدمة المحسنة
  Future<List<Ayah>> getAyahs(int surahNumber) async {
    try {
      AppLogger.info(
        '🔄 تحميل آيات السورة $surahNumber باستخدام الخدمة المحسنة...',
      );

      // محاولة استخدام الخدمة المحسنة أولاً
      try {
        final ayahs = await _enhancedQuranService.getSurahAyahs(surahNumber);
        AppLogger.info('✅ تم تحميل ${ayahs.length} آية من الخدمة المحسنة');
        return ayahs;
      } catch (enhancedError) {
        AppLogger.warning(
          '⚠️ فشل في استخدام الخدمة المحسنة، التبديل للخدمة القديمة: $enhancedError',
        );
        // التبديل للخدمة القديمة كخطة بديلة
        final ayahs = await _quranService.getAyahs(surahNumber);
        AppLogger.info('✅ تم تحميل ${ayahs.length} آية من الخدمة القديمة');
        return ayahs;
      }
    } catch (e) {
      AppLogger.error('❌ خطأ في تحميل آيات السورة $surahNumber: $e');
      _error = 'حدث خطأ أثناء تحميل آيات السورة: $e';
      notifyListeners();
      return [];
    }
  }

  /// الحصول على آيات سورة محددة مع التفسير
  Future<List<Ayah>> getAyahsWithTafsir(int surahNumber) async {
    try {
      AppLogger.info('بدء تحميل آيات السورة $surahNumber مع التفسير');

      // تحميل آيات السورة أولاً
      final ayahs = await _quranService.getAyahs(surahNumber);

      if (ayahs.isEmpty) {
        AppLogger.warning('لم يتم تحميل أي آيات للسورة $surahNumber');
        _error = 'لم يتم تحميل أي آيات للسورة';
        notifyListeners();
        return [];
      }

      AppLogger.info(
        'تم تحميل ${ayahs.length} آية للسورة $surahNumber، جاري تحميل التفسير',
      );

      // ثم تحميل التفاسير
      final ayahsWithTafsir = await _quranService.getAyahsWithTafsir(
        surahNumber,
        ayahs,
      );

      AppLogger.info(
        'تم تحميل التفسير لـ ${ayahsWithTafsir.length} آية من السورة $surahNumber',
      );

      return ayahsWithTafsir;
    } catch (e) {
      AppLogger.error('خطأ في تحميل تفسير السورة $surahNumber: $e');
      _error = 'حدث خطأ أثناء تحميل تفسير السورة: $e';
      notifyListeners();

      // في حالة الخطأ، نعيد الآيات بدون تفسير إذا كانت متوفرة
      try {
        final ayahs = await _quranService.getAyahs(surahNumber);
        if (ayahs.isNotEmpty) {
          AppLogger.info(
            'تم إرجاع ${ayahs.length} آية بدون تفسير للسورة $surahNumber',
          );
          return ayahs;
        }
      } catch (innerError) {
        AppLogger.error(
          'خطأ في تحميل الآيات البديلة للسورة $surahNumber: $innerError',
        );
      }

      return [];
    }
  }

  /// بدء البحث
  void startSearch(SearchType type) {
    _searchType = type;
    _isSearching = true;
    _searchQuery = '';
    _filteredSurahs = [];
    _searchResults = [];

    // إذا كان البحث عن آية، نعين السورة الأولى كسورة افتراضية للبحث
    if (type == SearchType.ayahText &&
        _isSearchInSpecificSurah &&
        _selectedSearchSurah == null &&
        _surahs.isNotEmpty) {
      _selectedSearchSurah = _surahs.first;
    }

    // تسجيل حالة البحث للتشخيص
    AppLogger.info(
      'تم بدء البحث: نوع البحث = $type، حالة البحث = $_isSearching، عدد السور = ${_surahs.length}',
    );

    // تأكد من تحديث واجهة المستخدم
    Future.microtask(() {
      notifyListeners();
    });
  }

  /// إلغاء البحث
  void cancelSearch() {
    _isSearching = false;
    _searchQuery = '';
    _filteredSurahs = [];
    _searchResults = [];
    notifyListeners();
  }

  /// تحديث نص البحث
  void updateSearchQuery(String query) {
    _searchQuery = query;

    if (query.isEmpty) {
      _filteredSurahs = [];
      _searchResults = [];
      notifyListeners();
      return;
    }

    // البحث الفوري للسور (بدون حد أدنى للأحرف)
    if (_searchType == SearchType.surahName) {
      _searchBySurahName(query);
      notifyListeners();
    } else {
      // للبحث في الآيات، نحتاج 3 أحرف على الأقل
      if (query.length >= 3) {
        // إلغاء البحث السابق إذا كان قيد التنفيذ
        _searchResults = [];
        _filteredSurahs = [];
        notifyListeners();

        // تنفيذ البحث الجديد
        _searchByAyahText(query);
      } else {
        _searchResults = [];
        _filteredSurahs = [];
        notifyListeners();
      }
    }
  }

  /// البحث في أسماء السور
  void _searchBySurahName(String query) {
    final normalizedQuery = _normalizeArabicText(query.toLowerCase());

    _filteredSurahs =
        _surahs.where((surah) {
          final normalizedName = _normalizeArabicText(surah.name.toLowerCase());
          final normalizedEnglishName = _normalizeArabicText(
            surah.englishName.toLowerCase(),
          );

          // البحث بالاسم العربي أو الإنجليزي أو رقم السورة
          return normalizedName.contains(normalizedQuery) ||
              normalizedEnglishName.contains(normalizedQuery) ||
              surah.number.toString().contains(query);
        }).toList();

    // ترتيب النتائج: السور التي تبدأ بالنص المطلوب أولاً
    _filteredSurahs.sort((a, b) {
      final aNameNormalized = _normalizeArabicText(a.name.toLowerCase());
      final bNameNormalized = _normalizeArabicText(b.name.toLowerCase());

      final aStartsWith = aNameNormalized.startsWith(normalizedQuery);
      final bStartsWith = bNameNormalized.startsWith(normalizedQuery);

      if (aStartsWith && !bStartsWith) return -1;
      if (!aStartsWith && bStartsWith) return 1;

      // إذا كان كلاهما يبدأ بالنص أو لا يبدأ، رتب حسب رقم السورة
      return a.number.compareTo(b.number);
    });
  }

  // تخزين مؤقت للآيات لتسريع البحث
  final Map<int, List<Ayah>> _ayahsCache = {};

  /// البحث في نصوص الآيات - نسخة محسنة للأداء
  void _searchByAyahText(String query) async {
    // تنظيف النص من الفراغات الزائدة
    final cleanQuery = query.trim();

    // تطبيع النص للبحث (إزالة التشكيل والهمزات)
    final normalizedQuery = _normalizeArabicText(cleanQuery.toLowerCase());

    // إظهار حالة التحميل
    _isLoading = true;
    _searchResults = []; // مسح نتائج البحث السابقة
    _filteredSurahs = []; // مسح النتائج السابقة للتوافق مع الواجهة القديمة
    notifyListeners();

    // قائمة مؤقتة لتخزين نتائج البحث
    final List<AyahSearchResult> results = [];

    // قائمة مؤقتة لتخزين السور التي تحتوي على الآيات المطابقة
    final List<Surah> matchingSurahs = [];

    try {
      AppLogger.info('بدء البحث عن: $query في نصوص الآيات');

      // تحديد السور التي سيتم البحث فيها
      List<int> surahsToSearch = [];

      // إذا كان البحث في سورة معينة، نبحث فقط في تلك السورة
      if (_isSearchInSpecificSurah && _selectedSearchSurah != null) {
        surahsToSearch = [_selectedSearchSurah!.number];
        AppLogger.info('البحث في سورة محددة: ${_selectedSearchSurah!.name}');

        await _searchInSurahList(
          surahsToSearch,
          normalizedQuery,
          query,
          results,
          matchingSurahs,
        );
      } else {
        // البحث الشامل في جميع السور
        AppLogger.info('البحث الشامل في جميع السور');

        // البحث في السور القصيرة والمشهورة أولاً للحصول على نتائج سريعة
        final prioritySurahs = [
          1, // الفاتحة
          112, // الإخلاص
          113, // الفلق
          114, // الناس
          36, // يس
          67, // الملك
          55, // الرحمن
          56, // الواقعة
          18, // الكهف
          2, // البقرة (جزء منها)
        ];

        // البحث في السور ذات الأولوية أولاً
        await _searchInSurahList(
          prioritySurahs,
          normalizedQuery,
          query,
          results,
          matchingSurahs,
        );

        // البحث في جميع السور المتبقية للحصول على نتائج شاملة
        if (results.length < 50) {
          // البحث في جميع السور المتبقية
          final remainingSurahNumbers =
              List.generate(114, (i) => i + 1)
                  .where((surahNum) => !prioritySurahs.contains(surahNum))
                  .toList();

          await _searchInSurahList(
            remainingSurahNumbers,
            normalizedQuery,
            query,
            results,
            matchingSurahs,
          );
        }
      }

      AppLogger.info(
        'اكتمل البحث. تم العثور على ${results.length} نتيجة في ${matchingSurahs.length} سورة',
      );
    } finally {
      // إنهاء حالة التحميل وتحديث القائمة النهائية
      _isLoading = false;
      _searchResults = results;
      _filteredSurahs = matchingSurahs;

      AppLogger.info(
        'تحديث نتائج البحث النهائية: ${_searchResults.length} نتيجة، isAyahSearch: ${_isSearching && _searchType == SearchType.ayahText}',
      );

      notifyListeners();
    }
  }

  /// البحث في قائمة من السور
  Future<void> _searchInSurahList(
    List<int> surahNumbers,
    String normalizedQuery,
    String originalQuery,
    List<AyahSearchResult> results,
    List<Surah> matchingSurahs,
  ) async {
    // البحث في كل سورة من القائمة
    for (final surahNumber in surahNumbers) {
      try {
        final surah = _surahs.firstWhere((s) => s.number == surahNumber);

        // تحميل آيات السورة (من التخزين المؤقت إذا كانت متوفرة)
        List<Ayah> ayahs;
        if (_ayahsCache.containsKey(surahNumber)) {
          ayahs = _ayahsCache[surahNumber]!;
        } else {
          ayahs = await _quranService.getAyahs(surahNumber);
          _ayahsCache[surahNumber] = ayahs; // تخزين الآيات للاستخدام المستقبلي
        }

        // البحث في نصوص الآيات
        bool hasMatch = false;
        for (final ayah in ayahs) {
          final normalizedText = _normalizeArabicText(ayah.text.toLowerCase());
          if (normalizedText.contains(normalizedQuery)) {
            hasMatch = true;

            // إنشاء نتيجة بحث مع تحديد مواقع الكلمات المطابقة
            final result = AyahSearchResult.withHighlighting(
              surah,
              ayah,
              originalQuery,
            );

            // إضافة النتيجة إلى القائمة
            results.add(result);
          }
        }

        // إضافة السورة إلى القائمة إذا وجدت تطابق
        if (hasMatch && !matchingSurahs.contains(surah)) {
          matchingSurahs.add(surah);

          // تحديث القوائم فوراً لإظهار النتائج تدريجياً
          _searchResults = List.from(results);
          _filteredSurahs = List.from(matchingSurahs);
          notifyListeners();
        }
      } catch (e) {
        AppLogger.error('خطأ في البحث في آيات السورة رقم $surahNumber: $e');
      }
    }
  }

  /// تحميل جميع السور للاستخدام بدون إنترنت
  Future<bool> downloadAllSurahs() async {
    if (_isDownloadingSurahs) {
      return false; // جاري التحميل بالفعل
    }

    try {
      _isDownloadingSurahs = true;
      _downloadProgress = 0.0;
      _downloadedSurahsCount = 0;
      _totalSurahsToDownload = _surahs.length;
      _error = '';
      notifyListeners();

      AppLogger.info('بدء تحميل جميع السور للاستخدام بدون إنترنت');

      // تحميل كل سورة على حدة
      for (final surah in _surahs) {
        try {
          AppLogger.info('جاري تحميل سورة ${surah.name} (${surah.number})');

          // تحميل آيات السورة
          final ayahs = await _quranService.getAyahs(surah.number);

          if (ayahs.isNotEmpty) {
            // حفظ الآيات محلياً باستخدام الخدمة الجديدة
            await _offlineService.downloadSurah(surah.number);

            if (!_downloadedSurahs.contains(surah.number)) {
              _downloadedSurahs.add(surah.number);
            }
            AppLogger.info(
              'تم تحميل وحفظ سورة ${surah.name} بنجاح (${ayahs.length} آية)',
            );
          } else {
            AppLogger.warning('فشل في تحميل سورة ${surah.name}: لا توجد آيات');
          }

          // تحديث التقدم
          _downloadedSurahsCount++;
          _downloadProgress = _downloadedSurahsCount / _totalSurahsToDownload;
          notifyListeners();
        } catch (e) {
          AppLogger.error('خطأ في تحميل سورة ${surah.name}: $e');
          // نستمر في تحميل باقي السور
        }
      }

      // حفظ قائمة السور المحملة
      await _saveDownloadedSurahsList();

      AppLogger.info(
        'اكتمل تحميل السور: $_downloadedSurahsCount من أصل $_totalSurahsToDownload',
      );

      _isDownloadingSurahs = false;
      notifyListeners();
      return true;
    } catch (e) {
      AppLogger.error('خطأ في تحميل جميع السور: $e');
      _error = 'حدث خطأ أثناء تحميل السور: $e';
      _isDownloadingSurahs = false;
      notifyListeners();
      return false;
    }
  }

  /// تحميل التفسير لجميع السور المحملة
  Future<bool> downloadAllTafsir() async {
    if (_isDownloadingTafsir) {
      return false; // جاري التحميل بالفعل
    }

    try {
      _isDownloadingTafsir = true;
      _downloadProgress = 0.0;
      _downloadedSurahsCount = 0;
      _totalSurahsToDownload = _downloadedSurahs.length;
      _error = '';
      notifyListeners();

      AppLogger.info('بدء تحميل التفسير لجميع السور المحملة');

      // تحميل التفسير لكل سورة محملة
      for (final surahNumber in _downloadedSurahs) {
        try {
          final surah = _surahs.firstWhere((s) => s.number == surahNumber);
          AppLogger.info(
            'جاري تحميل تفسير سورة ${surah.name} (${surah.number})',
          );

          // تحميل آيات السورة أولاً
          final ayahs = await _quranService.getAyahs(surahNumber);

          if (ayahs.isNotEmpty) {
            // تحميل وحفظ التفسير محلياً
            final success = await _offlineService.downloadSurahTafsir(
              surahNumber,
            );

            if (success) {
              AppLogger.info('تم تحميل وحفظ تفسير سورة ${surah.name} بنجاح');
            } else {
              AppLogger.warning('فشل في تحميل تفسير سورة ${surah.name}');
            }
          }

          // تحديث التقدم
          _downloadedSurahsCount++;
          _downloadProgress = _downloadedSurahsCount / _totalSurahsToDownload;
          notifyListeners();
        } catch (e) {
          AppLogger.error('خطأ في تحميل تفسير السورة رقم $surahNumber: $e');
          // نستمر في تحميل باقي التفاسير
        }
      }

      AppLogger.info(
        'اكتمل تحميل التفاسير: $_downloadedSurahsCount من أصل $_totalSurahsToDownload',
      );

      _isDownloadingTafsir = false;
      notifyListeners();
      return true;
    } catch (e) {
      AppLogger.error('خطأ في تحميل التفاسير: $e');
      _error = 'حدث خطأ أثناء تحميل التفاسير: $e';
      _isDownloadingTafsir = false;
      notifyListeners();
      return false;
    }
  }

  /// البحث الشامل المحسن في جميع السور
  Future<List<AyahSearchResult>> searchInAllSurahs(String query) async {
    final normalizedQuery = _normalizeArabicText(query.toLowerCase());
    final List<AyahSearchResult> results = [];

    try {
      AppLogger.info('بدء البحث الشامل المحسن عن: $query');

      // البحث في جميع السور بدون استثناء
      final allSurahs = List.generate(114, (i) => i + 1);

      // تقسيم البحث إلى مجموعات لتحسين الأداء
      const batchSize = 10;
      for (int i = 0; i < allSurahs.length; i += batchSize) {
        final batch = allSurahs.skip(i).take(batchSize).toList();

        await _searchInSurahList(batch, normalizedQuery, query, results, []);

        // إذا وجدنا نتائج كافية، توقف
        if (results.length >= 50) break;
      }

      // ترتيب النتائج حسب الصلة
      results.sort((a, b) {
        // أولوية للمطابقة الكاملة
        final aExactMatch = a.ayah.text.contains(query);
        final bExactMatch = b.ayah.text.contains(query);

        if (aExactMatch && !bExactMatch) return -1;
        if (!aExactMatch && bExactMatch) return 1;

        // ثم حسب رقم السورة
        return a.surah.number.compareTo(b.surah.number);
      });

      AppLogger.info(
        'اكتمل البحث الشامل المحسن. تم العثور على ${results.length} نتيجة',
      );
      return results;
    } catch (e) {
      AppLogger.error('خطأ في البحث الشامل المحسن: $e');
      return [];
    }
  }

  /// البحث في سورة محددة
  Future<List<AyahSearchResult>> searchInSpecificSurah(
    String query,
    Surah surah,
  ) async {
    final normalizedQuery = _normalizeArabicText(query.toLowerCase());
    final List<AyahSearchResult> results = [];

    try {
      AppLogger.info('بدء البحث في سورة ${surah.name} عن: $query');

      // تحميل آيات السورة
      List<Ayah> ayahs;
      if (_ayahsCache.containsKey(surah.number)) {
        ayahs = _ayahsCache[surah.number]!;
      } else {
        ayahs = await _quranService.getAyahs(surah.number);
        _ayahsCache[surah.number] = ayahs;
      }

      // البحث في نصوص الآيات
      for (final ayah in ayahs) {
        final normalizedText = _normalizeArabicText(ayah.text.toLowerCase());
        if (normalizedText.contains(normalizedQuery)) {
          final result = AyahSearchResult.withHighlighting(surah, ayah, query);
          results.add(result);
        }
      }

      AppLogger.info(
        'اكتمل البحث في سورة ${surah.name}. تم العثور على ${results.length} نتيجة',
      );
      return results;
    } catch (e) {
      AppLogger.error('خطأ في البحث في سورة ${surah.name}: $e');
      return [];
    }
  }

  /// تحميل آيات سورة من التخزين المحلي (للاستخدام بدون إنترنت)
  Future<List<Ayah>> getOfflineAyahs(int surahNumber) async {
    try {
      final offlineAyahs = await _offlineService.getOfflineAyahs(surahNumber);
      if (offlineAyahs != null && offlineAyahs.isNotEmpty) {
        AppLogger.info(
          'تم تحميل آيات السورة رقم $surahNumber من التخزين المحلي',
        );
        return offlineAyahs;
      }

      AppLogger.warning('لا توجد آيات محفوظة محلياً للسورة رقم $surahNumber');
      return [];
    } catch (e) {
      AppLogger.error(
        'خطأ في تحميل آيات السورة رقم $surahNumber من التخزين المحلي: $e',
      );
      return [];
    }
  }

  /// تحميل تفسير سورة من التخزين المحلي (للاستخدام بدون إنترنت)
  Future<List<Ayah>> getOfflineTafsir(int surahNumber) async {
    try {
      final offlineTafsir = await _offlineService.getOfflineTafsir(surahNumber);
      if (offlineTafsir != null && offlineTafsir.isNotEmpty) {
        AppLogger.info(
          'تم تحميل تفسير السورة رقم $surahNumber من التخزين المحلي',
        );
        return offlineTafsir;
      }

      AppLogger.warning('لا يوجد تفسير محفوظ محلياً للسورة رقم $surahNumber');
      return [];
    } catch (e) {
      AppLogger.error(
        'خطأ في تحميل تفسير السورة رقم $surahNumber من التخزين المحلي: $e',
      );
      return [];
    }
  }

  /// التحقق من وجود سورة محملة محلياً
  Future<bool> isSurahDownloaded(int surahNumber) async {
    return await _offlineService.isSurahDownloaded(surahNumber);
  }

  /// التحقق من وجود تفسير محمل محلياً
  Future<bool> isTafsirDownloaded(int surahNumber) async {
    return await _offlineService.isTafsirDownloaded(surahNumber);
  }

  /// الحصول على قائمة السور المحملة محلياً
  Future<List<int>> getDownloadedSurahs() async {
    return await _offlineService.getDownloadedSurahs();
  }

  /// الحصول على قائمة التفاسير المحملة محلياً
  Future<List<int>> getDownloadedTafsir() async {
    return await _offlineService.getDownloadedTafsir();
  }

  /// حذف جميع البيانات المحملة محلياً
  Future<void> clearAllDownloads() async {
    await _offlineService.clearAllDownloads();
    _downloadedSurahs.clear();
    notifyListeners();
    AppLogger.info('تم حذف جميع البيانات المحملة محلياً');
  }

  /// تطبيع النص العربي (إزالة التشكيل والهمزات)
  String _normalizeArabicText(String text) {
    // إزالة التشكيل
    final withoutTashkeel = text
        .replaceAll('\u064B', '') // فتحتين
        .replaceAll('\u064C', '') // ضمتين
        .replaceAll('\u064D', '') // كسرتين
        .replaceAll('\u064E', '') // فتحة
        .replaceAll('\u064F', '') // ضمة
        .replaceAll('\u0650', '') // كسرة
        .replaceAll('\u0651', '') // شدة
        .replaceAll('\u0652', '') // سكون
        .replaceAll('\u0653', '') // مدة
        .replaceAll('\u0654', '') // همزة فوق الحرف
        .replaceAll('\u0655', ''); // همزة تحت الحرف

    // توحيد أشكال الهمزات والألف
    return withoutTashkeel
        .replaceAll('أ', 'ا')
        .replaceAll('إ', 'ا')
        .replaceAll('آ', 'ا')
        .replaceAll('ى', 'ي')
        .replaceAll('ة', 'ه');
  }
}
