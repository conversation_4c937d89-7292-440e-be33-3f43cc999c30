/// مساعد لتحويل الأرقام الإنجليزية إلى العربية
class ArabicNumbers {
  /// خريطة تحويل الأرقام من الإنجليزية إلى العربية
  static const Map<String, String> _numberMap = {
    '0': '٠',
    '1': '١',
    '2': '٢',
    '3': '٣',
    '4': '٤',
    '5': '٥',
    '6': '٦',
    '7': '٧',
    '8': '٨',
    '9': '٩',
  };

  /// تحويل رقم إنجليزي إلى عربي
  static String toArabic(dynamic number) {
    if (number == null) return '';
    
    String numberStr = number.toString();
    String result = '';
    
    for (int i = 0; i < numberStr.length; i++) {
      String char = numberStr[i];
      result += _numberMap[char] ?? char;
    }
    
    return result;
  }

  /// تحويل نص يحتوي على أرقام إنجليزية إلى عربية
  static String convertText(String text) {
    if (text.isEmpty) return text;
    
    String result = text;
    _numberMap.forEach((english, arabic) {
      result = result.replaceAll(english, arabic);
    });
    
    return result;
  }

  /// تحويل رقم عربي إلى إنجليزي (للمعالجة الداخلية)
  static String toEnglish(String arabicNumber) {
    if (arabicNumber.isEmpty) return arabicNumber;
    
    String result = arabicNumber;
    _numberMap.forEach((english, arabic) {
      result = result.replaceAll(arabic, english);
    });
    
    return result;
  }

  /// تحقق من كون النص يحتوي على أرقام عربية
  static bool hasArabicNumbers(String text) {
    return _numberMap.values.any((arabic) => text.contains(arabic));
  }

  /// تحقق من كون النص يحتوي على أرقام إنجليزية
  static bool hasEnglishNumbers(String text) {
    return _numberMap.keys.any((english) => text.contains(english));
  }

  /// تنسيق رقم الآية مع النص المناسب
  static String formatAyahNumber(int ayahNumber) {
    return 'الآية ${toArabic(ayahNumber)}';
  }

  /// تنسيق رقم السورة مع النص المناسب
  static String formatSurahNumber(int surahNumber) {
    return 'السورة ${toArabic(surahNumber)}';
  }

  /// تنسيق معلومات السورة (رقم السورة وعدد الآيات)
  static String formatSurahInfo(int surahNumber, int ayahsCount) {
    return 'السورة ${toArabic(surahNumber)} • ${toArabic(ayahsCount)} آية';
  }

  /// تنسيق معلومات الآية (رقم الآية، الجزء، الصفحة)
  static String formatAyahInfo(int ayahNumber, int juz, int page) {
    return 'الآية ${toArabic(ayahNumber)} • الجزء ${toArabic(juz)} • الصفحة ${toArabic(page)}';
  }

  /// تنسيق معلومات مختصرة للآية
  static String formatShortAyahInfo(int juz, int page) {
    return 'الجزء ${toArabic(juz)} • الصفحة ${toArabic(page)}';
  }
}
