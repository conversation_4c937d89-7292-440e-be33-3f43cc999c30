import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/quran_model.dart';
import '../services/quran_provider.dart';
import '../services/integrated_audio_player.dart';

/// واجهة مشغل الصوتيات المتكاملة
class AudioPlayerWidget extends StatefulWidget {
  final int? surahNumber;
  final bool showReciterSelection;
  final bool showFullControls;

  const AudioPlayerWidget({
    super.key,
    this.surahNumber,
    this.showReciterSelection = true,
    this.showFullControls = true,
  });

  @override
  State<AudioPlayerWidget> createState() => _AudioPlayerWidgetState();
}

class _AudioPlayerWidgetState extends State<AudioPlayerWidget> {
  List<QuranReciter> _availableReciters = [];
  QuranReciter? _selectedReciter;
  bool _isLoadingReciters = false;

  @override
  void initState() {
    super.initState();
    if (widget.surahNumber != null && widget.showReciterSelection) {
      _loadAvailableReciters();
    }
  }

  /// تحميل القراء المتاحين
  Future<void> _loadAvailableReciters() async {
    if (widget.surahNumber == null) return;

    setState(() {
      _isLoadingReciters = true;
    });

    try {
      final provider = Provider.of<QuranProvider>(context, listen: false);
      _availableReciters = await provider.getAvailableReciters(widget.surahNumber!);
      
      if (_availableReciters.isNotEmpty) {
        _selectedReciter = _availableReciters.first;
      }
    } catch (e) {
      // معالجة الخطأ
    } finally {
      setState(() {
        _isLoadingReciters = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Consumer<QuranProvider>(
      builder: (context, provider, child) {
        return StreamBuilder<AudioPlayerState>(
          stream: provider.audioPlayer.stateStream,
          builder: (context, stateSnapshot) {
            final playerState = stateSnapshot.data ?? AudioPlayerState.stopped;

            return StreamBuilder<QuranAudioTrack?>(
              stream: provider.audioPlayer.trackStream,
              builder: (context, trackSnapshot) {
                final currentTrack = trackSnapshot.data;

                return Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: isDarkMode ? Colors.grey[800] : Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // معلومات التسجيل الحالي
                      if (currentTrack != null) ...[
                        _buildTrackInfo(currentTrack, isDarkMode),
                        const SizedBox(height: 16),
                      ],

                      // اختيار القارئ
                      if (widget.showReciterSelection && widget.surahNumber != null) ...[
                        _buildReciterSelection(isDarkMode),
                        const SizedBox(height: 16),
                      ],

                      // شريط التقدم
                      if (widget.showFullControls) ...[
                        _buildProgressBar(provider, isDarkMode),
                        const SizedBox(height: 16),
                      ],

                      // أزرار التحكم
                      _buildControlButtons(provider, playerState, isDarkMode),

                      // مستوى الصوت
                      if (widget.showFullControls) ...[
                        const SizedBox(height: 16),
                        _buildVolumeControl(provider, isDarkMode),
                      ],
                    ],
                  ),
                );
              },
            );
          },
        );
      },
    );
  }

  /// بناء معلومات التسجيل
  Widget _buildTrackInfo(QuranAudioTrack track, bool isDarkMode) {
    return Column(
      children: [
        Text(
          track.reciter.fullName,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : Colors.black87,
            fontFamily: 'Amiri',
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          track.reciter.rewaya,
          style: TextStyle(
            fontSize: 14,
            color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
            fontFamily: 'Amiri',
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// بناء اختيار القارئ
  Widget _buildReciterSelection(bool isDarkMode) {
    if (_isLoadingReciters) {
      return const CircularProgressIndicator();
    }

    if (_availableReciters.isEmpty) {
      return Text(
        'لا توجد تسجيلات متاحة',
        style: TextStyle(
          color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
          fontFamily: 'Amiri',
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'اختر القارئ:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : Colors.black87,
            fontFamily: 'Amiri',
          ),
        ),
        const SizedBox(height: 8),
        DropdownButton<QuranReciter>(
          value: _selectedReciter,
          isExpanded: true,
          dropdownColor: isDarkMode ? Colors.grey[800] : Colors.white,
          items: _availableReciters.map((reciter) {
            return DropdownMenuItem<QuranReciter>(
              value: reciter,
              child: Text(
                reciter.fullName,
                style: TextStyle(
                  color: isDarkMode ? Colors.white : Colors.black87,
                  fontFamily: 'Amiri',
                ),
              ),
            );
          }).toList(),
          onChanged: (QuranReciter? newReciter) {
            setState(() {
              _selectedReciter = newReciter;
            });
          },
        ),
      ],
    );
  }

  /// بناء شريط التقدم
  Widget _buildProgressBar(QuranProvider provider, bool isDarkMode) {
    return StreamBuilder<Duration>(
      stream: provider.audioPlayer.positionStream,
      builder: (context, positionSnapshot) {
        final position = positionSnapshot.data ?? Duration.zero;

        return StreamBuilder<Duration>(
          stream: provider.audioPlayer.durationStream,
          builder: (context, durationSnapshot) {
            final duration = durationSnapshot.data ?? Duration.zero;
            final progress = duration.inMilliseconds > 0 
                ? position.inMilliseconds / duration.inMilliseconds 
                : 0.0;

            return Column(
              children: [
                SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    activeTrackColor: isDarkMode ? Colors.amber[600] : Colors.brown[600],
                    inactiveTrackColor: isDarkMode ? Colors.grey[600] : Colors.grey[300],
                    thumbColor: isDarkMode ? Colors.amber[600] : Colors.brown[600],
                  ),
                  child: Slider(
                    value: progress.clamp(0.0, 1.0),
                    onChanged: (value) {
                      final newPosition = Duration(
                        milliseconds: (value * duration.inMilliseconds).round(),
                      );
                      provider.audioPlayer.seek(newPosition);
                    },
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _formatDuration(position),
                      style: TextStyle(
                        fontSize: 12,
                        color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                      ),
                    ),
                    Text(
                      _formatDuration(duration),
                      style: TextStyle(
                        fontSize: 12,
                        color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// بناء أزرار التحكم
  Widget _buildControlButtons(QuranProvider provider, AudioPlayerState state, bool isDarkMode) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // التراجع للخلف
        if (widget.showFullControls)
          IconButton(
            onPressed: () => provider.audioPlayer.seekBackward(),
            icon: Icon(
              Icons.replay_10,
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
          ),

        // تشغيل/إيقاف
        IconButton(
          onPressed: () => _handlePlayPause(provider, state),
          icon: Icon(
            _getPlayPauseIcon(state),
            size: 48,
            color: isDarkMode ? Colors.amber[600] : Colors.brown[600],
          ),
        ),

        // التقدم للأمام
        if (widget.showFullControls)
          IconButton(
            onPressed: () => provider.audioPlayer.seekForward(),
            icon: Icon(
              Icons.forward_10,
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
          ),

        // إيقاف
        IconButton(
          onPressed: () => provider.audioPlayer.stop(),
          icon: Icon(
            Icons.stop,
            color: isDarkMode ? Colors.white : Colors.black87,
          ),
        ),
      ],
    );
  }

  /// بناء تحكم مستوى الصوت
  Widget _buildVolumeControl(QuranProvider provider, bool isDarkMode) {
    return StreamBuilder<double>(
      stream: provider.audioPlayer.volumeStream,
      builder: (context, volumeSnapshot) {
        final volume = volumeSnapshot.data ?? 1.0;

        return Row(
          children: [
            Icon(
              Icons.volume_down,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
            ),
            Expanded(
              child: SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: isDarkMode ? Colors.amber[600] : Colors.brown[600],
                  inactiveTrackColor: isDarkMode ? Colors.grey[600] : Colors.grey[300],
                  thumbColor: isDarkMode ? Colors.amber[600] : Colors.brown[600],
                ),
                child: Slider(
                  value: volume,
                  onChanged: (value) => provider.audioPlayer.setVolume(value),
                ),
              ),
            ),
            Icon(
              Icons.volume_up,
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
            ),
          ],
        );
      },
    );
  }

  /// معالجة تشغيل/إيقاف
  void _handlePlayPause(QuranProvider provider, AudioPlayerState state) {
    switch (state) {
      case AudioPlayerState.stopped:
        if (widget.surahNumber != null) {
          provider.playSurah(
            widget.surahNumber!,
            reciterId: _selectedReciter?.id,
          );
        }
        break;
      case AudioPlayerState.playing:
        provider.audioPlayer.pause();
        break;
      case AudioPlayerState.paused:
        provider.audioPlayer.resume();
        break;
      case AudioPlayerState.loading:
      case AudioPlayerState.error:
        // لا نفعل شيئاً
        break;
    }
  }

  /// الحصول على أيقونة التشغيل/الإيقاف
  IconData _getPlayPauseIcon(AudioPlayerState state) {
    switch (state) {
      case AudioPlayerState.playing:
        return Icons.pause_circle_filled;
      case AudioPlayerState.paused:
      case AudioPlayerState.stopped:
        return Icons.play_circle_filled;
      case AudioPlayerState.loading:
        return Icons.hourglass_empty;
      case AudioPlayerState.error:
        return Icons.error;
    }
  }

  /// تنسيق المدة الزمنية
  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }
}
