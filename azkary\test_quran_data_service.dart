import 'package:flutter/material.dart';
import 'lib/services/quran_data_service.dart';
import 'lib/models/quran_data_models.dart';
import 'lib/utils/logger.dart';

/// اختبار شامل لخدمة QuranDataService الجديدة
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  print('🚀 بدء اختبار خدمة QuranDataService الجديدة...\n');

  final service = QuranDataService();

  // طباعة معلومات API
  service.printApiInfo();
  print('');

  try {
    // اختبار 1: اختبار الاتصال
    print('🔍 اختبار 1: اختبار الاتصال بـ API...');
    final isConnected = await service.testConnection();
    print('النتيجة: ${isConnected ? '✅ متصل' : '❌ غير متصل'}\n');

    if (!isConnected) {
      print('❌ فشل الاتصال بـ API. توقف الاختبار.');
      return;
    }

    // اختبار 2: تحميل جميع السور
    print('🕌 اختبار 2: تحميل جميع السور...');
    final surahs = await service.getAllSurahs();
    print('النتيجة: ✅ تم تحميل ${surahs.length} سورة');

    // عرض أول 5 سور كمثال
    print('أول 5 سور:');
    for (int i = 0; i < 5 && i < surahs.length; i++) {
      final surah = surahs[i];
      print('  ${surah.number}. ${surah.name.ar} (${surah.name.en})');
      print(
        '     آيات: ${surah.versesCount}, كلمات: ${surah.wordsCount}, حروف: ${surah.lettersCount}',
      );
      print('     مكان النزول: ${surah.revelationPlace.ar}');
    }
    print('');

    // اختبار 3: تحميل تفاصيل سورة الفاتحة
    print('📖 اختبار 3: تحميل تفاصيل سورة الفاتحة...');
    final surahDetail = await service.getSurahDetail(1);
    print('النتيجة: ✅ تم تحميل تفاصيل ${surahDetail.name.ar}');
    print('عدد الآيات: ${surahDetail.versesCount}');
    print('عدد التسجيلات الصوتية: ${surahDetail.audio.length}');

    // عرض أول آية
    if (surahDetail.verses.isNotEmpty) {
      final firstVerse = surahDetail.verses.first;
      print('الآية الأولى: ${firstVerse.text.ar}');
      print('الترجمة: ${firstVerse.text.en}');
    }
    print('');

    // اختبار 4: تحميل آيات سورة البقرة
    print('📜 اختبار 4: تحميل آيات سورة البقرة...');
    final verses = await service.getSurahVerses(2);
    print('النتيجة: ✅ تم تحميل ${verses.length} آية من سورة البقرة');

    // عرض آية الكرسي (الآية 255)
    final ayatAlKursi = verses.firstWhere(
      (verse) => verse.number == 255,
      orElse: () => verses.first,
    );
    print('آية الكرسي (جزء): ${ayatAlKursi.text.ar.substring(0, 50)}...');
    print('');

    // اختبار 5: تحميل التسجيلات الصوتية
    print('🎧 اختبار 5: تحميل التسجيلات الصوتية للفاتحة...');
    final audios = await service.getSurahAudio(1);
    print('النتيجة: ✅ تم تحميل ${audios.length} تسجيل صوتي');

    // عرض أول قارئ
    if (audios.isNotEmpty) {
      final firstAudio = audios.first;
      print(
        'القارئ الأول: ${firstAudio.reciter.ar} (${firstAudio.reciter.en})',
      );
      print('الرابط: ${firstAudio.link}');
    }
    print('');

    // اختبار 6: تحميل آيات السجدة
    print('🕋 اختبار 6: تحميل آيات السجدة...');
    final sajdaVerses = await service.getSajdaVerses();
    print('النتيجة: ✅ تم تحميل ${sajdaVerses.length} آية سجدة');

    // عرض أول آية سجدة
    if (sajdaVerses.isNotEmpty) {
      final firstSajda = sajdaVerses.first;
      print('أول آية سجدة: ${firstSajda.text.ar.substring(0, 50)}...');
      print('الصفحة: ${firstSajda.page}, الجزء: ${firstSajda.juz}');
    }
    print('');

    // اختبار 7: تحميل معلومات الصفحة الأولى
    print('📄 اختبار 7: تحميل معلومات الصفحة الأولى...');
    final pageInfo = await service.getPageInfo(pageNumber: 1);
    print('النتيجة: ✅ تم تحميل معلومات الصفحة ${pageInfo.page}');
    print(
      'تبدأ من: السورة ${pageInfo.start.surahNumber} الآية ${pageInfo.start.verse}',
    );
    print(
      'تنتهي في: السورة ${pageInfo.end.surahNumber} الآية ${pageInfo.end.verse}',
    );
    if (pageInfo.image != null) {
      print('رابط الصورة: ${pageInfo.image!.url}');
    }
    print('');

    // اختبار 8: تحميل معلومات صفحات سورة البقرة
    print('📄 اختبار 8: تحميل معلومات صفحات سورة البقرة...');
    final baqarahPageInfo = await service.getPageInfo(surahNumber: 2);
    print('النتيجة: ✅ تم تحميل معلومات صفحات سورة البقرة');
    print('الصفحة: ${baqarahPageInfo.page}');
    print('');

    // ملخص النتائج
    print('🎉 تم إنجاز جميع الاختبارات بنجاح!');
    print('');
    print('📊 ملخص النتائج:');
    print('✅ الاتصال بـ API: يعمل');
    print('✅ تحميل السور: ${surahs.length} سورة');
    print('✅ تفاصيل السور: يعمل');
    print('✅ آيات السور: يعمل');
    print('✅ التسجيلات الصوتية: ${audios.length} تسجيل');
    print('✅ آيات السجدة: ${sajdaVerses.length} آية');
    print('✅ معلومات الصفحات: يعمل');
    print('');
    print('🚀 خدمة QuranDataService جاهزة للاستخدام!');
  } catch (e) {
    print('❌ خطأ في الاختبار: $e');
    print('تفاصيل الخطأ: ${e.toString()}');
  }
}

/// دالة مساعدة لطباعة معلومات السورة
void printSurahInfo(QuranSurah surah) {
  print('السورة ${surah.number}: ${surah.name.ar}');
  print('  الاسم الإنجليزي: ${surah.name.en}');
  print('  النقل الصوتي: ${surah.name.transliteration}');
  print('  مكان النزول: ${surah.revelationPlace.ar}');
  print('  عدد الآيات: ${surah.versesCount}');
  print('  عدد الكلمات: ${surah.wordsCount}');
  print('  عدد الحروف: ${surah.lettersCount}');
}

/// دالة مساعدة لطباعة معلومات الآية
void printVerseInfo(QuranVerse verse) {
  print('الآية ${verse.number}:');
  print('  النص العربي: ${verse.text.ar}');
  print('  الترجمة: ${verse.text.en}');
  print('  الجزء: ${verse.juz}');
  print('  الصفحة: ${verse.page}');
  if (verse.hasSajda) {
    print('  سجدة: نعم');
  }
}

/// دالة مساعدة لطباعة معلومات التسجيل الصوتي
void printAudioInfo(QuranAudio audio) {
  print('التسجيل ${audio.id}:');
  print('  القارئ: ${audio.reciter.ar} (${audio.reciter.en})');
  if (audio.rewaya != null) {
    print('  الرواية: ${audio.rewaya!.ar} (${audio.rewaya!.en})');
  }
  if (audio.server != null) {
    print('  الخادم: ${audio.server}');
  }
  print('  الرابط: ${audio.link}');
}
