import 'azkary/lib/models/quran_model.dart';
import 'azkary/lib/models/quran_data_models.dart';

void main() {
  print('🧪 اختبار إصلاح مشكلة JSON Type Error...\n');

  // اختبار 1: sajda كـ boolean
  testSajdaAsBoolean();
  
  // اختبار 2: sajda كـ Map
  testSajdaAsMap();
  
  // اختبار 3: sajda كـ String
  testSajdaAsString();
  
  // اختبار 4: sajda كـ int
  testSajdaAsInt();
  
  // اختبار 5: Ayah.fromJson مع بيانات مختلطة
  testAyahFromJson();

  print('\n✅ جميع الاختبارات نجحت! تم إصلاح مشكلة JSON Type Error');
}

void testSajdaAsBoolean() {
  print('📝 اختبار 1: sajda كـ boolean');
  try {
    final sajda = QuranSajda.from<PERSON><PERSON>(true);
    print('   ✅ نجح: sajda = ${sajda.sajda}');
    
    final sajda2 = QuranSajda.fromJson(false);
    print('   ✅ نجح: sajda = ${sajda2.sajda}');
  } catch (e) {
    print('   ❌ فشل: $e');
  }
}

void testSajdaAsMap() {
  print('📝 اختبار 2: sajda كـ Map');
  try {
    final sajda = QuranSajda.fromJson({'sajda': true});
    print('   ✅ نجح: sajda = ${sajda.sajda}');
    
    final sajda2 = QuranSajda.fromJson({'sajda': false});
    print('   ✅ نجح: sajda = ${sajda2.sajda}');
  } catch (e) {
    print('   ❌ فشل: $e');
  }
}

void testSajdaAsString() {
  print('📝 اختبار 3: sajda كـ String');
  try {
    final sajda = QuranSajda.fromJson('true');
    print('   ✅ نجح: sajda = ${sajda.sajda}');
    
    final sajda2 = QuranSajda.fromJson('false');
    print('   ✅ نجح: sajda = ${sajda2.sajda}');
  } catch (e) {
    print('   ❌ فشل: $e');
  }
}

void testSajdaAsInt() {
  print('📝 اختبار 4: sajda كـ int');
  try {
    final sajda = QuranSajda.fromJson(1);
    print('   ✅ نجح: sajda = ${sajda.sajda}');
    
    final sajda2 = QuranSajda.fromJson(0);
    print('   ✅ نجح: sajda = ${sajda2.sajda}');
  } catch (e) {
    print('   ❌ فشل: $e');
  }
}

void testAyahFromJson() {
  print('📝 اختبار 5: Ayah.fromJson مع بيانات مختلطة');
  try {
    // اختبار مع sajda كـ boolean
    final ayah1 = Ayah.fromJson({
      'number': 1,
      'text': 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
      'numberInSurah': 1,
      'juz': 1,
      'page': 1,
      'hizbQuarter': 1,
      'sajda': true,
      'isBismillah': false,
    });
    print('   ✅ نجح مع boolean: sajda = ${ayah1.sajda}');
    
    // اختبار مع sajda كـ Map
    final ayah2 = Ayah.fromJson({
      'number': 2,
      'text': 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
      'numberInSurah': 2,
      'juz': 1,
      'page': 1,
      'hizbQuarter': 1,
      'sajda': {'sajda': false},
      'isBismillah': {'isBismillah': true},
    });
    print('   ✅ نجح مع Map: sajda = ${ayah2.sajda}, isBismillah = ${ayah2.isBismillah}');
    
    // اختبار مع sajda كـ String
    final ayah3 = Ayah.fromJson({
      'number': 3,
      'text': 'الرَّحْمَٰنِ الرَّحِيمِ',
      'numberInSurah': 3,
      'juz': 1,
      'page': 1,
      'hizbQuarter': 1,
      'sajda': 'true',
      'isBismillah': 'false',
    });
    print('   ✅ نجح مع String: sajda = ${ayah3.sajda}, isBismillah = ${ayah3.isBismillah}');
    
  } catch (e) {
    print('   ❌ فشل: $e');
  }
}
